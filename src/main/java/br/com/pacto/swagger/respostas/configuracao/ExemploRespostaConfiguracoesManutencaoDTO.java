package br.com.pacto.swagger.respostas.configuracao;

import br.com.pacto.controller.json.base.ConfiguracoesManutencaoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de configurações de manutenção")
public class ExemploRespostaConfiguracoesManutencaoDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as configurações de manutenção do sistema")
    private ConfiguracoesManutencaoDTO content;

    public ConfiguracoesManutencaoDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracoesManutencaoDTO content) {
        this.content = content;
    }
}
