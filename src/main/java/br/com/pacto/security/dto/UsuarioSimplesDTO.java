package br.com.pacto.security.dto;

import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.util.UteisValidacao;

import java.util.*;

/**
 * DTO de usuário simples, um DTO apenas com os dados de acesso de um usuário, objeto bem menor que a entidade
 * {@link br.com.pacto.bean.usuario.Usuario} para permitir criar caches em memória e trafegar menos dados a cada
 * requisição, eliminando a necessidade da IDA ao banco a cada requisição.
 *
 * <AUTHOR>
 * @since 19/07/2018
 */
public class UsuarioSimplesDTO {

    private Integer id;
    private String token;
    private String chave;
    private String permissoesApis;
    private String username;
    private Set<RecursoEnum> recursos;
    private Integer colaboradorId;
    private String provider;
    private Integer empresaAtual;
    private String fotoKey;

    /**
     * Inicializa o array de recursos
     */
    public UsuarioSimplesDTO() {
        this.recursos = new HashSet<>();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Set<RecursoEnum> getRecursos() {
        return recursos;
    }

    public void setRecursos(Set<RecursoEnum> recursos) {
        this.recursos = recursos;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }


    public List<String> getPermissoesApis() {
        return UteisValidacao.emptyString(permissoesApis) ? new ArrayList<>() : Arrays.asList(permissoesApis.split(","));
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public void setPermissoes(String permissoesApis) {
        this.permissoesApis = permissoesApis;
    }

    public Integer getColaboradorId() {
        return colaboradorId;
    }

    public void setColaboradorId(Integer colaboradorId) {
        this.colaboradorId = colaboradorId;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public Integer getEmpresaAtual() {return empresaAtual;}

    public void setEmpresaAtual(Integer empresaAtual) {this.empresaAtual = empresaAtual;}
}
