package br.com.pacto.dao.impl.wod;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.wod.FiltroWodJSON;
import br.com.pacto.bean.wod.Wod;
import br.com.pacto.bean.wod.WodAvaliacao;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.wod.WodAvaliacaoDao;
import br.com.pacto.dao.intf.wod.WodDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.WodExcecoes;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Rafael on 20/07/2016.
 */
@Repository
public class WodAvaliacaoDaoImpl extends DaoGenericoImpl<WodAvaliacao, Integer> implements WodAvaliacaoDao {

}
