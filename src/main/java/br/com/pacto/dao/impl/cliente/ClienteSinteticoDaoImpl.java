/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.cliente;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.bi.IndicadorAvaliacaoFisicaEnum;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.pessoa.Pessoa;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.aluno.FiltroAlunoJSON;
import br.com.pacto.controller.json.aluno.SituacaoAlunoEnum;
import br.com.pacto.controller.json.aluno.SituacaoContratoZWEnum;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.dao.intf.professor.ProfessorSinteticoDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AlunoExcecoes;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.AgendadoJSON;
import br.com.pacto.util.json.ClienteSintenticoJson;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.persistence.Query;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.Normalizer;
import java.util.*;

/**
 * <AUTHOR>
 */
@Repository
public class ClienteSinteticoDaoImpl extends DaoGenericoImpl<ClienteSintetico, Integer> implements ClienteSinteticoDao {


    private static final int MAXIMO_CLIENTE_SINTETICO_CONSULTAR = 50;
    @Autowired
    ProfessorSinteticoDao professorSinteticoDao;
    @Override
    @SuppressWarnings("unchecked")
    public List<ClienteSintetico> findByEmpresaProfessorNomesMatriculasIn(String contexto, Integer empresaZW,
                                                                          Integer professor, List<String> nomes,
                                                                          List<Integer> matriculas, Integer maxResults) throws Exception {

        final Boolean matriculasVazias = CollectionUtils.isEmpty(matriculas);
        final Boolean nomesVazios = CollectionUtils.isEmpty(nomes);
        if (matriculasVazias && nomesVazios) {
            throw new ServiceException("Os nomes e as matriculas não podem ser nulos simultaneamente.");
        }

        final Query query = getEntityManager(contexto)
                .createQuery(montarSQL(empresaZW, professor, nomes, matriculas, nomesVazios, matriculasVazias));

        if (maxResults != null && maxResults != 0) {
            query.setMaxResults(maxResults);
        }

        List<ClienteSintetico> lista= (List<ClienteSintetico>) query.getResultList();
        for(ClienteSintetico c : lista) {
            for (Usuario u: c.getUsuarios()) {
                if(c.getFotoKeyApp() == null && u.getFotoKeyApp() != null) {
                    c.setFotoKeyApp(u.getFotoKeyApp());
                    break;
                }
            }
        }

        return lista;
    }

    @Override
    public List<ClienteSintetico> consultarClientesSinteticos(String ctx, FiltroAlunoJSON filtros, String niveisSelecionados, String situacoesEnumSelecionados, String statusClienteEnumSelecionado,
                                                              PaginadorDTO paginadorDTO, Integer empresaId, Integer codigoProfessor, Integer empresaZw) throws ServiceException {
        getCurrentSession(ctx).clear();
        int maxResults = MAXIMO_CLIENTE_SINTETICO_CONSULTAR;
        int indiceInicial=0;
        Map<String, Object> param = new HashMap<String, Object>();
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_CLIENTE_SINTETICO_CONSULTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        hql.append("SELECT obj FROM ClienteSintetico obj ");
        if(!UteisValidacao.emptyNumber(empresaId)) {
            where.append(" obj.empresa = :empresaId ");
            param.put("empresaId", empresaId);
        }

        String colaboradorIds = "";
        if (!UteisValidacao.emptyList(filtros.getColaboradorIds())) {
            if (where.length() > 0) {
                where.append(" AND ");
            }
            for (Integer coladoradorId : filtros.getColaboradorIds()) {
                colaboradorIds += ","+coladoradorId;
            }
            colaboradorIds = colaboradorIds.replaceFirst(",", "");
            where.append(" obj.professorSintetico.codigoColaborador in (").append(colaboradorIds).append(")");
        }

        if (!StringUtils.isBlank(niveisSelecionados)) {
            if(where.length() > 0) {
                where.append(" AND ");
            }
            where.append(" obj.nivelAluno.codigo in (").append(niveisSelecionados).append(") ");
        }
        if ((filtros.getNome()) && (!filtros.getParametro().trim().equals(""))) {
            if(where.length() > 0) {
                where.append(" AND ");
            }
            try {
                if (filtros.getParametro() != null && (filtros.getParametro().replaceAll("[^0-9]", "").length() == 10 ||
                        filtros.getParametro().replaceAll("[^0-9]", "").length() == 11)) {
                    where.append(" codigoAcesso = :codAcesso ");
                    param.put("codAcesso", filtros.getParametro().replaceAll("[^0-9]", ""));
                } else {
                    Integer matricula = Integer.valueOf(filtros.getParametro());
                    where.append(" matricula = :matricula ");
                    param.put("matricula", matricula);
                }
            } catch (NumberFormatException ne) {
                where.append(" ( unaccent(upper(obj.nome)) LIKE CONCAT('%',:nome,'%') ");
                param.put("nome", removerCaracteresEspeciais(filtros.getParametro()).toUpperCase());
            }
        }
        if ((filtros.getEmail()) && (!filtros.getParametro().trim().equals(""))) {
            if(where.length() > 0 && !filtros.getNome()) {
                where.append(" AND ");
            } else if (where.length() > 0 && filtros.getNome()) {
                where.append(" or ");
            }
            where.append(" upper(obj.email) LIKE CONCAT('%',:email,'%') )");
            param.put("email", removerCaracteresEspeciais(filtros.getParametro()).toUpperCase());
        } else if ((filtros.getNome()) && (!filtros.getParametro().trim().equals("")) && !filtros.getEmail()){
            where.append(") ");
        }

        if (!StringUtils.isBlank(situacoesEnumSelecionados)) {
            if (where.length() > 0) {
                where.append(" AND ");
            }
            where.append(" ( ");
            if(situacoesEnumSelecionados.contains("and")){
                String sql = "(";
                String [] filterValues = situacoesEnumSelecionados.split(",");

                for (int i = 0; i < filterValues.length; i++) {
                    if(filterValues[i].contains("and")){
                        String [] composition = filterValues[i].split(" and "); // Example:  AT and NO
                        sql += "(obj.situacao = "+composition[0]+"' AND ";
                        sql += "obj.situacaoContrato = '"+composition[1]+")";
                    } else {
                        String filter = filterValues[i].replaceAll("'", "");
                        if(SituacaoContratoZWEnum.getInstance(filter)!=null)
                            sql += "obj.situacaoContrato in ("+ filterValues[i]+")";
                        else if(SituacaoAlunoEnum.getInstance(filter)!=null){
                            sql += "obj.situacao in ("+ filterValues[i]+")";
                        }
                    }
                    if(i<filterValues.length-1){
                        sql += " or ";
                    }
                }
                where.append(sql + ")");
            } else {
                String [] filterValues = situacoesEnumSelecionados.split(",");
                for (int i = 0; i < filterValues.length; i++) {
                    String filter = filterValues[i].replaceAll("'", "");
                    if(SituacaoContratoZWEnum.getInstance(filter)!=null && !SuperControle.independente(ctx)){
                        where.append("obj.situacaoContrato in ('"+ filter+"')");
                    } else if(SituacaoAlunoEnum.getInstance(filter)!=null){
                        where.append("obj.situacao in ('"+ filter+"')");
                    }
                    if(i<filterValues.length-1){
                        where.append(" or ");
                    }
                }
            }
            where.append(" ) ");
        }

        if (!StringUtils.isBlank(statusClienteEnumSelecionado)) {
            if(where.length() > 0) {
                where.append(" AND ");
            }

            Date inicio = Calendario.hoje();
            ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema duracaoNaAcademia = css.consultarPorTipo(ctx, ConfiguracoesEnum.DURACAO_ALUNO_NA_ACADEMIA);
            switch (statusClienteEnumSelecionado) {
                case "'MINHA_CARTEIRA'":
                    if (!UteisValidacao.emptyString(colaboradorIds)) {
                        where.append(" (obj.ativo is true or obj.ativo is null) ");
                    } else {
                        where.append(" professorsintetico_codigo = :professorSinteticoCodigo ");
                        where.append(" AND (obj.ativo is true or obj.ativo is null) ");
                        param.put("professorSinteticoCodigo", codigoProfessor);
                    }
                    break;
                case "'NA_ACADEMIA'":
                    where.append(" obj.codigo IN (SELECT sta.usuario.cliente.codigo FROM StatusPessoa sta  WHERE sta.dataInicioEvento between :inicio AND :fim AND sta.dataFimEvento IS NULL AND sta.empresa.codZW = :empresa) ");
                    param.put("empresa", empresaZw);
                    param.put("inicio", Uteis.somarCampoData(inicio, Calendar.MINUTE, -duracaoNaAcademia.getValorAsInteger()));
                    param.put("fim", Calendario.getDataComHora(inicio, "23:59:59"));
                    break;
                case "'NA_ACADEMIA_SEM_TREINO'":
                    where.append(" obj.codigo IN (SELECT sta.usuario.cliente.codigo FROM StatusPessoa sta  WHERE sta.dataInicioEvento between :inicio AND :fim AND sta.dataFimEvento IS NULL AND sta.empresa.codZW = :empresa) ");
                    where.append(" AND NOT EXISTS (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE obj.codigo = p.cliente.codigo) ");
                    param.put("empresa", empresaZw);
                    param.put("inicio", Uteis.somarCampoData(inicio, Calendar.MINUTE, -duracaoNaAcademia.getValorAsInteger()));
                    param.put("fim", Calendario.getDataComHora(inicio, "23:59:59"));
                    break;
                case "'NA_ACADEMIA_COM_TREINO_A_VENCER'":
                    where.append(" obj.codigo IN (SELECT sta.usuario.cliente.codigo FROM StatusPessoa sta  WHERE sta.dataInicioEvento between :inicio AND :fim AND sta.dataFimEvento IS NULL AND sta.empresa.codZW = :empresa) ");
                    where.append(" AND obj.codigo IN (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE p.programaTreinoRenovado is null and p.dataTerminoPrevisto between :antes and :depois ");
                    where.append(" AND NOT exists (SELECT pt.codigo FROM ProgramaTreino pt WHERE pt.dataInicio >= p.dataTerminoPrevisto and obj.codigo = pt.cliente.codigo)) ");
                    param.put("empresa", empresaZw);
                    param.put("inicio", Uteis.somarCampoData(inicio, Calendar.MINUTE, -duracaoNaAcademia.getValorAsInteger()));
                    param.put("fim", Calendario.getDataComHora(inicio, "23:59:59"));
                    ConfiguracaoSistema diasAntes = css.consultarPorTipo(ctx, ConfiguracoesEnum.DIAS_ANTES_VENCIMENTO);
                    Date depois = Uteis.somarCampoData(Calendario.hoje(), Calendar.DAY_OF_MONTH, diasAntes.getValorAsInteger());
                    param.put("antes", Calendario.getDataComHoraZerada(Calendario.hoje()));
                    param.put("depois", depois);
                    break;
                case "'NA_ACADEMIA_COM_TREINO_VENCIDO'":
                    where.append(" obj.codigo IN (SELECT sta.usuario.cliente.codigo FROM StatusPessoa sta  WHERE sta.dataInicioEvento between :inicio AND :fim AND sta.dataFimEvento IS null AND sta.empresa.codZW = :empresa) ");
                    where.append(" AND obj.codigo IN (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE CURRENT_DATE > p.dataTerminoPrevisto AND p.cliente.codigo IS NOT NULL) ");
                    where.append(" AND obj.codigo NOT IN (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE CURRENT_DATE <= p.dataTerminoPrevisto AND p.cliente.codigo IS NOT NULL) ");
                    where.append(" AND (obj.ativo is true or obj.ativo is null) ");
                    param.put("empresa", empresaZw);
                    param.put("inicio", Uteis.somarCampoData(inicio, Calendar.MINUTE, -duracaoNaAcademia.getValorAsInteger()));
                    param.put("fim", Calendario.getDataComHora(inicio, "23:59:59"));
                    break;
                case "'NA_ACADEMIA_DA_MINHA_CARTEIRA'":
                    where.append(" obj.codigo IN (SELECT sta.usuario.cliente.codigo FROM StatusPessoa sta WHERE sta.dataInicioEvento between current_date AND cast('2021-02-04' as date) AND sta.dataFimEvento IS null AND sta.empresa.codZW = :empresa) ");
                    where.append(" AND obj.professorSintetico.codigo = :professorSinteticoCodigo ");
                    param.put("empresa", empresaZw);
                    param.put("professorSinteticoCodigo", codigoProfessor);
                    break;
                case "'TREINO_VENCIDO'":
                    where.append(" obj.codigo IN (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE CURRENT_DATE > p.dataTerminoPrevisto AND p.cliente.codigo IS NOT NULL) ");
                    where.append(" AND obj.codigo NOT IN (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE CURRENT_DATE <= p.dataTerminoPrevisto AND p.cliente.codigo IS NOT NULL) ");
                    where.append(" AND (obj.ativo is true or obj.ativo is null) ");
                    break;
                case "'SEM_TREINO'":
                    where.append(" NOT EXISTS (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE obj.codigo = p.cliente.codigo)");
                    where.append(" AND (obj.ativo is true or obj.ativo is null) ");
                    break;
            }
        }

        if (where.length() > 0){
            where.insert(0, " where ");
            hql.append(where.toString());
        }
        hql.append(paginadorDTO.getSQLOrderByUse());
        List<ClienteSintetico> lista;
        try {
            if (paginadorDTO != null) {
                Long qtdeElemento = countWithParam(ctx, "codigo", where, param).longValue();
                paginadorDTO.setQuantidadeTotalElementos(qtdeElemento);
                if(qtdeElemento < indiceInicial){
                    indiceInicial = 0;
                }
            }
            lista = findByParam(ctx, hql.toString(), param, maxResults,indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_BUSCAR_ALUNOS, e);
        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return lista;
    }

    public static String removerCaracteresEspeciais(String texto) {
        texto = Normalizer.normalize(texto, Normalizer.Form.NFD);
        texto = texto.replaceAll("[^\\p{ASCII}]", "");
        return texto;
    }

    @Override
    public boolean consultarClientesSinteticosNiveis(Integer id, String ctx) throws ServiceException{
        try {
            StringBuilder hql = new StringBuilder();
            StringBuilder where = new StringBuilder();
            boolean vinculoAluno = false;
            hql.append("SELECT obj FROM ClienteSintetico obj ");
            if (id != null) {
                if (where.length() > 0) {
                    where.append(" AND ");
                }
                where.append(" obj.nivelAluno.codigo in (").append(id).append(") ");
            }
            if (where.length() > 0){
                where.insert(0, " where ");
                hql.append(where.toString());
            }
            Query q = getEntityManager(ctx).createQuery(hql.toString());

            List<ClienteSintetico> lista = q.getResultList();
            if(lista.size() > 0 ){

                vinculoAluno = true;
            }

            return vinculoAluno;

        }catch (Exception e){
            e.printStackTrace();
            return false;
        }

    }

    @Override
    public ClienteSintetico obterPorId(String ctx, Integer id) throws ServiceException {
        try {
            getCurrentSession(ctx).clear();
            return findById(ctx, id);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    private String montarSQL(Integer empresaZW, Integer professor, List<String> nomes, List<Integer> matriculas,
                             Boolean nomesVazios, Boolean matriculasVazias) {

        final StringBuilder SQL = new StringBuilder("SELECT cliente FROM ").append(ClienteSintetico.class.getSimpleName())
                .append(" cliente WHERE 1 = 1 ");

        if (empresaZW != null && empresaZW != 0) {
            SQL.append("AND empresa = ").append(empresaZW);
        }

        if (professor != null && professor != 0) {
            SQL.append("AND professorsintetico_codigo = ").append(professor);
        }

        final String nomesIn = "LOWER(nome) IN (" + StringUtils.join(nomes, ",").toLowerCase() + ") ";
        final String matriculasIn = "matricula IN (" + StringUtils.join(matriculas, ",") + ") ";

        if (!nomesVazios && matriculasVazias) {
            SQL.append("AND ").append(nomesIn);
        } else if (nomesVazios && !matriculasVazias) {
            SQL.append("AND ").append(matriculasIn);
        } else if (!nomesVazios) {
            SQL.append("AND ").append(nomesIn).append(" OR ").append(matriculasIn);
        }

        return SQL.toString();
    }

    public List<ClienteSintetico> consultarClientesSinteticosIntegracaoFera(String ctx) throws Exception {
        try {
            final StringBuilder SQL = new StringBuilder("SELECT cliente FROM ").append(ClienteSintetico.class.getSimpleName())
                    .append(" cliente WHERE 1 = 1 ");
            Query q = getEntityManager(ctx).createQuery(SQL.toString());

            List<ClienteSintetico> lista = q.getResultList();
            return lista;
        }catch (Exception e){
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    @Override
    public List<ClienteSintetico> consultarResultadoEvolucaoBiPorIndicadorAvaliacaoFisica(
            String ctx,
            IndicadorAvaliacaoFisicaEnum indicadorAvaliacaoFisica,
            Integer empresaId,
            Integer codigoProfessor,
            String parametro,
            PaginadorDTO paginadorDTO) throws ServiceException {

        int maxResults = MAXIMO_CLIENTE_SINTETICO_CONSULTAR;

        int indiceInicial=0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_CLIENTE_SINTETICO_CONSULTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
        }

        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> param = new HashMap<>();

        hql.append("SELECT obj FROM ClienteSintetico obj ");
        where.append(" WHERE obj.empresa = :empresaId");
        param.put("empresaId", empresaId);
        //TipoBIAvaliacaoEnum.ALUNOS_SEM_VINCULO o integer maxvalue é porque ele pesquisa alunos sem vinculo e o -1 é quando pesquisa todos os professores inativos
        if (!UteisValidacao.emptyNumber(codigoProfessor) && codigoProfessor != Integer.MAX_VALUE && codigoProfessor != Integer.MAX_VALUE-1) {
            where.append(" AND obj.professorSintetico.codigo = :codigoProfessor");
            param.put("codigoProfessor", codigoProfessor);
        }
        if (codigoProfessor == Integer.MAX_VALUE-1){
            where.append(" AND obj.professorSintetico.ativo = false");
        }
        if (StringUtils.isNotBlank(parametro)) {
            int matricula = 0;
            try {
                matricula = Integer.parseInt(parametro);
            } catch (Exception ex) {
                matricula = 0;
            }
            where.append(" AND upper(obj.nome) like CONCAT('%',:nome,'%') ");
            param.put("nome", parametro.toUpperCase());
            if (matricula > 0) {
                where.append(" or obj.matricula = :matricula ");
                param.put("matricula", matricula);
            }
        }
        if (IndicadorAvaliacaoFisicaEnum.IND_AF_PERDERAM_PERCENTUAL.equals(indicadorAvaliacaoFisica)) {
            where.append(" AND obj.percentualGorduraInicio > obj.percentualGorduraAtual");
            if (paginadorDTO != null) {
                if (paginadorDTO.getSort() != null && paginadorDTO.getSort().contains("diff")) {
                    paginadorDTO.setSort(paginadorDTO.getSort().replace("diff", "(obj.percentualGorduraInicio - obj.percentualGorduraAtual)"));
                }
            }
        } else if (IndicadorAvaliacaoFisicaEnum.IND_AF_PERDERAM_PESO.equals(indicadorAvaliacaoFisica)) {
            where.append(" AND obj.pesoInicio > obj.pesoAtual ");
            if (paginadorDTO != null) {
                if (paginadorDTO.getSort() != null && paginadorDTO.getSort().contains("diff")) {
                    paginadorDTO.setSort(paginadorDTO.getSort().replace("diff", "(obj.pesoInicio - obj.pesoAtual)"));
                }
            }
        } else if (IndicadorAvaliacaoFisicaEnum.IND_AF_GANHARAM_MASSA.equals(indicadorAvaliacaoFisica)) {
            where.append(" AND obj.massaMagraInicio < obj.massaMagraAtual ");
            if (paginadorDTO != null) {
                if (paginadorDTO.getSort() != null && paginadorDTO.getSort().contains("diff")) {
                    paginadorDTO.setSort(paginadorDTO.getSort().replace("diff", "(obj.massaMagraInicio - obj.massaMagraAtual)"));
                }
            }
        }

        hql.append(where.toString());
        List<ClienteSintetico> lista;
        try {
            if (paginadorDTO != null) {
                hql.append(paginadorDTO.getSQLOrderByUse());
                Long qtdeElemento = countWithParam(ctx, "codigo", where, param).longValue();
                paginadorDTO.setQuantidadeTotalElementos(qtdeElemento);
                if(qtdeElemento < indiceInicial){
                    indiceInicial = 0;
                }
                paginadorDTO.setSize((long) maxResults);
                paginadorDTO.setPage((long) indiceInicial);
                lista = findByParam(ctx, hql.toString(), param, maxResults,indiceInicial);
            } else {
                lista = findByParam(ctx, hql.toString(), param, 0,0);
            }
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_CONSULTAR_RESULTADO_EVOLUCAO_BI_AVALIACAO, e);
        }

        return lista;
    }

    @Override
    public ArrayList<Map.Entry<String, Integer>> countResultadosAlunos(
            String ctx,
            IndicadorAvaliacaoFisicaEnum indicadorAvaliacaoFisica,
            Integer empresaId,
            Integer codigoProfessor,
            String parametro,
            PaginadorDTO paginadorDTO) throws ServiceException {

        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();

        if (!UteisValidacao.emptyNumber(codigoProfessor) && codigoProfessor != Integer.MAX_VALUE && codigoProfessor != Integer.MAX_VALUE-1) {
            where.append(" AND obj.professorsintetico_codigo = "+codigoProfessor+" ");
        }
        if (codigoProfessor == Integer.MAX_VALUE-1){
            where.append(" AND obj.ativo = false");
        }
        if (StringUtils.isNotBlank(parametro)) {
            int matricula = 0;
            try {
                matricula = Integer.parseInt(parametro);
            } catch (Exception ex) {
                matricula = 0;
            }
            where.append(" AND upper(obj.nome) like CONCAT('%',"+parametro.toUpperCase()+",'%') ");

            if (matricula > 0) {
                where.append(" or obj.matricula = "+matricula+" ");
            }
        }
        hql.append("select (SELECT count (*) FROM ClienteSintetico obj  WHERE obj.empresa = "+empresaId+" AND obj.massaMagraInicio < obj.massaMagraAtual "+where+" ) as ganharammassamagra,");
        hql.append("(SELECT count (*) FROM ClienteSintetico obj  WHERE obj.empresa = "+empresaId+" AND obj.percentualGorduraInicio > obj.percentualGorduraAtual "+where+ ") as perderamgordura, " );
        hql.append("(SELECT count (*) FROM ClienteSintetico obj  WHERE obj.empresa = "+empresaId+" AND obj.pesoInicio > obj.pesoAtual "+where+ ") as perderampeso ");

        ArrayList<Map.Entry<String, Integer>> listaResultado = new ArrayList<>();

        try (ResultSet result = createStatement(ctx, hql.toString())) {
            while (result.next()) {
                listaResultado.add(
                    new AbstractMap.SimpleEntry<>("ganharammassamagra", result.getInt("ganharammassamagra"))
                );
                listaResultado.add(
                        new AbstractMap.SimpleEntry<>("perderamgordura", result.getInt("perderamgordura"))
                );
                listaResultado.add(
                        new AbstractMap.SimpleEntry<>("perderampeso", result.getInt("perderampeso"))
                );
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return listaResultado;
    }

    @Override
    public List<ClienteSintetico> consultarAlunosSemUsuario(String ctx) throws ServiceException {
        try {
            StringBuilder hql = new StringBuilder();
            hql.append("SELECT obj FROM ClienteSintetico obj ");
            hql.append("WHERE obj.codigo not in (SELECT us.cliente.codigo FROM Usuario us WHERE us.cliente is not null)");

            return findByParam(ctx, hql.toString(), new HashMap<>(), 0);
        } catch (Exception ex) {
            throw new ServiceException(AlunoExcecoes.ERRO_BUSCAR_ALUNOS, ex);
        }
    }

    @Override
    public List<ClienteSintetico> consultarClientesNaoInclusoClientePesquisa(String ctx) throws ServiceException {
        try {
            StringBuilder SQL = new StringBuilder("select c from ClienteSintetico c where c.matricula not in (select matricula from ClientePesquisa)");
            Query query = getEntityManager(ctx).createQuery(SQL.toString());

            return query.getResultList();
        } catch (Exception err) {
            throw new ServiceException(AlunoExcecoes.ERRO_BUSCAR_ALUNOS, err);
        }
    }

    public ClienteSintetico consultarSimplificadoPorCodigoCliente(final String ctx, final Integer id) throws ServiceException{
        try {
            try (ResultSet rs = createStatement(ctx, "select p.fotokey, c.matricula, c.nome from clientesintetico c " +
                    " inner join pessoa p on p.codigo = c.pessoa_codigo " +
                    " where c.codigocliente = " + id)) {
                if (rs.next()) {
                    ClienteSintetico clienteSintetico = new ClienteSintetico();
                    clienteSintetico.setMatricula(rs.getInt("matricula"));
                    clienteSintetico.setNome(rs.getString("nome"));
                    clienteSintetico.setPessoa(new Pessoa());
                    clienteSintetico.getPessoa().setFotoKey(rs.getString("fotokey"));
                    return clienteSintetico;
                }
            }
            return null;
        }catch (Exception err){
            throw new ServiceException(AlunoExcecoes.ERRO_BUSCAR_ALUNOS, err);
        }
    }

    public List<ClienteSintenticoJson> findClienteSintetico(String ctx, List<AgendadoJSON> alunos) throws Exception {
        if (alunos == null || alunos.isEmpty()) {
            return new ArrayList<ClienteSintenticoJson>();
        }
        StringBuilder  sql = new StringBuilder();
        sql.append(" select c.codigo, c.codigocliente, c.codigopessoa,c.nome,c.datanascimento,c.matricula, \n");
        sql.append(" p.fotokey, idade, telefones, email, colaboradores, situacao, situacaocontrato, empresa, codigocontrato \n");
        sql.append(" from clientesintetico c \n");
        sql.append(" inner join pessoa p on p.codigo = c.pessoa_codigo \n");
        sql.append(" where c.codigocliente IN(");
        String codigosAlunos = "";
        for (AgendadoJSON aluno : alunos) {
            codigosAlunos += "," + aluno.getCodigoCliente();
        }
        sql.append(codigosAlunos.replaceFirst(",","")).append(")");

        List<ClienteSintenticoJson> result = new ArrayList<ClienteSintenticoJson>();
        try (ResultSet set = createStatement(ctx, sql.toString())) {
            while (set.next()) {
                ClienteSintenticoJson cli = new ClienteSintenticoJson();
                cli.setCodigo(set.getInt("codigo"));
                cli.setCodigoContrato(set.getInt("codigocontrato"));
                cli.setCodigoPessoaZW(set.getInt("codigopessoa"));
                cli.setCodigoClienteZW(set.getInt("codigocliente"));
                cli.setMatricula(set.getInt("matricula"));
                cli.setNome(set.getString("nome"));
                cli.setDataNascimento(set.getDate("datanascimento"));
                cli.setIdade(set.getInt("idade"));
                cli.setColaboradores(set.getString("colaboradores"));
                cli.setSituacao(set.getString("situacao"));
                cli.setSituacaoContrato(set.getString("situacaocontrato"));
                cli.setEmpresa(set.getInt("empresa"));
                cli.setEmail(set.getString("email"));
                cli.setTelefones(set.getString("telefones"));
                cli.setUrlFoto(Uteis.getPaintFotoDaNuvem(set.getString("fotokey")));
                result.add(cli);
            }
        }

        return result;
    }

    public List consultarMatriculaClientesStatusNivel(String ctx, String statusClienteEnumSelecionado, String niveisSelecionados,
                                                      Integer codigoProfessor, Integer empresaZw, List<Integer> colaboradorZw) throws Exception {
        getCurrentSession(ctx).clear();

//        Empresa empresaTR = null;
//        if (!UteisValidacao.emptyNumber(empresaZw)) {
//            EmpresaService empresaService = (EmpresaService) UtilContext.getBean(EmpresaService.class);
//            empresaTR = empresaService.obterPorIdZW(ctx, empresaZw);
//        }

        Map<String, Object> param = new HashMap<String, Object>();
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        hql.append("SELECT obj.matricula FROM ClienteSintetico obj ");
//        if(empresaTR != null) {
//            where.append(" obj.empresa = :empresaId ");
//            param.put("empresaId", empresaZw);
//        }

        if (!StringUtils.isBlank(niveisSelecionados)) {
            if (where.length() > 0) {
                where.append(" AND ");
            }
            where.append(" obj.nivelAluno.codigo in (").append(niveisSelecionados).append(") ");
        }

        String colaboradorIds = "";
        if (!UteisValidacao.emptyList(colaboradorZw)) {
            if (where.length() > 0) {
                where.append(" AND ");
            }
            for (Integer coladoradorId : colaboradorZw) {
                colaboradorIds += ","+coladoradorId;
            }
            colaboradorIds = colaboradorIds.replaceFirst(",", "");
            where.append(" obj.professorSintetico.codigoColaborador in (").append(colaboradorIds).append(")");
        }

        if (!StringUtils.isBlank(statusClienteEnumSelecionado)) {
            if (where.length() > 0) {
                where.append(" AND ");
            }

            Date inicio = Calendario.hoje();
            ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema duracaoNaAcademia = css.consultarPorTipo(ctx, ConfiguracoesEnum.DURACAO_ALUNO_NA_ACADEMIA);
            switch (statusClienteEnumSelecionado) {
                case "'MINHA_CARTEIRA'":
                    where.append(" professorsintetico_codigo = :professorSinteticoCodigo ");
                    where.append(" AND (obj.ativo is true or obj.ativo is null) ");
                    param.put("professorSinteticoCodigo", codigoProfessor);
                    break;
                case "'NA_ACADEMIA'":
                    where.append(" obj.codigo IN (SELECT sta.usuario.cliente.codigo FROM StatusPessoa sta  WHERE sta.dataInicioEvento between :inicio AND :fim AND sta.dataFimEvento IS NULL AND sta.empresa.codigo = :empresa) ");
                    param.put("empresa", empresaZw);
                    param.put("inicio", Uteis.somarCampoData(inicio, Calendar.MINUTE, -duracaoNaAcademia.getValorAsInteger()));
                    param.put("fim", Calendario.getDataComHora(inicio, "23:59:59"));
                    break;
                case "'NA_ACADEMIA_SEM_TREINO'":
                    where.append(" obj.codigo IN (SELECT sta.usuario.cliente.codigo FROM StatusPessoa sta  WHERE sta.dataInicioEvento between :inicio AND :fim AND sta.dataFimEvento IS NULL AND sta.empresa.codigo = :empresa) ");
                    where.append(" AND NOT EXISTS (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE obj.codigo = p.cliente.codigo) ");
                    param.put("empresa", empresaZw);
                    param.put("inicio", Uteis.somarCampoData(inicio, Calendar.MINUTE, -duracaoNaAcademia.getValorAsInteger()));
                    param.put("fim", Calendario.getDataComHora(inicio, "23:59:59"));
                    break;
                case "'NA_ACADEMIA_COM_TREINO_A_VENCER'":
                    where.append(" obj.codigo IN (SELECT sta.usuario.cliente.codigo FROM StatusPessoa sta  WHERE sta.dataInicioEvento between :inicio AND :fim AND sta.dataFimEvento IS NULL AND sta.empresa.codigo = :empresa) ");
                    where.append(" AND obj.codigo IN (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE p.programaTreinoRenovado is null and p.dataTerminoPrevisto between :antes and :depois ");
                    where.append(" AND NOT exists (SELECT pt.codigo FROM ProgramaTreino pt WHERE pt.dataInicio >= p.dataTerminoPrevisto and obj.codigo = pt.cliente.codigo)) ");
                    param.put("empresa", empresaZw);
                    param.put("inicio", Uteis.somarCampoData(inicio, Calendar.MINUTE, -duracaoNaAcademia.getValorAsInteger()));
                    param.put("fim", Calendario.getDataComHora(inicio, "23:59:59"));
                    ConfiguracaoSistema diasAntes = css.consultarPorTipo(ctx, ConfiguracoesEnum.DIAS_ANTES_VENCIMENTO);
                    Date depois = Uteis.somarCampoData(Calendario.hoje(), Calendar.DAY_OF_MONTH, diasAntes.getValorAsInteger());
                    param.put("antes", Calendario.getDataComHoraZerada(Calendario.hoje()));
                    param.put("depois", depois);
                    break;
                case "'NA_ACADEMIA_COM_TREINO_VENCIDO'":
                    where.append(" obj.codigo IN (SELECT sta.usuario.cliente.codigo FROM StatusPessoa sta  WHERE sta.dataInicioEvento between :inicio AND :fim AND sta.dataFimEvento IS null AND sta.empresa.codigo = :empresa) ");
                    where.append(" AND obj.codigo IN (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE CURRENT_DATE > p.dataTerminoPrevisto AND p.cliente.codigo IS NOT NULL) ");
                    where.append(" AND obj.codigo NOT IN (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE CURRENT_DATE <= p.dataTerminoPrevisto AND p.cliente.codigo IS NOT NULL) ");
                    where.append(" AND (obj.ativo is true or obj.ativo is null) ");
                    param.put("empresa", empresaZw);
                    param.put("inicio", Uteis.somarCampoData(inicio, Calendar.MINUTE, -duracaoNaAcademia.getValorAsInteger()));
                    param.put("fim", Calendario.getDataComHora(inicio, "23:59:59"));
                    break;
                case "'NA_ACADEMIA_DA_MINHA_CARTEIRA'":
                    where.append(" obj.codigo IN (SELECT sta.usuario.cliente.codigo FROM StatusPessoa sta WHERE sta.dataInicioEvento between current_date AND cast('2021-02-04' as date) AND sta.dataFimEvento IS null AND sta.empresa.codigo = :empresa) ");
                    where.append(" AND obj.professorSintetico.codigo = :professorSinteticoCodigo ");
                    param.put("empresa", empresaZw);
                    param.put("professorSinteticoCodigo", codigoProfessor);
                    break;
                case "'TREINO_VENCIDO'":
                    where.append(" obj.codigo IN (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE CURRENT_DATE > p.dataTerminoPrevisto AND p.cliente.codigo IS NOT NULL) ");
                    where.append(" AND obj.codigo NOT IN (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE CURRENT_DATE <= p.dataTerminoPrevisto AND p.cliente.codigo IS NOT NULL) ");
                    where.append(" AND (obj.ativo is true or obj.ativo is null) ");
                    break;
                case "'SEM_TREINO'":
                    where.append(" NOT EXISTS (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE obj.codigo = p.cliente.codigo)");
                    where.append(" AND (obj.ativo is true or obj.ativo is null) ");
                    break;
            }
        }

        if (where.length() > 0) {
            where.insert(0, " where ");
            hql.append(where);
        }
        return findByParamList(ctx, hql.toString(), param, 0, 0);
    }

    @Override
    public List<ClienteSintetico> findClienteSinteticoQueryNativa(String ctx, String query) throws Exception {
        List<ClienteSintetico> result = new ArrayList<ClienteSintetico>();

        if (!query.toUpperCase().startsWith("SELECT ")) {
            throw new SecurityException("Somente consultas SELECT são permitidas");
        }

        try (Connection conn = getConnection(ctx);
            PreparedStatement ps = conn.prepareStatement(query);
            ResultSet rs = ps.executeQuery()) {
            ClienteSinteticoMapper mapper = new ClienteSinteticoMapper();
            while (rs.next()) {
                try {
                    ClienteSintetico clienteSintetico = mapper.map(rs);
                    clienteSintetico.setProfessorSintetico(professorSinteticoDao.obterPorId(ctx, rs.getInt("professorsintetico_codigo")));
                    result.add(clienteSintetico);
                } catch (ServiceException e) {
                    throw new RuntimeException(e);
                }
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }

        return result;
    }

}
