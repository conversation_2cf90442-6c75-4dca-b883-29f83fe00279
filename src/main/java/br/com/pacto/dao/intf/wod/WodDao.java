package br.com.pacto.dao.intf.wod;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.wod.FiltroWodJSON;
import br.com.pacto.bean.wod.Wod;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.Date;
import java.util.List;

/**
 * Created by Rafael on 20/07/2016.
 */
public interface WodDao extends DaoGenerico<Wod, Integer> {

    /**
     * Consulta no banco os dias que possuem WOD
     *
     * @param contexto    contexto de acesso
     * @param dataInicial data inicial em millissegundos (inclusiva)
     * @param dataFinal   data final em millissegundos (exclusiva)
     * @return A lista de datas que possuem WOD
     */
    List<Date> consultarDiasWodPeriodo(String contexto, Integer empresa, Long dataInicial, Long dataFinal) throws Exception;

    List<Wod> obterListaWods(String ctx, FiltroWodJSON filtros, PaginadorDTO paginadorDTO, Integer empresaId, boolean restringirEmpresas) throws ServiceException;

    List<Wod> listarTodosOsWods(String ctx, Date dia, Integer empresaId) throws ServiceException;

    Wod obterPorId(String ctx, Integer id) throws ServiceException;
}
