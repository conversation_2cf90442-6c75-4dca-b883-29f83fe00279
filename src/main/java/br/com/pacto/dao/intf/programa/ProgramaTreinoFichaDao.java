/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.intf.programa;

import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface ProgramaTreinoFichaDao extends DaoGenerico<ProgramaTreinoFicha, Integer>{

    List<ProgramaTreinoFicha> obterPorProgramaTreino(String ctx, Integer programaTreinoId) throws ServiceException;

    List<ProgramaTreinoFicha> obterPorFicha(String ctx, Integer fichaId) throws ServiceException;

    void atualizarLista(String ctx, List<ProgramaTreinoFicha> programaTreinoFichas) throws Exception;

    void atualizarProgramaTreinoFicha(String ctx, ProgramaTreinoFicha programaTreinoFicha) throws Exception;
}
