package br.com.pacto.controller.json.totalpass;

public class TotalPassDTO {

    private Integer codigo;
    private Integer empresa;
    private String nome;
    private String tokenApiTotalpass;
    private String codigoTotalPass;
    private Integer limiteDeAcessosPorDia;
    private Integer limiteDeAulasPorDia;

    public TotalPassDTO() {}

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
         this.empresa = empresa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTokenApiTotalpass() {
        return tokenApiTotalpass;
    }

    public void setTokenApiTotalpass(String tokenApiTotalpass) {
        this.tokenApiTotalpass = tokenApiTotalpass;
    }

    public String getCodigoTotalPass() {
        return codigoTotalPass;
    }

    public void setCodigoTotalPass(String codigoTotalPass) {
        this.codigoTotalPass = codigoTotalPass;
    }

    public Integer getLimiteDeAcessosPorDia() {
        return limiteDeAcessosPorDia;
    }

    public void setLimiteDeAcessosPorDia(Integer limiteDeAcessosPorDia) {
        this.limiteDeAcessosPorDia = limiteDeAcessosPorDia;
    }

    public Integer getLimiteDeAulasPorDia() {
        return limiteDeAulasPorDia;
    }

    public void setLimiteDeAulasPorDia(Integer limiteDeAulasPorDia) {
        this.limiteDeAulasPorDia = limiteDeAulasPorDia;
    }
}
