package br.com.pacto.controller.json.base;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.controller.json.aluno.AlunoController;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.intf.processo.ProcessosService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/manutencao")
public class ManutencaoController extends SuperController {

    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ProcessosService processosService;

    @ResponseBody
    @RequestMapping(value = "/replicar-assinatura-contrato-para-parq", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> replicarAssinaturaContratoParaParq(@RequestParam("empresaZW") Integer empresaZW,
                                                                                  @RequestParam("apenasValidar") Boolean apenasValidar) {
        // Processo para replicar assinatura do contrato na assinatura do parq de alunos com assinatura indevida;
        String key = sessaoService.getUsuarioAtual().getChave();
        try {
            return ResponseEntityFactory.ok(processosService.replicarAssinaturaContratoParaParq(key, empresaZW, apenasValidar));
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo replicarAssinaturaContratoParaParq", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), "Erro ao executar o processo replicarAssinaturaContratoParaParq");
        }
    }

    @RequestMapping(value = "/forcar-sincronizacao-fotokey-pessoa-zw-tr", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> forcarSincronizacaoFotoKeyPessoaZwTr() {
        // Processo para forçar sincronização de fotokey de pessoa entre zw e treino;
        String key = sessaoService.getUsuarioAtual().getChave();
        try {
            return ResponseEntityFactory.ok(processosService.forcarSincronizacaoFotoKeyPessoaZwTr(key));
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo forcarSincronizacaoFotoKeyPessoaZwTr", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), "Erro ao executar o processo forcarSincronizacaoFotoKeyPessoaZwTr");
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/validar-sincronizacao-todos-alunos-zw-tr", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> validarSincronizacaoTodosAlunosZwTr(@RequestParam("considerarVisitante") Boolean considerarVisitante,
                                                                                   @RequestHeader("empresaId") Integer empresaId) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(processosService.validarSincronizacaoTodosAlunosZwTr(ctx, empresaId, considerarVisitante));
        } catch (Exception e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar importar aluno", e);
            return ResponseEntityFactory.erroInterno("Erro", e.getMessage());
        }
    }

}
