package br.com.pacto.controller.json.programa;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.ficha.CategoriaFichaResponseTO;
import br.com.pacto.bean.ficha.FiltroCategoriaFichaJSON;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.ficha.CategoriaFichaService;
import br.com.pacto.service.intf.ficha.FichaService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.servlet.http.HttpServletRequest;
import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
    @RequestMapping("/psec/categoria-ficha")
public class CategoriaFichaController {
    CategoriaFichaService categoriaFichaService;
    FichaService fichaService;

    @Autowired
    public CategoriaFichaController(CategoriaFichaService categoriaFichaService, FichaService fichaService){
        Assert.notNull(categoriaFichaService, "O serviço de ficha não foi injetado corretamente");
        this.categoriaFichaService = categoriaFichaService;

        Assert.notNull(fichaService, "O serviço de ficha não foi injetado corretamente");
        this.fichaService = fichaService;
    }
    @Autowired
    private HttpServletRequest request;


    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarCategoriaFicha(@RequestBody CategoriaFichaResponseTO categoriaFichaResponseTO, HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(categoriaFichaService.criarCategoriaFicha(categoriaFichaResponseTO, request));
        } catch (ServiceException e) {
            Logger.getLogger(CategoriaFichaController.class.getName()).log(Level.SEVERE, "Erro ao criar uma ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.CATEGORIA_FICHAS)
    @RequestMapping(value = "/categorias", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarCategoriaFichas(
            @RequestParam(value = "filters", required = false) JSONObject filtros,PaginadorDTO paginadorDTO) {
        try {
            FiltroCategoriaFichaJSON filtroCategoriaFichaJSON = new FiltroCategoriaFichaJSON(filtros);
            return ResponseEntityFactory.ok(categoriaFichaService.listarCategoriasFicha(filtroCategoriaFichaJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(CategoriaFichaController.class.getName()).log(Level.SEVERE, "Erro ao carregar categoria de fichas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> editarCategoriaFichas(@RequestBody CategoriaFichaResponseTO categoriaFichaResponseTO, HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(categoriaFichaService.editarCategoriaFicha(categoriaFichaResponseTO, request));
        } catch (ServiceException e) {
            Logger.getLogger(CategoriaFichaController.class.getName()).log(Level.SEVERE, "Erro ao editar uma ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirCategoriaFichas(@PathVariable("id") final Integer id){
        try {
            categoriaFichaService.excluirCategoriaFicha(id, request);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(CategoriaFichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }
}
