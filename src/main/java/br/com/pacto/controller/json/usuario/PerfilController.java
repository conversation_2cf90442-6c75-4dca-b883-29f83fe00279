package br.com.pacto.controller.json.usuario;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.FiltroPerfilJSON;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.perfil.PerfilService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 24/08/2018.
 */
@Controller
@RequestMapping("/psec/perfis-acesso")
public class PerfilController {

    private PerfilService perfilService;

    @Autowired
    public PerfilController(PerfilService perfilService){
        Assert.notNull(perfilService, "O serviço de perfil não foi injetado corretamente");
        this.perfilService = perfilService;
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.PERFIL_USUARIO)
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarPerfil(@RequestBody HashMap<String, Object> perfilDTO) {
        try {
            return ResponseEntityFactory.ok(perfilService.cadastrarPerfil(perfilDTO));
        } catch (ServiceException e) {
            Logger.getLogger(PerfilController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar perfil", e);
            if (e.getCause() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PERFIL_USUARIO)
    @RequestMapping(value = "/{id}", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> editarPerfil(@PathVariable("id") Integer perfilId, @RequestBody HashMap<String, Object> perfilDTO) {
        try {
            return ResponseEntityFactory.ok(perfilService.editarPerfil(perfilDTO, perfilId, null));
        } catch (ServiceException e) {
            Logger.getLogger(PerfilController.class.getName()).log(Level.SEVERE, "Erro ao tentar editar perfil", e);
            if (e.getCause() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PERFIL_USUARIO)
    @RequestMapping(value = "porNome/{nome}", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> editarPerfil(@PathVariable("nome") String perfilNome, @RequestBody HashMap<String, Object> perfilDTO) {
        try {
            return ResponseEntityFactory.ok(perfilService.editarPerfil(perfilDTO, null, perfilNome));
        } catch (ServiceException e) {
            Logger.getLogger(PerfilController.class.getName()).log(Level.SEVERE, "Erro ao tentar editar perfil", e);
            if (e.getCause() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarPerfil(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                            PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroPerfilJSON filtroPerfilJSON = new FiltroPerfilJSON(filtros);
            return ResponseEntityFactory.ok(perfilService.consultarPerfil(filtroPerfilJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(PerfilController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar perfil", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PERFIL_USUARIO)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterPerfil(@PathVariable(value = "id") Integer perfilId) {
        try {
            return ResponseEntityFactory.ok(perfilService.obterPerfil(perfilId));
        } catch (ServiceException e) {
            Logger.getLogger(PerfilController.class.getName()).log(Level.SEVERE, "Erro ao consultar perfil", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PERFIL_USUARIO)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerPerfil(@PathVariable(value = "id") Integer perfilId) {
        try {
            perfilService.removerPerfil(perfilId);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(PerfilController.class.getName()).log(Level.SEVERE, "Erro ao remover perfil", e);
            if (e.getCause() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PERFIL_USUARIO)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarTodos() {
        try {
            return ResponseEntityFactory.ok(perfilService.consultarTodos());
        } catch (ServiceException e) {
            Logger.getLogger(PerfilController.class.getName()).log(Level.SEVERE, "Erro ao consultar todos os perfis", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.PERFIL_USUARIO)
    @RequestMapping(value = "/consultar-professores-por-perfil/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarProfessoresPorPerfil(@PathVariable(value = "id") Integer perfilId,
                                                                             @RequestParam(value = "filters", required = false) String filter,
                                                                             @RequestHeader("empresaId") Integer empresaIdZw,
                                                                             PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(perfilService.consultarProfessoresPorPerfil(perfilId, paginadorDTO, filter, empresaIdZw), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(PerfilController.class.getName()).log(Level.SEVERE, "Erro ao listar os professores do perfil informado", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}


