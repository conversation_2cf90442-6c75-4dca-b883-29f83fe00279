package br.com.pacto.controller.json.professor;

import br.com.pacto.bean.professor.ProfessorSintetico;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by paulo 31/10/2018
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProfessorSimplesResponseDTO {

    private Integer id;
    private String nome;

    public ProfessorSimplesResponseDTO(ProfessorSintetico professorSintetico) {
        this.id = professorSintetico.getCodigo();
        this.nome = professorSintetico.getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
