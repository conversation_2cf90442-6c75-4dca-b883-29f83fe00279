package br.com.pacto.controller.json.sintetico;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.professor.Professor<PERSON><PERSON><PERSON><PERSON>;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.usuarioEmail.UsuarioEmail;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.sintetico.SinteticoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.service.intf.usuarioEmail.UsuarioEmailService;
import br.com.pacto.swagger.respostas.sintetico.ExemploRespostaExcluirAluno;
import br.com.pacto.swagger.respostas.sintetico.ExemploRespostaIncrementarVersao;
import br.com.pacto.swagger.respostas.sintetico.ExemploRespostaSincronizacaoUsuario;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.webservice.TreinoWS;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import servicos.integracao.zw.beans.ClienteZW;
import servicos.integracao.zw.beans.UsuarioZW;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/sintetico")
public class SinteticoController {

    @ApiOperation(
            value = "Sincronizar usuários do sistema externo",
            notes = "Sincroniza uma lista de usuários (alunos ou professores) do sistema externo com o sistema interno. " +
                    "Realiza a integração completa incluindo criação/atualização de dados pessoais, vinculação com empresas, " +
                    "tratamento de perfis de usuário, configuração de e-mails e sincronização de fotos. " +
                    "Processa tanto alunos quanto professores/colaboradores de acordo com o tipo especificado.",
            tags = "Usuário"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Sincronização realizada)", response = ExemploRespostaSincronizacaoUsuario.class)
    })
    @RequestMapping(value = "/sincronizarUsuario", method = RequestMethod.POST)
    public @ResponseBody
    String sincronizarUsuario(
            @ApiParam(value = "Chave de autenticação da empresa", required = true, defaultValue = "empresa123")
            @RequestHeader String key,
            @ApiParam(value = "Lista de usuários para sincronização. Cada usuário deve conter os dados completos incluindo tipo (ALUNO/PROFESSOR), dados pessoais, códigos de identificação e informações de empresa. Para alunos, deve incluir dados do cliente. Para professores, deve incluir dados do colaborador.")
            @RequestBody List<UsuarioZW> list) {
        for (UsuarioZW usuarioZW : list) {
            try {
                if (usuarioZW.getCodigoExterno() != null && !usuarioZW.getCodigoExterno().isEmpty()
                        && Integer.valueOf(usuarioZW.getCodigoExterno()) > 0) {
                    UsuarioService usuarioService = (UsuarioService) UtilContext.getBean(
                            UsuarioService.class);
                    EmpresaService empresaService = (EmpresaService) UtilContext.getBean(EmpresaService.class);
                    empresaService.persistirEmpresasZW(key, usuarioZW.getEmpresaZW(),
                            usuarioZW.getUsuarioZW());

                    TipoUsuarioEnum tipo = usuarioZW.getTipo();
                    UsuarioEmail usuarioEmail = null;
                    ClienteSintetico cli = null;
                    ProfessorSintetico profLocal = null;
                    SinteticoService sinteticoService = (SinteticoService) UtilContext.getBean(SinteticoService.class);
                    if (tipo.equals(TipoUsuarioEnum.ALUNO)) {
                        sinteticoService.sincronizarAluno(key, usuarioZW);
                        ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(
                                ClienteSinteticoService.class);
                        String s = "select obj from ClienteSintetico obj where codigoPessoa = :id";
                        Map<String, Object> p = new HashMap<String, Object>();
                        p.put("id", usuarioZW.getCliente().getCodigoPessoa());
                        cli = clienteService.obterObjetoPorParam(key, s, p);
                        if (cli != null) {
                            clienteService.refresh(key, cli);
                            usuarioZW.setCliente(ClienteZW.fromClienteSintetico(cli));
                            usuarioZW.getCliente().setCodigo(cli.getCodigo());
                        }
                    } else {
                        ProfessorSinteticoService professorService = (ProfessorSinteticoService) UtilContext.getBean(
                                ProfessorSinteticoService.class);
                        professorService.sincronizarProfessor(key, usuarioZW.getProfessor(), usuarioZW.getEmpresaZW(), usuarioZW.getFotoKey());

                        String s = "select obj from ProfessorSintetico obj where codigoColaborador = :id";
                        Map<String, Object> p = new HashMap<String, Object>();
                        p.put("id", usuarioZW.getProfessor().getCodigoColaborador());
                        profLocal = professorService.obterObjetoPorParam(key, s, p);
                        if (profLocal != null) {
                            usuarioZW.setProfessor(profLocal);
                        }
                    }

                    final String where = tipo.equals(TipoUsuarioEnum.ALUNO) ? " where cliente.codigoCliente = :chave " : " where professor.codigo = :chave";
                    String s = "select u from Usuario u " + where;
                    HashMap<String, Object> p = new HashMap<String, Object>();
                    p.put("chave", tipo.equals(TipoUsuarioEnum.ALUNO) ? usuarioZW.getCliente().getCodigoCliente() : usuarioZW.getProfessor().getCodigo());
                    Usuario uLocal = usuarioService.obterObjetoPorParam(key, s, p);

                    UsuarioEmailService usuarioEmailService = (UsuarioEmailService) UtilContext.getBean(UsuarioEmailService.class);
                    if (!tipo.equals(TipoUsuarioEnum.ALUNO) && uLocal != null && uLocal.getUsuarioEmail() != null) {
                        usuarioEmail = usuarioEmailService.obterUsuarioEmailParam(key, "codigo", uLocal.getUsuarioEmail().getCodigo());
                    }

                    if (usuarioEmail == null) {
                        usuarioEmail = usuarioEmailService.obterUsuarioEmailParam(key, "email", usuarioZW.getEmail());
                        if (usuarioEmail == null) {
                            usuarioEmail = new UsuarioEmail();
                        }
                    }
                    usuarioEmail.setEmail(usuarioZW.getEmail());

                    Map<String, Object> params = new HashMap<String, Object>();
                    params.put("usuarioEmail", usuarioEmail.getEmail());
                    Usuario usuarioDoEmail = usuarioService.obterObjetoPorParam(key, "SELECT u FROM Usuario u WHERE u.usuarioEmail.email = :usuarioEmail", params);
                    if (usuarioDoEmail != null && usuarioDoEmail.getUsuarioZW() != null && !usuarioDoEmail.getUsuarioZW().equals(usuarioZW.getUsuarioZW())) {
                        System.out.println("ERRO: ".concat("O e-mail está vinculado ao usuário ".concat(usuarioDoEmail.getNome())));
                        continue;
                    }

                    if (uLocal == null) {


                        usuarioZW.setCodigo(null);
                        uLocal = UsuarioZW.toUsuarioTreino(usuarioZW);
                        uLocal.setCliente(cli);
                        uLocal.setTipo(tipo);
                        uLocal.setProfessor(profLocal);
                        if (!tipo.equals(TipoUsuarioEnum.ALUNO)) {
                            uLocal.setUsuarioEmail(usuarioEmail);
                        }
                        sinteticoService.tratarPerfilUsuario(key, usuarioZW, uLocal);
                        usuarioService.inserir(key, uLocal);
                    } else {
                        usuarioZW.setCodigo(uLocal.getCodigo());
                        try {
                            if (usuarioEmail.getCodigo() != null) {
                                usuarioEmail = usuarioEmailService.alterar(key, usuarioEmail);
                            } else {
                                usuarioEmail = usuarioEmailService.inserir(key, usuarioEmail);
                            }
                        } catch (Exception ex) {
                            System.out.println("Não foi possível alterar e-mail: " + ex.getMessage());
                        }
                        if (!tipo.equals(TipoUsuarioEnum.ALUNO)) {
                            uLocal.setUsuarioEmail(usuarioEmail);
                        }
                        if (!UteisValidacao.emptyNumber(usuarioZW.getPerfilTw())) {
                            sinteticoService.tratarPerfilUsuario(key, usuarioZW, uLocal);
                        }
                        uLocal = usuarioService.alterar(key, uLocal, usuarioZW);
                        usuarioService.adicionarUsuarioServicoDescobrir(key, uLocal.getUserName());
                    }
                } else {
                    System.out.println("Usuário inválido!");
                    continue;
                }
            } catch (Exception ex) {
                Logger.getLogger(SinteticoController.class.getName()).log(Level.SEVERE, null, ex);
                return "ERRO: ".concat(ex.getMessage());
            }
        }
        return "OK";
    }

    @ApiOperation(
            value = "Incrementar versão do cliente",
            notes = "Incrementa a versão do cliente no sistema e opcionalmente atualiza a foto do aluno no aplicativo. " +
                    "A operação é utilizada para controle de sincronização e versionamento dos dados do cliente, " +
                    "garantindo que as informações estejam atualizadas entre os sistemas. " +
                    "Se fornecida uma chave de foto válida, também realiza a atualização da foto do aluno no aplicativo.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Versão incrementada)", response = ExemploRespostaIncrementarVersao.class)
    })
    @RequestMapping(value = "/incrementarVersaoCliente", method = RequestMethod.POST)
    public @ResponseBody
    String incrementarVersaoCliente(
            @ApiParam(value = "Chave de autenticação da empresa", required = true, defaultValue = "empresa123")
            @RequestHeader String key,
            @ApiParam(value = "Dados do usuário contendo o código do cliente para incremento de versão e opcionalmente a chave da foto para atualização no aplicativo")
            @RequestBody UsuarioZW usuarioZW) {
        try {
            ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(
                    ClienteSinteticoService.class);
            clienteService.incrementarVersao(key,usuarioZW.getCliente().getCodigoCliente());
            if(!UteisValidacao.emptyString(usuarioZW.getFotoKey())) {
                clienteService.atualizarFotoAlunoApp(key, usuarioZW);
            }
            return "OK";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }
}
