package br.com.pacto.controller.json.selfloops;

import java.util.List;

public class SelfloopsDTO {

    private String pactoClientId;
    private Integer empresa; //codigo empresa do treino
    private String nome;
    private String code;
    private String empresaSelfloops;
    private List<TeamsSelfloopsDTO> teams;

    public SelfloopsDTO() {}

    public String getPactoClientId() {
        return pactoClientId;
    }

    public void setPactoClientId(String pactoClientId) {
        this.pactoClientId = pactoClientId;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public List<TeamsSelfloopsDTO> getTeams() {
        return teams;
    }

    public void setTeams(List<TeamsSelfloopsDTO> teams) {
        this.teams = teams;
    }

    public String getEmpresaSelfloops() {
        return empresaSelfloops;
    }

    public void setEmpresaSelfloops(String empresaSelfloops) {
        this.empresaSelfloops = empresaSelfloops;
    }
}
