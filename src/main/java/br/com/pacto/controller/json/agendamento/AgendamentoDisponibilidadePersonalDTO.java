package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.objeto.Calendario;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by paulo 03/08/2019
 */
@ApiModel(description = "Disponibilidade de agendamento personalizado com informações do professor e tipo de serviço")
public class AgendamentoDisponibilidadePersonalDTO {

    @ApiModelProperty(value = "Código identificador único da disponibilidade de agendamento", example = "1001")
    Integer id;

    @ApiModelProperty(value = "Data da disponibilidade no formato dd/MM/yyyy", example = "15/01/2024")
    String dia;

    @ApiModelProperty(value = "Horário de início da disponibilidade no formato HH:mm", example = "14:00")
    String horarioInicial;

    @ApiModelProperty(value = "Horário de término da disponibilidade no formato HH:mm", example = "15:00")
    String horarioFinal;

    @ApiModelProperty(value = "Status atual da disponibilidade. " +
                             "<br/><strong>Valores disponíveis:</strong>" +
                             "<ul>" +
                             "<li>AGUARDANDO_CONFIRMACAO - Aguardando confirmação</li>" +
                             "<li>CONFIRMADO - Confirmado</li>" +
                             "<li>EXECUTADO - Executado</li>" +
                             "<li>CANCELADO - Cancelado</li>" +
                             "<li>FALTOU - Faltou</li>" +
                             "<li>REAGENDADO - Reagendado</li>" +
                             "</ul>",
                             example = "AGUARDANDO_CONFIRMACAO")
    StatusAgendamentoEnum status;

    @ApiModelProperty(value = "Informações básicas do professor responsável pela disponibilidade")
    ColaboradorSimplesTO professor;

    @ApiModelProperty(value = "Detalhes do tipo de agendamento/serviço disponível")
    TipoAgendamentoDTO tipoAgendamento;

    @ApiModelProperty(value = "Código da disponibilidade de horário associada, quando aplicável", example = "501")
    Integer horarioDisponibilidadeCod;

    public AgendamentoDisponibilidadePersonalDTO(Agendamento agendamento, Boolean treinoIndependente) {
        this.id = agendamento.getCodigo();
        this.dia = Calendario.getData(agendamento.getInicio(), "dd/MM/yyyy");
        this.horarioInicial = agendamento.getHoraInicioApresentar();
        this.horarioFinal = agendamento.getHoraFimApresentar();
        this.status = agendamento.getStatus();
        this.professor = new ColaboradorSimplesTO(agendamento.getProfessor(), treinoIndependente);
        if(agendamento.getTipoEvento() != null){
            this.tipoAgendamento = new TipoAgendamentoDTO(agendamento.getTipoEvento());
        }else{
            this.tipoAgendamento = new TipoAgendamentoDTO(agendamento.getHorarioDisponibilidade().getDisponibilidade());
            this.tipoAgendamento.setPermitir_app(agendamento.getHorarioDisponibilidade().getPermieAgendarAppTreino());
            this.tipoAgendamento.setSomente_carteira_professor(agendamento.getHorarioDisponibilidade().getApenasAlunosCarteira());
            this.horarioDisponibilidadeCod = agendamento.getHorarioDisponibilidade().getCodigo();
        }
    }

    public AgendamentoDisponibilidadePersonalDTO() {
    }

    public AgendamentoDisponibilidadePersonalDTO(Agendamento agendamento, Boolean treinoIndependente, String horarioInicial, String horarioFinal) {
        this.id = agendamento.getCodigo();
        this.dia = Calendario.getData(agendamento.getInicio(), "dd/MM/yyyy");
        this.horarioInicial = horarioInicial;
        this.horarioFinal = horarioFinal;
        this.status = agendamento.getStatus();
        this.professor = new ColaboradorSimplesTO(agendamento.getProfessor(), treinoIndependente);
        if (agendamento.getTipoEvento() != null) {
            this.tipoAgendamento = new TipoAgendamentoDTO(agendamento.getTipoEvento());
        } else {
            this.tipoAgendamento = new TipoAgendamentoDTO(agendamento.getHorarioDisponibilidade().getDisponibilidade());
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public ColaboradorSimplesTO getProfessor() {
        return professor;
    }

    public void setProfessor(ColaboradorSimplesTO professor) {
        this.professor = professor;
    }

    public TipoAgendamentoDTO getTipoAgendamento() {
        return tipoAgendamento;
    }

    public void setTipoAgendamento(TipoAgendamentoDTO tipoAgendamento) {
        this.tipoAgendamento = tipoAgendamento;
    }

    public StatusAgendamentoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusAgendamentoEnum status) {
        this.status = status;
    }

    public Integer getHorarioDisponibilidadeCod() {
        return horarioDisponibilidadeCod;
    }

    public void setHorarioDisponibilidadeCod(Integer horarioDisponibilidadeCod) {
        this.horarioDisponibilidadeCod = horarioDisponibilidadeCod;
    }
}
