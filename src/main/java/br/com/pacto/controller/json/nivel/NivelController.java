package br.com.pacto.controller.json.nivel;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.nivel.NivelTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.log.LogService;
import br.com.pacto.service.intf.nivel.NivelService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 24/08/2018.
 */
@Controller
@RequestMapping("/psec/niveis")
public class NivelController extends SuperController {

    private final NivelService nivelService;
    private final ClienteSinteticoService clienteSinteticoService;

    @Autowired
    public NivelController(NivelService nivelService, ClienteSinteticoService clienteSinteticoService){
        Assert.notNull(nivelService, "O serviço de Nível não foi injetado corretamente");
        this.nivelService = nivelService;
        this.clienteSinteticoService = clienteSinteticoService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.NIVEIS)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarNiveis(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                               PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroNivelJSON filtroNivelJSON = new FiltroNivelJSON(filtros);
            return ResponseEntityFactory.ok(nivelService.consultarNivel(filtroNivelJSON, paginadorDTO),paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(NivelController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os níveis", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.NIVEIS)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirNivel(@RequestBody NivelTO nivelTO) {
        try {
            return ResponseEntityFactory.ok(nivelService.inserir(nivelTO));
        } catch (ServiceException e) {
            Logger.getLogger(NivelController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir nivel", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.NIVEIS)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarNivel(@PathVariable("id") final Integer id,
                                                                @RequestBody NivelTO nivelTO) {
        try {
            return ResponseEntityFactory.ok(nivelService.alterar(id, nivelTO));

        } catch (ServiceException e) {
            Logger.getLogger(NivelController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar nivel", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.NIVEIS)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirNivel(@PathVariable("id") final Integer id){
        try {
            nivelService.excluir(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(NivelController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir nivel", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.NIVEIS)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosNiveis() {
        try {
            return ResponseEntityFactory.ok(nivelService.listarTodosNiveis());
        } catch (ServiceException e) {
            Logger.getLogger(NivelController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar todos os niveis", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/aluno/{codigoCliente}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> nivelAluno(@PathVariable("codigoCliente") final Integer codigoCliente) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.nivelAluno(codigoCliente));
        } catch (ServiceException e) {
            Logger.getLogger(NivelController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar nivel", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

}
