package br.com.pacto.controller.json.wod;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.wod.FiltroWodJSON;
import br.com.pacto.bean.wod.WodTO;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.wod.WodService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.sql.Timestamp;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 28/08/2018.
 */
@Controller
@RequestMapping("/psec/wods")
public class WodController {

    private final WodService wodService;

    @Autowired
    public WodController(WodService wodService){
        Assert.notNull(wodService, "O serviço de wod não foi injetado corretamente");
        this.wodService = wodService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterListaWods(
            @RequestHeader(value = "empresaId") Integer empresaId,
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroWodJSON filtroWodJSON = new FiltroWodJSON(filtros);
            boolean restringirEmpresas = false;
            return ResponseEntityFactory.ok(wodService.obterListaWods(filtroWodJSON, paginadorDTO, empresaId, restringirEmpresas), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar wods", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosOsWods(
            @RequestHeader(value = "empresaId", required = false) Integer empresaId,
            @RequestParam(value = "data",required = false) Long data) {
        try {
            return ResponseEntityFactory.ok(wodService.listarTodosWods(Uteis.dataHoraZeradaUTC(data), empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar wods", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarWod(HttpServletRequest request,@PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(wodService.buscarWod(id,request));
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar wod", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/wods-importados-crossfit", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> importarWodCrossfit(@RequestParam("filtroDia") Long filtroDia) {
        try {
            return ResponseEntityFactory.ok(wodService.importarWodCrossfit(Uteis.dataHoraZeradaUTC(filtroDia)));
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar importar wod crossfit", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarWod(
            @RequestHeader (value = "empresaId", required = true) Integer empresaId,
            @RequestBody WodTO wodTO, HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(wodService.cadastrarWod(wodTO, empresaId, request));
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar wod", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarWod(@PathVariable("id") final Integer id,
                                                          @RequestHeader(value = "empresaId") Integer empresaId,
                                                          @RequestBody WodTO wodTO,
                                                          HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(wodService.alterarWod(wodTO, id, empresaId, request));
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar wod", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerWod(@PathVariable("id") final Integer id) {
        try {
            wodService.removerWod(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar wod", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/importar-wods-da-franqueadora", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> importarWodsDaFranqueadora(@RequestHeader(value = "empresaId") Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(wodService.importarWodsDaFranqueadora(empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar importar os wods da franqueadora", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
