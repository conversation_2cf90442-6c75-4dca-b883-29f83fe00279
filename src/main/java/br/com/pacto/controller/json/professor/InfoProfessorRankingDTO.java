package br.com.pacto.controller.json.professor;

import br.com.pacto.bean.bi.IndicadorDashboardEnum;
import br.com.pacto.bean.bi.ProfessorRankingIndicador;
import br.com.pacto.objeto.Uteis;

public class InfoProfessorRankingDTO {

    private String momento;
    private String label;
    private Integer valor;
    private Double pontos;

    public InfoProfessorRankingDTO() {
    }

    public InfoProfessorRankingDTO(ProfessorRankingIndicador professorRankingIndicador) {
        this.label = professorRankingIndicador.getIndicador().getLabelSort();
        if(professorRankingIndicador.getIndicador().equals(IndicadorDashboardEnum.PERCENTUAL_RENOVACAO_CARTEIRA) ||
                professorRankingIndicador.getIndicador().equals(IndicadorDashboardEnum.PERC_TREINO_EM_DIA) ){
            this.pontos = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(
                    new Double(professorRankingIndicador.getValor()));
            this.valor = new Double(professorRankingIndicador.getValor()).intValue();
        }else{
            this.pontos = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(
                    new Double(professorRankingIndicador.getValor() * professorRankingIndicador.getMultiplicador()));
            this.valor = new Double(professorRankingIndicador.getValor() * professorRankingIndicador.getMultiplicador()).intValue();
        }

        if(!professorRankingIndicador.getPositivo()){
            this.pontos = this.pontos * -1;
            this.valor = this.valor * -1;
        }
    }

    public InfoProfessorRankingDTO(String momento, String label) {
        this.momento = momento;
        this.label = label;
    }

    public String getMomento() {
        return momento;
    }

    public void setMomento(String momento) {
        this.momento = momento;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Integer getValor() {
        return valor;
    }

    public void setValor(Integer valor) {
        this.valor = valor;
    }

    public Double getPontos() {
        return pontos;
    }

    public void setPontos(Double pontos) {
        this.pontos = pontos;
    }
}
