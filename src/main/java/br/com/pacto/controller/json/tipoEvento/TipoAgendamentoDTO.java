package br.com.pacto.controller.json.tipoEvento;

import br.com.pacto.bean.disponibilidade.Disponibilidade;
import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.enumerador.agenda.TipoDuracaoEvento;
import br.com.pacto.util.enumeradores.PaletaCoresEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;

@ApiModel(description = "DTO para representar os detalhes de um tipo de agendamento, contendo configurações de duração, comportamento e outras propriedades de agendamento.")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TipoAgendamentoDTO {

    @ApiModelProperty(value = "Identificador único do tipo de agendamento", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do tipo de agendamento", example = "Avaliação Física")
    private String nome;

    @ApiModelProperty(value = "Duração do intervalo inferior", example = "15")
    private String duracao_intervalo_inferior;

    @ApiModelProperty(value = "Duração do intervalo superior", example = "30")
    private String duracao_intervalo_superior;

    @ApiModelProperty(value = "Duração fixa do evento", example = "60")
    private String duracao_fixa;

    @ApiModelProperty(
            value = "Comportamento do agendamento (enum ordinal). Valores possíveis: " +
                    "0 = Contato interpessoal, " +
                    "1 = Prescrição de treino, " +
                    "2 = Revisão de treino, " +
                    "3 = Renovar treino, " +
                    "4 = Avaliação física",
            example = "1"
    )
    @Enumerated(EnumType.ORDINAL)
    private TipoAgendamentoEnum comportamentoEnum;

    @ApiModelProperty(value = "Descrição do comportamento", example = "Avaliação Física")
    private String comportamento;

    @ApiModelProperty(value = "Indica se o tipo de agendamento está ativo", example = "true")
    private Boolean ativo;

    @ApiModelProperty(
            value = "Tipo de duração do evento (enum ordinal). Valores possíveis: " +
                    "DURACAO_LIVRE, " +
                    "DURACAO_PREDEFINIDA, " +
                    "INTERVALO_DE_TEMPO",
            example = "DURACAO_LIVRE"
    )
    @Enumerated(EnumType.ORDINAL)
    private TipoDuracaoEvento tipo_duracao;

    @ApiModelProperty(value = "Cor associada (enum ordinal)", example = "2")
    @Enumerated(EnumType.ORDINAL)
    private PaletaCoresEnum corEnum;

    @ApiModelProperty(value = "Código da cor", example = "#FF5733")
    private String cor;

    @ApiModelProperty(value = "Número de agendamentos realizados", example = "5")
    private Integer numero_agendamentos;

    @ApiModelProperty(value = "Quantidade de dias", example = "7")
    private Integer dias;

    @ApiModelProperty(value = "Intervalo mínimo, em minutos, caso haja falta", example = "10")
    private Integer intervalo_minimo_caso_falta;

    @ApiModelProperty(value = "Indica se o agendamento é restrito à carteira do professor", example = "false")
    private Boolean somente_carteira_professor;

    @ApiModelProperty(value = "Indica se o aplicativo está permitido para esse tipo de agendamento", example = "true")
    private Boolean permitir_app;

    @ApiModelProperty(value = "Indica se o item é do tipo agendamento (true) ou uma nova disponibilidade (false)", example = "true")
    private Boolean tipoEvento; // true = tipo agendamento // false = nova disponibilidade

    @ApiModelProperty(value = "Dados do professor responsável, se aplicável")
    private ProfessorResponseTO professor;

    @ApiModelProperty(value = "Hora de início do agendamento (formato HH:mm)", example = "08:00")
    private String inicio;

    @ApiModelProperty(value = "Hora de término do agendamento (formato HH:mm)", example = "09:00")
    private String fim;

    public TipoAgendamentoDTO() {
    }

    public TipoAgendamentoDTO(Disponibilidade t){
        this(t, null);
    }

    public TipoAgendamentoDTO(TipoEvento t){
        this(t, null);
    }
    public TipoAgendamentoDTO(TipoEvento t, ProfessorResponseTO professor){
        this.professor = professor;
        this.id = t.getCodigo();
        this.nome = t.getNome();
        this.comportamento = t.getComportamento().getDescricao();
        this.comportamentoEnum = t.getComportamento();
        this.ativo = t.getAtivo();
        this.cor= t.getCor().getCor();
        this.corEnum = t.getCor();
        this.dias = t.getDias();
        this.numero_agendamentos = t.getNrAgendamentos();
        this.intervalo_minimo_caso_falta = t.getIntervaloMinimoFalta();
        this.tipo_duracao = t.getDuracao();
        if (t.getDuracao() == TipoDuracaoEvento.DURACAO_PREDEFINIDA) {
            this.duracao_fixa = String.valueOf(t.getDuracaoMinutosMin());
            this.duracao_intervalo_superior = String.valueOf(0);
            this.duracao_intervalo_inferior = String.valueOf(0);
        } else if (t.getDuracao() == TipoDuracaoEvento.INTERVALO_DE_TEMPO){
            this.duracao_intervalo_superior = String.valueOf(t.getDuracaoMinutosMax());
            this.duracao_intervalo_inferior = String.valueOf(t.getDuracaoMinutosMin());
            this.duracao_fixa = String.valueOf(0);
        }
        this.somente_carteira_professor = t.getApenasAlunosCarteira();
        this.permitir_app = t.getPermitirApp();
        this.tipoEvento = true;
    }

    public TipoAgendamentoDTO(Disponibilidade t, ProfessorResponseTO professor) {
        this.professor = professor;
        this.id = t.getCodigo();
        this.nome = t.getNome();
        TipoAgendamentoEnum tipoEnum = TipoAgendamentoEnum.getFromId(t.getComportamento());
        this.comportamento = tipoEnum.getDescricao();
        this.comportamentoEnum = tipoEnum;
        this.ativo = true;
        this.cor = t.getCor();
        this.corEnum = PaletaCoresEnum.getFromHex(t.getCor());
        this.tipo_duracao = TipoDuracaoEvento.getFromId(t.getTipoHorario());
        if (t.getTipoHorario() == TipoDuracaoEvento.DURACAO_PREDEFINIDA.ordinal()) {
            this.duracao_fixa = String.valueOf(t.getDuracao());
            this.duracao_intervalo_superior = String.valueOf(0);
            this.duracao_intervalo_inferior = String.valueOf(0);
        } else if (t.getTipoHorario() == TipoDuracaoEvento.INTERVALO_DE_TEMPO.ordinal()) {
            this.duracao_intervalo_superior = String.valueOf(t.getDuracaoMaxima());
            this.duracao_intervalo_inferior = String.valueOf(t.getDuracaoMinima());
            this.duracao_fixa = String.valueOf(0);
        }
        this.tipoEvento = false;
    }


    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public TipoAgendamentoEnum getComportamentoEnum() {
        return comportamentoEnum;
    }

    public void setComportamentoEnum(TipoAgendamentoEnum comportamentoEnum) {
        this.comportamentoEnum = comportamentoEnum;
    }

    public String getComportamento() {
        return comportamento;
    }

    public void setComportamento(String comportamento) {
        this.comportamento = comportamento;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDuracao_intervalo_inferior() {
        return duracao_intervalo_inferior;
    }

    public void setDuracao_intervalo_inferior(String duracao_intervalo_inferior) {
        this.duracao_intervalo_inferior = duracao_intervalo_inferior;
    }

    public String getDuracao_intervalo_superior() {
        return duracao_intervalo_superior;
    }

    public void setDuracao_intervalo_superior(String duracao_intervalo_superior) {
        this.duracao_intervalo_superior = duracao_intervalo_superior;
    }

    public PaletaCoresEnum getCorEnum() {
        return corEnum;
    }

    public void setCorEnum(PaletaCoresEnum corEnum) {
        this.corEnum = corEnum;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public TipoDuracaoEvento getTipo_duracao() {
        return tipo_duracao;
    }

    public void setTipo_duracao(TipoDuracaoEvento tipo_duracao) {
        this.tipo_duracao = tipo_duracao;
    }


    public Integer getNumero_agendamentos() {
        return numero_agendamentos;
    }

    public void setNumero_agendamentos(Integer numero_agendamentos) {
        this.numero_agendamentos = numero_agendamentos;
    }

    public Integer getDias() {
        return dias;
    }

    public void setDias(Integer dias) {
        this.dias = dias;
    }

    public Integer getIntervalo_minimo_caso_falta() {
        return intervalo_minimo_caso_falta;
    }

    public void setIntervalo_minimo_caso_falta(Integer intervalo_minimo_caso_falta) {
        this.intervalo_minimo_caso_falta = intervalo_minimo_caso_falta;
    }

    public Boolean getSomente_carteira_professor() {
        return somente_carteira_professor;
    }

    public void setSomente_carteira_professor(Boolean somente_carteira_professor) {
        this.somente_carteira_professor = somente_carteira_professor;
    }

    public String getDuracao_fixa() {
        return duracao_fixa;
    }

    public void setDuracao_fixa(String duracao_fixa) {
        this.duracao_fixa = duracao_fixa;
    }

    public Boolean getPermitir_app() {
        return permitir_app;
    }

    public void setPermitir_app(Boolean permitir_app) {
        this.permitir_app = permitir_app;
    }

    public ProfessorResponseTO getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorResponseTO professor) {
        this.professor = professor;
    }

    public String getInicio() {
        return inicio;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public String getFim() {
        return fim;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public Boolean getTipoEvento() {
        return tipoEvento;
    }

    public void setTipoEvento(Boolean tipoEvento) {
        this.tipoEvento = tipoEvento;
    }
}
