package br.com.pacto.controller.json.controller;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.colaborador.ColaboradorTO;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.usuario.UsuarioJSON;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.dto.UsuarioEncriptadoDTO;
import br.com.pacto.security.dto.UsuarioLoginAppDTO;
import br.com.pacto.security.dto.UsuarioLoginDTO;
import br.com.pacto.security.dto.UsuarioLoginV2DTO;
import br.com.pacto.security.service.LoginService;
import br.com.pacto.security.service.UsuarioAutenticadoDTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Controle de Login da aplicação rest
 *
 * <AUTHOR> Karlus
 * @since 19/07/2018
 */
@Controller
@RequestMapping("login")
public class LoginController {

    private final LoginService loginService;

    /**
     * @param loginService {@link LoginService}
     */
    @Autowired
    public LoginController(LoginService loginService) {
        this.loginService = loginService;
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> login(@RequestBody UsuarioLoginDTO usuarioDTO) {
        try {
            return ResponseEntityFactory.ok(loginDecode(usuarioDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    private String loginDecode(UsuarioLoginDTO usuarioDTO) throws ServiceException{
        try {
            return loginService.login(usuarioDTO.getChave(), URLDecoder.decode(usuarioDTO.getUsername(), "UTF-8"), usuarioDTO.getSenha(), true);
        }catch (Exception e){
            return loginService.login(usuarioDTO.getChave(), usuarioDTO.getUsername(), usuarioDTO.getSenha(), true);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/app", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> loginApp(@RequestBody UsuarioLoginDTO usuarioDTO) {
        try {
            return ResponseEntityFactory.ok(loginService.app(usuarioDTO.getChave(), usuarioDTO.getUsername()));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/ef92095f05323869f2d5e207faadb37a", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> loginAppCriptografado(@RequestBody String conteudoCriptografado) {
        try {
            String jsonDescriptografado = Uteis.decryptUserData(conteudoCriptografado);
            JSONObject obj = new JSONObject(jsonDescriptografado);

            String chave = obj.optString("chave");
            String username = obj.optString("username");
            String time = obj.optString("time");

            if (UteisValidacao.emptyString(time)) {
                throw new ServiceException("Parâmetro obrigatório 'time' não pode estar vazio.");
            }

            return ResponseEntityFactory.ok(Uteis.encryptUserData(loginService.app(chave, username)));
        } catch (Exception e) {
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, "Erro no loginApp", e);

            JSONObject erro = new JSONObject();
            try {
                erro.put("erro", e.getMessage());
            } catch (Exception ignored) {
                Logger.getLogger(this.getClass().getName()).log(Level.WARNING, "Error creating error JSON", e);
            }

            String encryptedError = "";
            try {
                encryptedError = Uteis.encryptUserData(erro.toString());
                String erroLoginApp = Uteis.encryptUserData("erro_login_app");
                return ResponseEntityFactory.erroInterno(erroLoginApp, encryptedError);
            } catch (Exception encryptionException) {
                Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, "Error encrypting error message", encryptionException);
                encryptedError = "{\"erro\": \"Erro interno ao criptografar\"}";
            }
            return ResponseEntityFactory.erroInterno("Erro ao criptografar", encryptedError);
        }
    }

    @ResponseBody
    @RequestMapping(value = "v3/app", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> loginApp(@RequestBody UsuarioLoginAppDTO usuarioDTO) {
        try {
            return ResponseEntityFactory.ok(loginService.app(usuarioDTO.getChave(), usuarioDTO.getCodUsuario()));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/validar-usuario-movel", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> validarUsuarioMovel(@RequestBody UsuarioLoginDTO usuarioDTO) {
        try {
            return ResponseEntityFactory.ok(validarUsuario(usuarioDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    private String validarUsuario(UsuarioLoginDTO usuarioDTO) throws ServiceException{
        try {
            return loginService.validarUsuarioMovel(usuarioDTO.getChave(),
                    URLDecoder.decode(usuarioDTO.getUsername(), "UTF-8"));
        }catch (Exception e){
            return loginService.validarUsuarioMovel(usuarioDTO.getChave(),
                    usuarioDTO.getUsername());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/gerar-token-usuario-zw", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> gerarTokenUsuarioZw(@RequestBody UsuarioEncriptadoDTO usuario) {
        try {
            return ResponseEntityFactory.ok(loginService.gerarTokenZw(usuario));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/dados", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> dados(@RequestBody UsuarioLoginDTO usuarioDTO, @RequestParam(required = false) Integer czw) {
        try {
            if(UteisValidacao.emptyNumber(czw)){
                return ResponseEntityFactory.ok(loginService.login(usuarioDTO.getChave(), usuarioDTO.getUsername(), usuarioDTO.getSenha()));
            }
            return ResponseEntityFactory.ok(loginService.usuarioZw(usuarioDTO.getChave(), czw));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/addsessao", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> dados(@RequestBody UsuarioAutenticadoDTO dto, @RequestParam String chave) {
        try {
            loginService.addUsuarioSessao(chave, dto);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/v2/dados", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> dadosV2(@RequestBody UsuarioLoginV2DTO dto) {
        try {
            return ResponseEntityFactory.ok(loginService.loginV2(dto));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/v2/app", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> loginAppSemSenha(@RequestBody UsuarioLoginDTO dto) {
        try {
            UsuarioService usuarioService = UtilContext.getBean(UsuarioService.class);
            Usuario u = usuarioService.validarUsuarioEmail(dto.getChave(), dto.getUsername(), false);
            UsuarioJSON uJSON = new UsuarioJSON(u, usuarioService.empresasApp(dto.getChave(), u));
            return ResponseEntityFactory.ok(uJSON.toJSON());
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/importacao/usuario/{chave}/{empresa}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> usuarioImportacao(@RequestBody ColaboradorTO colaboradorTO,
                                                                 @PathVariable String chave,
                                                                 @PathVariable Integer empresa,
                                                                 HttpServletRequest request) {
        try {
            UsuarioService usuarioService = UtilContext.getBean(UsuarioService.class);
            usuarioService.criarOuConsultarSeExisteImportacao(chave, colaboradorTO,
                    empresa, request);
            return ResponseEntityFactory.ok(loginService.login(chave, colaboradorTO.getAppUserName(), colaboradorTO.getAppPassword(), true));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno("erro_obter_usuario_importacao", e.getMessage());
        }
    }
}
