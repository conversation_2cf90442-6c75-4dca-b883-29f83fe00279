package br.com.pacto.controller.json.programa;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.avaliacao.AnamneseTreinoPorIADTO;
import br.com.pacto.bean.colaborador.FiltroColaboradorJSON;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.programa.*;
import br.com.pacto.controller.json.colaborador.ColaboradorController;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.intf.programa.PrescricaoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.json.ResultAlunoClienteSinteticoJSON;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.pacto.controller.json.base.SuperControle.STATUS_ERRO;
import static br.com.pacto.controller.json.base.SuperControle.STATUS_SUCESSO;

/**
 * Created by ulisses on 13/08/2018.
 */
@Controller
@RequestMapping("/psec/programas")
public class ProgramaTreinoController {

    private final ProgramaTreinoService programaTreinoService;
    private final PrescricaoService prescricaoService;

    @Autowired
    public ProgramaTreinoController(ProgramaTreinoService programaTreinoService,
                                    PrescricaoService prescricaoService
    ){
        Assert.notNull(programaTreinoService, "O serviço de programa treino não foi injetado corretamente");
        this.programaTreinoService = programaTreinoService;
        this.prescricaoService = prescricaoService;
    }
    @Autowired
    private HttpServletRequest request;

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/pre-definido", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarProgramasPreDefinidos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                              @RequestHeader(value = "user-agent", required = false) String userAgent,
                                                                              PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroProgramaTreinoJSON filtroProgramaTreinoJSON = new FiltroProgramaTreinoJSON(filtros);
            boolean app = UteisValidacao.emptyString(userAgent) || userAgent.toUpperCase().contains("TREINO_IOS");
            return ResponseEntityFactory.ok(programaTreinoService.obterProgramasPreDefinidos(paginadorDTO, filtroProgramaTreinoJSON, app), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar os programas pré-definidos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/pre-definido/slim", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarProgramasPreDefinidosSlim(@RequestParam String nomePrograma,
                                                                                  @RequestParam String rede,
                                                                                  @RequestParam String chaveRede) throws JSONException {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.obterProgramasPreDefinidosSlim(Boolean.valueOf(rede),
                    nomePrograma, chaveRede, false));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar os programas pré-definidos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarProgramaTreino(@PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.consultarProgramaTreino(id, null));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o programa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "{clientecodigo}/{id}/anterior", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarProgramaTreinoAnterior(@PathVariable("clientecodigo") Integer clienteCodigo,
                                                                               @PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.consultarProgramaTreinoAnterior(clienteCodigo,id, null));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o programa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarProgramaTreino(
            @RequestParam(value = "preDefinidoId", required = false) Integer preDefinidoId,
            @RequestParam(value = "chaveFranqueadora", required = false) String chaveFranqueadora,
            @RequestParam(value = "renovarAtual", required = false) Boolean renovarAtual,
            @RequestHeader(value = "empresaId", required = false) Integer empresaId,
            @RequestParam(value = "origem", required = false, defaultValue = "1") Integer origem,
            @RequestBody ProgramaTreinoTO programaTreinoTO
    ) {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.criarProgramaTreino(
                    empresaId,
                    programaTreinoTO, preDefinidoId,
                    chaveFranqueadora,
                    renovarAtual, request, origem));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar criar programa", e);
            if (UteisValidacao.emptyString(e.getMessage())) {
                String msgs = "";
                for (int i = 0; i < ((ValidacaoException) e).getMensagens().size(); i++) {
                    msgs += ((ValidacaoException) e).getMensagens().get(0);
                    msgs += ";";
                }
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), msgs);
            } else {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarProgramaTreino(@PathVariable("id") final Integer id,
                                                                     @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                     @RequestBody ProgramaTreinoTO programaTreinoTO) {
        try {
            programaTreinoTO.setId(id);
            return ResponseEntityFactory.ok(programaTreinoService.alterarProgramaTreino(empresaId, programaTreinoTO, request));

        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar programa", e);
            return ResponseEntityFactory.erroRegistroEstaSendoUtilizado(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/aprovar/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aprovarProgramaTreino(@PathVariable("id") final Integer id,
                                                                     @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                     @RequestBody ProgramaTreinoTO programaTreinoTO) {
        try {
            programaTreinoTO.setId(id);
            programaTreinoService.alterarEmRevisaoProfessor(id, programaTreinoTO.getEmRevisaoProfessor());
            return ResponseEntityFactory.ok(true);

        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar programa", e);
            return ResponseEntityFactory.erroRegistroEstaSendoUtilizado(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/conflitante", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> programaConflitante(@RequestParam("inicio") final Long inicio,
                                                                   @RequestParam("termino") Long termino,
                                                                   @RequestParam("alunoId") Integer alunoId,
                                                                   @RequestParam("programaId") Integer programaId) {
        try {
            ProgramaTreinoTO programaTreinoTO = new ProgramaTreinoTO();
            programaTreinoTO.setId(programaId);
            programaTreinoTO.setInicio(inicio);
            programaTreinoTO.setTermino(termino);
            programaTreinoTO.setAlunoId(alunoId);
            return ResponseEntityFactory.ok(programaTreinoService.programaConflitante(programaTreinoTO));

        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar programa", e);
            return ResponseEntityFactory.erroRegistroEstaSendoUtilizado(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/conflitante/colaborador", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> programaConflitanteColaborador(@RequestParam("inicio") final Long inicio,
                                                                              @RequestParam("termino") Long termino,
                                                                              @RequestParam("colaboradorId") Integer colaboradorId,
                                                                              @RequestParam("programaId") Integer programaId) {
        try {
            ProgramaTreinoTO programaTreinoTO = new ProgramaTreinoTO();
            programaTreinoTO.setId(programaId);
            programaTreinoTO.setInicio(inicio);
            programaTreinoTO.setTermino(termino);
            programaTreinoTO.setColaboradorId(colaboradorId);
            return ResponseEntityFactory.ok(programaTreinoService.programaConflitante(programaTreinoTO));

        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar programa", e);
            return ResponseEntityFactory.erroRegistroEstaSendoUtilizado(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/{id}/tornar-predefinido", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> tornarProgramaPreDefinido(@PathVariable("id") final Integer id) {
        try {
            programaTreinoService.tornarProgramaPreDefinido(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar colocar o programa como pré-definido", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirProgramaTreino(@PathVariable("id") final Integer id){
        try {
            programaTreinoService.excluirProgramaTreino(id, request);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir programa", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroEstaSendoUtilizado(e.getChaveExcecao(), e.getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/{id}/calcular-aulas-previstas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarCalculosAulasPrevista(
            @PathVariable("id") Integer programaId,
            @RequestParam(value = "campoAlterado") String campoAlterado,
            @RequestParam(value = "value") String value,
            @RequestParam(value = "inicio") Long inicio,
            @RequestParam(value = "termino") Long termino,
            @RequestParam(value = "totalTreinos") Integer totalTreinos,
            @RequestParam(value = "qtdDiasSemana") Integer qtdDiasSemana
    ) {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.updateCalculosAulasPrevistas(programaId, campoAlterado, value, inicio, termino, totalTreinos, qtdDiasSemana));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir programa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/atualizar-situacao-predefinido/{id}/{situacao}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ModelMap atualizarSituacaoProgramaPredefinido(@PathVariable("id") Integer id,
                                                         @PathVariable("situacao") Integer situacao) {
        ModelMap mm = new ModelMap();
        try {
            programaTreinoService.atualizarSituacaoProgramaPredefinido(id, situacao, request);
            mm.addAttribute(STATUS_SUCESSO);
        } catch (ServiceException e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar a situação do programa", e);
        }
        return mm;
    }

    @ResponseBody
    @RequestMapping(value = "/criar-predefinido", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarProgramaPreDefinido(@RequestBody ProgramaTreinoTO programaTreinoTO) {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.criarProgramaPreDefinido(programaTreinoTO, request));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar criar programa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/aluno-acompanhamento/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunoAcompanhamento(@PathVariable Integer codigoPessoa,
                                                                   HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.consultarAcompanhamento(codigoPessoa, request));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o programa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/lista-prescricao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaPrescricao(@RequestParam(value = "filters", required = false) String filtros,
                                                               @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                               PaginadorDTO paginadorDTO) {
        try {
            JSONObject filtrosJSON = filtros == null ? new JSONObject() : new JSONObject(filtros);
            return ResponseEntityFactory.ok(prescricaoService.listaPrescricao(empresaId, filtrosJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o programa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/lista-prescricaoV2", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaPrescricaoV2(@RequestParam(value = "filters", required = false) String filtros,
                                                               @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                               PaginadorDTO paginadorDTO) {
        try {
            JSONObject filtrosJSON = filtros == null ? new JSONObject() : new JSONObject(filtros);
            return ResponseEntityFactory.ok(prescricaoService.listaPrescricaoV2(empresaId, filtrosJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o programa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/matriculas-prescricao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> matriculasPrescricao(@RequestParam(value = "filters", required = false) String filtros,
                                                                    @RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            JSONObject filtrosJSON = filtros == null ? new JSONObject() : new JSONObject(filtros);
            return ResponseEntityFactory.ok(prescricaoService.matriculasPrescricao(empresaId, filtrosJSON));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o programa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/consultarGruposTrabalhadosPeriodo", method = RequestMethod.POST)
    public ResponseEntity<EnvelopeRespostaDTO> consultarGruposTrabalhadosPeriodo(@PathVariable String ctx,
                                                                                 @RequestParam String dataInicial,
                                                                                 @RequestParam String dataFinal,
                                                                                 @RequestParam Integer codigoCliente) throws Exception {

        final ModelMap modelMap = new ModelMap();
        ResultAlunoClienteSinteticoJSON result = new ResultAlunoClienteSinteticoJSON();
        List<Map<String, Object>> grupos = new ArrayList<>();

        try {
            return ResponseEntityFactory.ok(programaTreinoService.gruposMuscularesTrabalhadosPeriodo(ctx, dataInicial, dataFinal, codigoCliente));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar grupos musculares trabalhados por período", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping( value = "filtro-prescricao",method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaFiltroPrincipal(
            @RequestParam(value = "filters", required = false) JSONObject filters,
            PaginadorDTO paginadorDTO,
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            HttpServletRequest request
    ) throws JSONException {
        try {
            FiltroColaboradorJSON filtros = new FiltroColaboradorJSON(filters);
            return ResponseEntityFactory.ok(prescricaoService.filtroPrincipal(empresaId, filtros, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ENVIAR_TREINO_EM_MASSA)
    @RequestMapping(value = "/enviar/{codigoProgramaBase}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> enviarProgramaTreino(
            @PathVariable(value = "codigoProgramaBase") Integer codigoProgramaBase,
            @RequestHeader(value = "empresaId", required = false) Integer empresaId,
            @RequestBody List<Integer> clientes
    ) {
        try {
            programaTreinoService.enviarProgramaTreino(empresaId, codigoProgramaBase, clientes);
            return ResponseEntityFactory.ok("processando...");
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar criar programa", e);
            if (UteisValidacao.emptyString(e.getMessage())) {
                String msgs = "";
                for (int i = 0; i < ((ValidacaoException) e).getMensagens().size(); i++) {
                    msgs += ((ValidacaoException) e).getMensagens().get(0);
                    msgs += ";";
                }
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), msgs);
            } else {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/importar-predefinidos/{ctxOrigem}/{ctxDestino}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> importarProgramaPreDefinido(@PathVariable("ctxOrigem") String ctxOrigem,
                                                                           @PathVariable("ctxDestino") String ctxDestino) {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.importarProgramaPreDefinido(ctxOrigem, ctxDestino, request));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar importar programa predefinido", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/criaProgramaTreinoPorIA", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criaProgramaTreinoPorIA(@RequestBody AnamneseTreinoPorIADTO anamneseTreinoPorIADTO) {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.preparaProgramaTreinoPorIA(anamneseTreinoPorIADTO, null));
        } catch (Exception e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar criar programa por IA - ", e);
            return ResponseEntityFactory.erroInterno("erro.interno", e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/processos/atualizarBancoAtividadesIA", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarBancoAtividadesIA() {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.atualizarBancoAtividadesIA(null));
        } catch (Exception e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo ", e);
            return ResponseEntityFactory.erroInterno("erro.interno", e.getMessage());
        }
    }

}
