package br.com.pacto.controller.json.aluno;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.programa.FiltroProgramaTreinoJSON;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.cliente.ClientePesquisaService;
import br.com.pacto.service.intf.cliente.ClienteService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.cliente.ListaRapidaAcessosService;
import br.com.pacto.service.intf.gestao.DashboardBIService;
import br.com.pacto.service.intf.graduacao.GraduacaoService;
import br.com.pacto.service.intf.notificacao.NotificacaoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.swagger.respostas.ExemploRespostaVazia;
import br.com.pacto.swagger.respostas.acesso.ExemploRespostaConfigListaRapidaAcessoDTO;
import br.com.pacto.swagger.respostas.aluno.ExemploRespostaAlunoResponseTO;
import br.com.pacto.swagger.respostas.aluno.ExemploRespostaAlunoSimplesDTO;
import br.com.pacto.swagger.respostas.aluno.ExemploRespostaListAlunoResponseTOPaginacao;
import br.com.pacto.swagger.respostas.aluno.app.ExemploRespostaAlunoAppInfoDTO;
import br.com.pacto.swagger.respostas.aluno.avaliacao.ExemploRespostaAvaliacaoResponseTO;
import br.com.pacto.swagger.respostas.aluno.codigo.ExemploRespostaCodigoAluno;
import br.com.pacto.swagger.respostas.aluno.observacao.ExemploRespostaAlunoObservacaoDTO;
import br.com.pacto.swagger.respostas.aluno.observacao.ExemploRespostaListAlunoObservacaoDTO;
import br.com.pacto.swagger.respostas.aluno.pesquiisa.ExemploRespostaListAlunoPesquisaResponseTO;
import br.com.pacto.swagger.respostas.aluno.zw.ExemploRespostaListAlunoZWResponseDTO;
import br.com.pacto.swagger.respostas.app.ExemploRespostaConfirmacaoEmailTreino;
import br.com.pacto.swagger.respostas.atestado.ExemploRespostaAtestadoResponseTO;
import br.com.pacto.swagger.respostas.cliente.colaborador.ExemploRespostaListClienteColaboradoresDTO;
import br.com.pacto.swagger.respostas.cliente.mensagem.ExemploRespostaClienteMensagem;
import br.com.pacto.swagger.respostas.cliente.observacao.ExemploRespostaClienteObservacaoAnexosDTO;
import br.com.pacto.swagger.respostas.importar.ExemploRespostaImportarAluno;
import br.com.pacto.swagger.respostas.linhaTempo.ExemploRespostaListLinhaDeTempoAlunoResponseDTO;
import br.com.pacto.swagger.respostas.notificacao.ExemploRespostaNotificacaoDTO;
import br.com.pacto.swagger.respostas.treino.ExemploRespostaDetalheTreinoAlunoDTO;
import br.com.pacto.swagger.respostas.treino.ExemploRespostaListProgramaTreinoAlunoResponseDTOPaginacao;
import br.com.pacto.swagger.respostas.variaveis.ExemploRespostaBoolean;
import br.com.pacto.swagger.respostas.variaveis.ExemploRespostaListInteger;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.json.TipoLinhaEnum;
import br.com.pacto.webservice.TreinoWS;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.pacto.controller.json.base.SuperControle.STATUS_ERRO;

/**
 * Created by ulisses on 27/08/2018.
 */
@Controller
@RequestMapping("/psec/alunos")
public class AlunoController {

    private final ClienteSinteticoService clienteSinteticoService;
    private final ListaRapidaAcessosService listaRapidaAcessosService;
    private final ClienteService clienteService;
    private final ClientePesquisaService clientePesquisaService;
    private final NotificacaoService notificacaoService;
    private final ProgramaTreinoService programaTreinoService;
    private final GraduacaoService graduacaoService;

    @Autowired
    HttpServletRequest request;
    @Autowired
    private SessaoService sessaoService;

    @Autowired
    public AlunoController(ClienteSinteticoService clienteSinteticoService,
                           ClientePesquisaService clientePesquisaService,
                           NotificacaoService notificacaoService,
                           ClienteService clienteService,
                           ProgramaTreinoService programaTreinoService,
                           ListaRapidaAcessosService listaRapidaAcessosService,
                           GraduacaoService graduacaoService) {
        this.clienteService = clienteService;
        Assert.notNull(clienteSinteticoService, "O serviço de cliente sintético não foi injetado corretamente");
        Assert.notNull(clienteSinteticoService, "O serviço de pesquisa de cliente não foi injetado corretamente");
        this.clienteSinteticoService = clienteSinteticoService;
        this.clientePesquisaService = clientePesquisaService;
        this.notificacaoService = notificacaoService;
        this.programaTreinoService = programaTreinoService;
        this.graduacaoService = graduacaoService;
        this.listaRapidaAcessosService = listaRapidaAcessosService;
    }

    private JSONObject desencriptarPlayloadZWUI(String content) throws Exception {
        try {
            JSONObject jsonObject = new JSONObject(content);
            String encryptedData = jsonObject.getString("data");
            String decryptedContent = Uteis.desencriptarZWUI(encryptedData);
            JSONObject data = new JSONObject(decryptedContent);
            return data;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("Não foi possível ler os dados recebidos");
        }
    }

    @ApiOperation(
            value = "Cadastrar aluno",
            notes = "Cadastra um aluno na academia. O aluno é um cliente que já adquiriu um plano da academia.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAlunoResponseTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarAluno(
            @ApiParam(value = "Informações para o cadastro do aluno")
            @RequestBody AlunoDTO AlunoDTO,
            @ApiParam(value = "Código da empresa que o aluno será vinculado", defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.cadastrarAluno(null, AlunoDTO, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/p9Q2t9stT45sc9v3RT85s6dgFifbjuoHFUf0ykjGyio", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterUmAlunoCrypt(@RequestBody String content,
                                                                 @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                 HttpServletRequest request) {
        try {
            JSONObject data = desencriptarPlayloadZWUI(content);

            AlunoResponseTO alunoResponseTO = clienteSinteticoService.obterUmAluno(data.optInt("id"), null, empresaId, request);
            String jsonReturn = new JSONObject(alunoResponseTO).toString();

            return ResponseEntityFactory.ok(Uteis.encriptarZWUI(jsonReturn));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(STATUS_ERRO, e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar um aluno",
            notes = "Consulta as informações de um aluno da academia. O aluno é um cliente que já adquiriu um plano da academia.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAlunoResponseTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterUmAluno(@ApiParam(value = "Código do aluno que será consultado", defaultValue = "123", required = true)
                                                            @PathVariable("id") Integer alunoID,
                                                            @ApiParam(value = "Código da empresa que o aluno está vinculado", defaultValue = "1", required = true)
                                                            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                            HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterUmAluno(alunoID, null, empresaId, request));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Fazer download do atestado de um aluno",
            notes = "Faz o download do atestado de um aluno. O aluno é um cliente que já adquiriu um plano da academia.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{id}/{atestadoId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> downloadArquivo(@PathVariable("id") Integer alunoID,
                                                               @PathVariable("atestadoId") Integer atestadoId){
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.downloadAnexoAtestado(alunoID, atestadoId));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter arquivo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Consultar alunos da academia",
            notes = "Consulta os alunos da academia, podendo aplicar filtros durante a busca. O aluno é um cliente que já adquiriu um plano da academia.",
            tags = "Clientes"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListAlunoResponseTOPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaAlunos(
            @ApiParam(value = "Código da empresa que os alunos estão vinculados", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,

            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome ou e-mail do aluno.\n" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\", \"email\"]).\n" +
                    "- <strong>niveisIds:</strong> Filtra por um ou mais níveis de aluno (Deve ser informado como uma lista de códigos/IDs ex: [1, 2]).\n" +
                    "- <strong>colaboradorIds:</strong> Filtra por um ou mais colaboradores (carteira) do aluno (Deve ser informado como uma lista de códigos/IDs ex: [10, 15]).\n" +
                    "- <strong>situacoesEnuns:</strong> Filtra pela situação do aluno (Deve ser informado como uma lista de códigos de 2 letras ex: [\"AT\", \"IN\"]).\n" +
                    "- <strong>statusClienteEnuns:</strong> Filtra pelo status do CRM do aluno (Deve ser informado como uma lista ex: [\"EM_ATENDIMENTO\", \"CONVERTIDO\"]).",
                    defaultValue = "{\"quicksearchValue\":\"Silva\", \"quicksearchFields\":[\"nome\"], \"niveisIds\":[1], \"colaboradorIds\":[10], \"situacoesEnuns\":[\"AT\"], \"statusClienteEnuns\":[\"CONVERTIDO\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,

            @ApiParam(value = "Indica se permite que a consulta busque por alunos de outra empresa da rede", defaultValue = "false")
            @RequestParam(value = "permiteAlunoOutraEmpresa", required = false) Boolean permiteAlunoOutraEmpresa,

            @ApiParam(value = "Indica se permite incluir alunos autorizados", defaultValue = "false")
            @RequestParam(value = "incluirAutorizado", required = false) Boolean incluirAutorizado,

            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request) throws JSONException {
        try {
            FiltroAlunoJSON filtroAlunoJSON = new FiltroAlunoJSON(filtros);
            return ResponseEntityFactory.ok(clienteSinteticoService.listaAlunos(filtroAlunoJSON, paginadorDTO, request, empresaId, permiteAlunoOutraEmpresa, incluirAutorizado), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar alunos e passivos",
            notes = "Consulta os alunos e passivos de uma academia, podendo aplicar filtros durante a busca. O aluno é um cliente que já adquiriu um plano da academia.",
            tags = "Clientes"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListAlunoResponseTOPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/fila-espera", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaAlunosAndPassivos(
            @ApiParam(value = "Código da empresa que os alunos estão vinculados", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,

            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome ou e-mail do aluno.\n" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\", \"email\"]).\n" +
                    "- <strong>niveisIds:</strong> Filtra por um ou mais níveis de aluno (Deve ser informado como uma lista de códigos/IDs ex: [1, 2]).\n" +
                    "- <strong>colaboradorIds:</strong> Filtra por um ou mais colaboradores (carteira) do aluno (Deve ser informado como uma lista de códigos/IDs ex: [10, 15]).\n" +
                    "- <strong>situacoesEnuns:</strong> Filtra pela situação do aluno (Deve ser informado como uma lista de códigos de 2 letras ex: [\"AT\", \"IN\"]).\n" +
                    "- <strong>statusClienteEnuns:</strong> Filtra pelo status do CRM do aluno (Deve ser informado como uma lista ex: [\"EM_ATENDIMENTO\", \"CONVERTIDO\"]).",
                    defaultValue = "{\"quicksearchValue\":\"Silva\", \"quicksearchFields\":[\"nome\"], \"niveisIds\":[1], \"colaboradorIds\":[10], \"situacoesEnuns\":[\"AT\"], \"statusClienteEnuns\":[\"CONVERTIDO\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,

            @ApiParam(value = "Indica se permite que a consulta busque por alunos de outra empresa da rede", defaultValue = "false")
            @RequestParam(value = "permiteAlunoOutraEmpresa", required = false) Boolean permiteAlunoOutraEmpresa,


            @RequestParam(value = "incluirAutorizado", required = false) Boolean incluirAutorizado,
            PaginadorDTO paginadorDTO,
            HttpServletRequest request) throws JSONException {
        try {
            List<AlunoResponseTO> retorno = new ArrayList<>();
            FiltroAlunoJSON filtroAlunoJSON = new FiltroAlunoJSON(filtros);
            List<AlunoResponseTO> passivos = clienteSinteticoService.listaPassivosFila(filtroAlunoJSON, empresaId);
            List<AlunoResponseTO> alunos = clienteSinteticoService.listaAlunos(filtroAlunoJSON, paginadorDTO, request, empresaId, permiteAlunoOutraEmpresa, incluirAutorizado);
            retorno.addAll(alunos);
            retorno.addAll(passivos);
            paginadorDTO.setSize((long) retorno.size());
            paginadorDTO.setQuantidadeTotalElementos(paginadorDTO.getQuantidadeTotalElementos() + ((long) passivos.size()));
            return ResponseEntityFactory.ok(retorno, paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Atualizar aluno",
            notes = "Atualiza as informações de um aluno. O aluno é um cliente que já adquiriu um plano da academia.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAlunoResponseTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarAluno(
            @ApiParam(value = "Código do aluno que será atualizado", defaultValue = "123", required = true)
            @PathVariable("id") final Integer id,
            @ApiParam(value = "Informações que serão atualizados do aluno")
            @RequestBody AlunoDTO AlunoDTO) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.alterarAluno(id, AlunoDTO, request));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }


    @ApiOperation(
            value = "Deletar aluno",
            notes = "Deleta as informações de um aluno. O aluno é um cliente que já adquiriu um plano da academia.",
            tags = "Clientes"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida). Não retorna corpo de resposta.", response = ExemploRespostaVazia.class)
    )
    @ResponseBody
    @RequestMapping(value = "{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> deletarAluno(
            @ApiParam(value = "Código do cliente que será deletado", defaultValue = "123", required = true)
            @PathVariable("id") Integer id) {
        try {
            clienteSinteticoService.deletarUmAluno(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Atualizar situação de um aluno",
            notes = "Atualiza a situação de um aluno. O aluno é um cliente que já adquiriu um plano da academia.",
            tags = "Clientes"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida). Não retorna corpo de resposta.", response = ExemploRespostaVazia.class)
    )
    @ResponseBody
    @RequestMapping(value = "/situacao/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> editarSituacaoAluno(@ApiParam(value = "Código do aluno que terá a situação atualizada", defaultValue = "123", required = true)
                                                                   @PathVariable("id") Integer id,
                                                                   @ApiParam(value = "Informações para atualização da situação do aluno. (Enviar apenas a situação, os outros dados serão ignorados)")
                                                                   @RequestBody AlunoDTO alunoDTO) {
        try {
            alunoDTO.setCodigo(id);
            clienteSinteticoService.editarSituacao(alunoDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Consultar informações simplificadas de todos os alunos",
            notes = "Consulta as informações simplificadas de todos os alunos. O aluno é um cliente que já adquiriu um plano da academia.",
            tags = "Clientes"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaAlunoResponseTO.class)
    )
    @ResponseBody
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> all() {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.all());
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Consultar avaliação física de um aluno",
            notes = "Consulta as informações da avaliação física de um aluno. O aluno é um cliente que já adquiriu um plano da academia.",
            tags = "Avaliação Física"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaAvaliacaoResponseTO.class)
    )
    @ResponseBody
    @RequestMapping(value = "/perfil/avaliacao-fisica/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliacoesFisicaAluno(
            @ApiParam(value = "Código do aluno que a avaliação física será consultada", required = true, defaultValue = "1")
            @PathVariable("id") Integer id
    ) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.carregarAvaliacoesAluno(id));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao carregar avaliações fisica do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }

    }


    @ApiOperation(
            value = "Consultar linha do tempo de um aluno",
            notes = "Consulta as informações da linha do tempo de um aluno. A linha do tempo traz dados sobre dias que o aluno treinou, revisou o treino, renovou o treino, entre outras. O aluno é um cliente que já adquiriu um plano da academia.",
            tags = "Clientes"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaListLinhaDeTempoAlunoResponseDTO.class)
    )
    @ResponseBody
    @RequestMapping(value = "/linha-tempo/{mat}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> linhaDeTempoAluno(
            @ApiParam(value = "Código da matrícula do aluno que será consultado", required = true, defaultValue = "3123")
            @PathVariable("mat") Integer mat,
            @ApiParam(value = "Lista de tipos de eventos que serão consultados.\n\n" +
                    "<strong>Valores disponíveis</strong>\n" +
                    "- 0 - TREINOU\n" +
                    "- 1 - AGENDAMENTO\n" +
                    "- 2 - REVISOU_TREINO\n" +
                    "- 3 - RENOVOU_TREINO\n" +
                    "- 4 - ACABOU_TREINO\n" +
                    "- 5 - NOTIFICACAO\n" +
                    "- 6 - MONTOU_TREINO\n" +
                    "- 7 - MUDOU_DE_NIVEL\n" +
                    "- 8 - GANHOU_BADGE\n" +
                    "- 9 - FEZ_AULA\n" +
                    "- 10 - REALIZOU_AVALIACAO\n" +
                    "- 11 - REGISTROU_WOD\n" +
                    "- 12 - AGENDOU_BOOKING_GYMPASS\n" +
                    "- 13 - ALTERACAO_AGENDAMENTO_SERVICOS\n" +
                    "- 14 - AULA_DESMARCADA", defaultValue = "[0, 1]")
            @RequestParam(value = "tiposEvento", required = false) List<TipoLinhaEnum> tiposEvento,
            @ApiParam(value = "Data de início que será filtrado a linha do tempo (Formato timestamp em milissegundos)", defaultValue = "1733875200000")
            @RequestParam(value = "dataInicio", required = false) Long dataInicio,
            @ApiParam(value = "Data de fim que será filtrado a linha do tempo (Formato timestamp em milissegundos)", defaultValue = "1728681600000")
            @RequestParam(value = "dataFim", required = false) Long dataFim
    ) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.carregarLinhaDeTempoAluno(mat, new FiltroLinhaTempoDTO(tiposEvento, dataInicio, dataFim)));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao carregar linha de tempo do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Alterar data de avaliação física de um aluno.",
            notes = "Altera a data de avaliação física de um aluno. O aluno é um cliente que já adquiriu um plano da academia.",
            tags = "Avaliação Física"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaListLinhaDeTempoAlunoResponseDTO.class)
    )
    @ResponseBody
    @RequestMapping(value = "/perfil/avaliacao-fisica/{avaliacaoId}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarDataAvaliacao(
            @ApiParam(value = "Código da avaliação que terá a data alterada", defaultValue = "134", required = true)
            @PathVariable("avaliacaoId") Integer avaliacaoId,
            @ApiParam(value = "Data que a avaliação será realizada")
            @RequestBody AvaliacaoAlunoDTO avaliacaoAlunoDTO
    ) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.alterarDataAvaliacaoFisica(avaliacaoId, avaliacaoAlunoDTO));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao carregar linha de tempo do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/rJ6urBkIMHhEfn0jpIfrUKtbWTbTN4MXAJxAsyJW9KB", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE) //
    public ResponseEntity<EnvelopeRespostaDTO> detalhesTreinoAlunoCrypt(@RequestBody String content) {
        try {
            JSONObject data = desencriptarPlayloadZWUI(content);

            DetalheTreinoAlunoDTO detalheTreinoAlunoDTO = clienteSinteticoService.detalhesTreinamentoAluno(data.optInt("alunoId"));
            String jsonReturn = new JSONObject(detalheTreinoAlunoDTO).toString();

            return ResponseEntityFactory.ok(Uteis.encriptarZWUI(jsonReturn));
        } catch (ServiceException e) {
            if (Objects.equals(e.getMessage(), "Ocorreu um erro ao pesquisar o Programa Treino informado")) {
                return ResponseEntityFactory.erroNoContent(e.getChaveExcecao(), e.getMessage());
            } else {
                Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao carregar detalhes do treino do aluno", e);
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            }
        } catch (Exception e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao carregar detalhes do treino do aluno", e);
            return ResponseEntityFactory.erroInterno(STATUS_ERRO, e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar programa de treino de um aluno.",
            notes = "Consulta as informações do programa de treino de um aluno. O aluno é um cliente que já adquiriu um plano da academia.",
            tags = "Programa de Treino"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaDetalheTreinoAlunoDTO.class)
    )
    @ResponseBody
    @RequestMapping(value = "/perfil/programa-treino/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalhesTreinoAluno(
            @ApiParam(value = "Código do aluno que será consultado o programa de treino do aluno", required = true, defaultValue = "354")
            @PathVariable("id") Integer alunoID) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.detalhesTreinamentoAluno(alunoID));
        } catch (ServiceException e) {
            if (e.getMessage() == "Ocorreu um erro ao pesquisar o Programa Treino informado") {
                return ResponseEntityFactory.erroNoContent(e.getChaveExcecao(), e.getMessage());
            } else {
                Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao carregar detalhes do treino do aluno", e);
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Reenviar confirmação de email para o aluno para acesso do App Treino.",
            notes = "Reenvia a confirmação de email para o aluno acessar o App Treino.",
            tags = "App Treino"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaConfirmacaoEmailTreino.class)
    )
    @ResponseBody
    @RequestMapping(value = "/{id}/reenviar-confirmacao-app", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> reenviarUserApp(
            @ApiParam(value = "Código do aluno que será reenviado a confirmação", required = true, defaultValue = "354")
            @PathVariable("id") Integer id) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.reenviarConfirmacaoApp(id));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao reenviar usuário do aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }


    @ApiOperation(
            value = "Reenviar confirmação de email para o aluno para acesso do App Treino.",
            notes = "Reenvia a confirmação de email para o aluno acessar o App Treino.",
            tags = "App Treino"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaConfirmacaoEmailTreino.class)
    )
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ALUNOS)
    @RequestMapping(value = "/buscar-aluno-olympia/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarAlunoOlympia(@PathVariable() String id) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.buscarAlunoOlympia(id));
        } catch (ServiceException ex) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Error Ao tentar busacar aluno Olympia.");
            switch (ex.getCodigoError()) {
                case 400:
                    return ResponseEntityFactory.erroConhecido(ex.getChaveExcecao(), ex.getMessage());
                default:
                    return ResponseEntityFactory.erroInterno(ex.getChaveExcecao(), ex.getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Consultar observações de um aluno.",
            notes = "Consulta as observações de um aluno.",
            tags = "Clientes"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaListAlunoObservacaoDTO.class)
    )
    @ResponseBody
    @RequestMapping(value = "/{id}/observacoes", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarObservacao(
            @ApiParam(value = "Código do aluno que será consultado as observações. Pode ser o código identificador do aluno, o código de matrícula do aluno ou o código do aluno no sistema ZW.", required = true, defaultValue = "124")
            @PathVariable("id") final Integer id,
            @ApiParam(value = "Informa se é o código do cliente no ZW", defaultValue = "false")
            @RequestParam(required = false) Boolean codigoClienteZw,
            @ApiParam(value = "Informa se o código digitado é o código de matrícula", required = true, defaultValue = "false")
            @RequestParam(required = false) Boolean matricula) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterObservacoes(id, codigoClienteZw, matricula));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir a observacao", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Cadastrar observação de um aluno.",
            notes = "Cadastra uma observação de um aluno.",
            tags = "Clientes"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaListAlunoObservacaoDTO.class)
    )
    @ResponseBody
    @RequestMapping(value = "/{id}/observacoes", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarObservacao(@ApiParam(value = "Código identificador do aluno que será cadastrada a observação", required = true, defaultValue = "45")
                                                                   @PathVariable("id") final Integer id,
                                                                   @ApiParam(value = "Informações para o cadastro da observação")
                                                                   @RequestBody AlunoObservacaoDTO alunoObservacaoDTO) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.gravarObservacao(id, alunoObservacaoDTO));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir a observacao", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Deletar uma observação de um aluno.",
            notes = "Exclui as informações de uma observação.",
            tags = "Clientes"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida). Não retorna corpo de resposta.", response = ExemploRespostaVazia.class)
    )
    @ResponseBody
    @RequestMapping(value = "/observacoes/{idObservacao}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> deletarObservacao(@ApiParam(value = "Código identificador da observação", required = true, defaultValue = "123")
                                                                 @PathVariable("idObservacao") final Integer idObservacao) {
        try {
            clienteSinteticoService.deletarObservacao(idObservacao);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir a observacao", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar observações de aluno que foram registradas com um anexo.",
            notes = "Consulta as observações de um aluno que foram registradas com um anexo.",
            tags = "Clientes"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
    })
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaListAlunoObservacaoDTO.class)
    )
    @ResponseBody
    @RequestMapping(value = "/{id}/anexosAvaliacao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarObservacaoAnexos(@ApiParam(value = "Código identificador do aluno que será consultado as observações", required = true, defaultValue = "4231")
                                                                      @PathVariable("id") final Integer id,
                                                                      @ApiParam(value = "Indica se deve buscar o anexo no Zw", required = true, defaultValue = "false")
                                                                      @RequestParam(required = false) Boolean buscarAnexoZw,
                                                                      @ApiIgnore PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterObservacoesAnexos(id, buscarAnexoZw, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir a observacao", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar observações de aluno que foram registradas com um anexo.",
            notes = "Consulta as observações de um aluno que foram registradas com um anexo.",
            tags = "Clientes"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
    })
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaListAlunoObservacaoDTO.class)
    )
    @ResponseBody
    @RequestMapping(value = "/{id}/anexosAvaliacao/tela-aluno", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarObservacaoAnexosDocumentos(@ApiParam(value = "Código identificador do aluno que será consultado as observações", required = true, defaultValue = "4231")
                                                                                @PathVariable("id") final Integer id,
                                                                                @ApiIgnore PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterObservacoesAnexos(id, true, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir a observacao", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Cadastrar observação com anexos da avaliação física.",
            notes = "Cadastra uma observação contendo em anexo informações sobre a avaliação física de um cliente.",
            tags = "Clientes"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaAlunoObservacaoDTO.class)
    )
    @ResponseBody
    @RequestMapping(value = "/{id}/anexosAvaliacao", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarObservacaoAtestado(@ApiParam(value = "Código identificador do aluno que será cadastrado a observação", required = true, defaultValue = "4231")
                                                                           @PathVariable("id") final Integer id,
                                                                           @ApiParam(value = "Informações da observação do aluno que serão cadastradas")
                                                                           @RequestBody AlunoObservacaoDTO alunoObservacaoDTO) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.gravarObservacaoAnexos(id, alunoObservacaoDTO));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir a observacao", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar anexo de observação de avaliação física de um aluno.",
            notes = "Consulta um anexo de observação de avaliação física de um aluno.",
            tags = "Clientes"
    )

    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaClienteObservacaoAnexosDTO.class)
    )
    @ResponseBody
    @RequestMapping(value = "/{alunoId}/anexosAvaliacao/{anexoId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterObservacaoAnexos(@ApiParam(value = "Código identificador do aluno que foi cadastrada a avaliação", required = true, defaultValue = "123")
                                                                     @PathVariable("alunoId") final Integer alunoId,
                                                                     @ApiParam(value = "Código identificador do anexo que será consultado", required = true, defaultValue = "34")
                                                                     @PathVariable("anexoId") final Integer anexoId) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterObservacaoAnexos(anexoId));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir a observacao", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Deletar anexo de observação de um aluno.",
            notes = "Deleta um anexo de observação de um aluno.",
            tags = "Clientes"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida). Não retorna corpo de resposta.", response = ExemploRespostaVazia.class)
    )
    @ResponseBody
    @RequestMapping(value = "/anexosAvaliacao/{idAnexoAvaliacao}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> deletarObservacaoAnexos(@ApiParam(value = "Código da observação que será excluída", defaultValue = "123", required = true)
                                                                       @PathVariable("idAnexoAvaliacao") final Integer idObservacao) {
        try {
            clienteSinteticoService.deletarObservacao(idObservacao);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir a observacao", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Consultar uma mensagem de cliente.",
            notes = "Consulta uma mensageme de cliente.",
            tags = "Cliente Mensagem"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaVazia.class)
    )
    @ResponseBody
    @RequestMapping(value = "/{id}/{tipoMensagem}/mensagem", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarClienteMensagem(@ApiParam(value = "Código identificador do cliente", required = true, defaultValue = "124")
                                                                     @PathVariable("id") final Integer id,
                                                                     @ApiParam(value = "Tipo da mensagem que será consultada.\n\n" +
                                                                             "<strong>Tipo da mensagem</strong>\n" +
                                                                             "- 0 (Nenhum)\n" +
                                                                             "- 1 (Aviso do Consultor)\n" +
                                                                             "- 2 (Catraca)" +
                                                                             "- 3 (Aviso Médico)" +
                                                                             "- 4 (Boletim)" +
                                                                             "- 5 (Dados Incompletos)" +
                                                                             "- 6 (Parcela em Atraso)" +
                                                                             "- 7 (Risco)" +
                                                                             "- 8 (Objetivo Curto)" +
                                                                             "- 9 (Observação)" +
                                                                             "- 10 (Observação do cliente)" +
                                                                             "- 11 (Cartão Vencido)" +
                                                                             "- 12 (Aluguel de armário vencido e chave não devolvida)" +
                                                                             "- 13 (Estorno)", required = true, defaultValue = "1", allowableValues = "0,1,2,3,4,5,6,7,8,9,10,11,12,13")
                                                                     @PathVariable("tipoMensagem") final String tipoMensagem) {
        return ResponseEntityFactory.ok(clienteSinteticoService.obterClienteMensagem(null, id, tipoMensagem));
    }

    @ApiOperation(
            value = "Consultar anexos dos atestados médicos de um aluno",
            notes = "Consulta o anexo de um atestado médico de um aluno.",
            tags = "Clientes com Atestado"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaAtestadoResponseTO.class)
    )
    @ResponseBody
    @RequestMapping(value = "/{id}/atestados", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarAnexosAtestados(
            @ApiParam(value = "Código do cliente que será consultado os atestados", required = true, defaultValue = "123")
            @PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterAnexosAtestados(id));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir a observacao", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Consultar notificações enviadas para um professor sobre um aluno",
            notes = "Retorna as notificações enviadas para um professor pelo TreinoWeb sobre um aluno, ordenadas da mais recente para a mais antiga.",
            tags = "Cliente Mensagem"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
    })
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaNotificacaoDTO.class)
    )
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ALUNOS)
    @RequestMapping(value = "/{id}/notificacoesAluno", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarNotificacoes(
            @ApiParam(value = "Código do aluno que será consultado as notificações", required = true, defaultValue = "313")
            @PathVariable("id") Integer id,
            @ApiIgnore PaginadorDTO paginadorDTO
    ) {
        try {
            return ResponseEntityFactory.ok(notificacaoService.getNotificacoesAlunos(id, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao carregar notificacões do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }

    }

    @ApiOperation(
            value = "Validar se o cadastro de cliente está integrado ao sistema Olympia",
            notes = "Valida se o cliente está cadastrado no sistema Olympia ",
            tags = "Clientes"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaBoolean.class)
    )
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ALUNOS)
    @RequestMapping(value = "/validar-cadastro-olympia/{codigoExterno}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> validarCadastroOlympia(@ApiParam(value = "Código externo do cliente", defaultValue = "123", required = true)
                                                                      @PathVariable() String codigoExterno) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.validarCadastroOlympia(codigoExterno));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar validar cadastro Olympia");
            switch (e.getCodigoError()) {
                case 400:
                    return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getMessage());
                default:
                    return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            }
        }
    }


    @ApiOperation(
            value = "Consultar aluno no ZW",
            notes = "Consulta as informações de um aluno no sistema ZW.",
            tags = "Clientes"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaListAlunoZWResponseDTO.class)
    )
    @ResponseBody
    @RequestMapping(value = "/aluno-zw", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarAlunoZW(
            @ApiParam(value = "Filtra os alunos pelo nome", defaultValue = "Joana")
            @RequestParam(value = "nome", required = false) String nome,
            @ApiParam(value = "Filtra os alunos pelo CPF", defaultValue = "123.456.789-10")
            @RequestParam(value = "cpf", required = false) String cpf,
            @ApiParam(value = "Filtra os alunos pelo código de matrícula", defaultValue = "998877")
            @RequestParam(value = "matricula", required = false) Integer matricula,
            @ApiParam(value = "Código identificador da empresa que o cliente está vinculado", defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterAlunoZW(nome, cpf, matricula, empresaId, request));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao importar aluno do ZW", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else if (e.getCause().getMessage().equals("informe_um_filtro")) {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Cadastrar aluno no sitema ZW",
            notes = "Cadastra um aluno no sistema ZW. Se o aluno for cadastrado irá retornar um content contendo true.",
            tags = "Clientes"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaBoolean.class)
    )
    @ResponseBody
    @RequestMapping(value = "/aluno-zw", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarAlunoZW(@ApiParam(value = "Informações para cadastro do aoluno no sistema ZW")
                                                                @RequestBody AlunoZWDTO alunoZWDTO) {
        try {
            clienteSinteticoService.cadastrarAlunoZW(alunoZWDTO);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro reenviar usuário do aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }

    }


    @ApiOperation(
            value = "Cadastrar login para um aluno",
            notes = "Cadastra um login para um aluno. Se o aluno for cadastrado irá retornar um content contendo true.",
            tags = "Clientes"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaBoolean.class)
    )
    @ResponseBody
    @RequestMapping(value = "/usuario", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarUsuarioMovel(@ApiParam(value = "Informações para cadastrar o login de um aluno")
                                                                 @RequestBody UsuarioSimplesAlunoDTO usuarioSimplesAlunoDTO) {
        try {
            clienteSinteticoService.criarUsuarioMovelAluno(usuarioSimplesAlunoDTO);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao criar usuário movel do aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }

    }

    @ApiOperation(
            value = "Gerar PDF de avaliação de progresso de um aluno",
            notes = "Gera um PDF de avaliação do progresso de um aluno.",
            tags = "Clientes"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaBoolean.class)
    )
    @ResponseBody
    @RequestMapping(value = "/gerar-pdf-avaliacao-progresso-aluno", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> gerarPdfAvaliacaoProgressoAluno(@ApiParam(value = "Código da matrícula do cliente que vai ser gerado a avaliação", required = true, defaultValue = "123")
                                                                               @RequestParam(value = "matricula", required = true) Integer matricula,
                                                                               @ApiParam(value = "Código identificador do aluno", required = true, defaultValue = "443")
                                                                               @RequestParam(value = "alunoId", required = true) Integer alunoId,
                                                                               @ApiParam(value = "Código da ficha do aluno", required = true, defaultValue = "445")
                                                                               @RequestParam(value = "fichaId", required = true) Integer fichaId,
                                                                               @ApiParam(value = "Código da avaliação respondida do aluno", required = true, defaultValue = "422")
                                                                               @RequestParam(value = "avaliacaoAlunoRespondidaId", required = true) Integer avaliacaoAlunoRespondidaId,
                                                                               @ApiIgnore HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(graduacaoService.gerarPdfAvaliacaoProgressoV2(matricula, alunoId, fichaId, avaliacaoAlunoRespondidaId, request));
        } catch (Exception ex) {
            return ResponseEntityFactory.erroInterno(STATUS_ERRO, ex.getMessage());
        }
    }

    @ApiOperation(
            value = "Pesquisar aluno cadastrado na academia",
            notes = "Pesquisa no sistema as informações de um aluno cadastrado na academia através do código de matrícula, nome ou email dele. A lista de alunos é ordenada pelo nome de forma ascendente.",
            tags = "Clientes"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
    })
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaListAlunoPesquisaResponseTO.class)
    )
    @ResponseBody
    @RequestMapping(value = "/search", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaAlunosPesquisa(
            @ApiParam(value = "Código identificador da empresa", required = true, defaultValue = "134")
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiParam(value = "Filtros de busca. Filtra pelo nome do aluno, email ou código da matrícula dele", required = true, defaultValue = "joana")
            @RequestParam(value = "parametro", required = true) String parametro,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request) throws JSONException {
        try {
            return ResponseEntityFactory.ok(clientePesquisaService.listaAlunos(parametro, paginadorDTO, request, empresaId), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar código identificador de um aluno pelo código de matrícula dele",
            notes = "Consulta o código identificador de um aluno pelo código de matrícula dele.",
            tags = "Clientes"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaCodigoAluno.class)
    )
    @ResponseBody
    @RequestMapping(value = "/matricula/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterUmAlunoMatricula(@ApiParam(value = "Código da matrícula do aluno", required = true, defaultValue = "123")
                                                                     @PathVariable("id") Integer alunoMatricula,
                                                                     @ApiParam(value = "Código da empresa que o aluno está vinculado", required = true, defaultValue = "1")
                                                                     @RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterSomenteCodigo(alunoMatricula, true));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/Qer3Rrr4xa55Xgy1t0SecbtrtpaErETa9VZxVBhtQqs", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterAlunoPorMatriculaCrypt(@RequestBody String content,
                                                                           @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                           HttpServletRequest request) {
        try {
            JSONObject data = desencriptarPlayloadZWUI(content);

            AlunoResponseTO alunoResponseTO = clienteSinteticoService.obterUmAluno(null, data.getInt("matricula"), empresaId, request);
            String jsonReturn = new JSONObject(alunoResponseTO).toString();

            return ResponseEntityFactory.ok(Uteis.encriptarZWUI(jsonReturn));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(STATUS_ERRO, e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar aluno por código de matrícula",
            notes = "Consulta as informações de um aluno pelo código de matrícula dele.",
            tags = "Clientes"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).",
                    response = ExemploRespostaAlunoResponseTO.class)
    )
    @ResponseBody
    @RequestMapping(value = "/obter-aluno-completo-por-matricula/{matricula}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterAlunoPorMatricula(@ApiParam(value = "Código identificador do aluno que será consultado", required = true, defaultValue = "123")
                                                                      @PathVariable("matricula") Integer matricula,
                                                                      @ApiParam(value = "Código da empresa que o aluno está vinculado", required = true, defaultValue = "1")
                                                                      @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                      @ApiIgnore HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterUmAluno(null, matricula, empresaId, request));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar programas de treinos dos alunos",
            notes = "Este métdo retorna os dados do treino do aluno cuja matrícula foi passada por parâmetro",
            tags = {"Treino"}
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string")
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaListProgramaTreinoAlunoResponseDTOPaginacao.class),
    })
    @ResponseBody
    @RequestMapping(value = "/obter-programas-aluno/{matricula}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterProgramasAluno(@ApiParam(value = "Matrícula do aluno", defaultValue = "000100", required = true)
                                                                   @PathVariable("matricula") Integer matricula,

                                                                   @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                                                                           "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                                                                           "<strong>Filtros disponíveis:</strong>\n" +
                                                                           "- <strong>quicksearchValue:</strong> Filtra pelo nome do programa de treino.\n" +
                                                                           "- <strong>quicksearchFields:</strong> Define o campo onde o 'quicksearchValue' será aplicado. Para filtrar pelo nome, deve ser informado como uma lista ex: [\"nome\"].",
                                                                           defaultValue = "{\"quicksearchValue\":\"Hipertrofia\", \"quicksearchFields\":[\"nome\"]}")
                                                                   @RequestParam(value = "filters", required = false) String filtros,

                                                                   @ApiIgnore PaginadorDTO paginadorDTO) {
        try {
            FiltroProgramaTreinoJSON filtroJSON = new FiltroProgramaTreinoJSON(new JSONObject(filtros));
            return ResponseEntityFactory.ok(programaTreinoService.obterProgramasAluno(filtroJSON, paginadorDTO, matricula), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar dados do aluno de forma simplificada",
            notes = "Consulta os dados do aluno de forma simplificada.",
            tags = {"Clientes"}
    )
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaAlunoSimplesDTO.class),
    })
    @ResponseBody
    @RequestMapping(value = "/{matricula}/obter-aluno-simples", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterAlunoSimples(@ApiParam(value = "Código da matrícula do cliente que será consultado", required = true, defaultValue = "123")
                                                                 @PathVariable("matricula") Integer matricula) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterAlunoSimples(matricula));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter os contatos do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar uma cliente mensagem pelo código de matrícula do cliente",
            notes = "Consulta uma cliente mensagem pelo código de matrícula do cliente.",
            tags = {"Cliente Mensagem"}
    )
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaClienteMensagem.class),
    })
    @ResponseBody
    @RequestMapping(value = "/{id}/{tipoMensagem}/mensagem-matricula", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarClienteMensagemMatricula(@ApiParam(value = "Código da matrícula do cliente", required = true, defaultValue = "123")
                                                                              @PathVariable("id") final Integer id,
                                                                              @ApiParam(value = "Tipo da mensagem que será consultada.\n\n" +
                                                                                      "<strong>Tipo da mensagem</strong>\n" +
                                                                                      "- 0 (Nenhum)\n" +
                                                                                      "- 1 (Aviso do Consultor)\n" +
                                                                                      "- 2 (Catraca)" +
                                                                                      "- 3 (Aviso Médico)" +
                                                                                      "- 4 (Boletim)" +
                                                                                      "- 5 (Dados Incompletos)" +
                                                                                      "- 6 (Parcela em Atraso)" +
                                                                                      "- 7 (Risco)" +
                                                                                      "- 8 (Objetivo Curto)" +
                                                                                      "- 9 (Observação)" +
                                                                                      "- 10 (Observação do cliente)" +
                                                                                      "- 11 (Cartão Vencido)" +
                                                                                      "- 12 (Aluguel de armário vencido e chave não devolvida)" +
                                                                                      "- 13 (Estorno)", required = true, defaultValue = "1", allowableValues = "0,1,2,3,4,5,6,7,8,9,10,11,12,13")
                                                                              @PathVariable("tipoMensagem") final String tipoMensagem) {
        return ResponseEntityFactory.ok(clienteService.obterClienteMensagem(id, tipoMensagem));
    }

    @ApiOperation(
            value = "Cadastrar uma cliente mensagem",
            notes = "Cadastra uma cliente mensagem pelo código de matrícula do cliente.",
            tags = {"Cliente Mensagem"}
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida). Não retorna corpo de resposta.", response = ExemploRespostaVazia.class)
    )
    @ResponseBody
    @RequestMapping(value = "/{matricula}/{tipoMensagem}/mensagem", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> salvarAviso(@ApiParam(value = "Código da matrícula do cliente", required = true, defaultValue = "123")
                                                           @PathVariable("matricula") final Integer matricula,
                                                           @ApiParam(value = "Tipo da mensagem que será salva.\n\n" +
                                                                   "<strong>Tipo da mensagem</strong>\n" +
                                                                   "- 0 (Nenhum)\n" +
                                                                   "- 1 (Aviso do Consultor)\n" +
                                                                   "- 2 (Catraca)" +
                                                                   "- 3 (Aviso Médico)" +
                                                                   "- 4 (Boletim)" +
                                                                   "- 5 (Dados Incompletos)" +
                                                                   "- 6 (Parcela em Atraso)" +
                                                                   "- 7 (Risco)" +
                                                                   "- 8 (Objetivo Curto)" +
                                                                   "- 9 (Observação)" +
                                                                   "- 10 (Observação do cliente)" +
                                                                   "- 11 (Cartão Vencido)" +
                                                                   "- 12 (Aluguel de armário vencido e chave não devolvida)" +
                                                                   "- 13 (Estorno)", required = true, defaultValue = "1", allowableValues = "0,1,2,3,4,5,6,7,8,9,10,11,12,13")
                                                           @PathVariable("tipoMensagem") final String tipoMensagem,
                                                           @ApiParam(value = "Mensagem que será enviada para ser salva", example = "Cartão Vencido")
                                                           @RequestBody String mensagem) {
        try {
            clienteService.salvarClienteMensagem(matricula, tipoMensagem, mensagem);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Deletar uma cliente mensagem",
            notes = "Deleta uma cliente mensagem pelo código de matrícula do cliente.",
            tags = {"Cliente Mensagem"}
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida). Não retorna corpo de resposta.", response = ExemploRespostaVazia.class)
    )
    @ResponseBody
    @RequestMapping(value = "/{matricula}/{tipoMensagem}/mensagem", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> deleteMensagem(@ApiParam(value = "Código da matrícula do cliente", required = true, defaultValue = "123")
                                                              @PathVariable("matricula") final Integer matricula,
                                                              @ApiParam(value = "Tipo da mensagem que será salva.\n\n" +
                                                                      "<strong>Tipo da mensagem</strong>\n" +
                                                                      "- 0 (Nenhum)\n" +
                                                                      "- 1 (Aviso do Consultor)\n" +
                                                                      "- 2 (Catraca)" +
                                                                      "- 3 (Aviso Médico)" +
                                                                      "- 4 (Boletim)" +
                                                                      "- 5 (Dados Incompletos)" +
                                                                      "- 6 (Parcela em Atraso)" +
                                                                      "- 7 (Risco)" +
                                                                      "- 8 (Objetivo Curto)" +
                                                                      "- 9 (Observação)" +
                                                                      "- 10 (Observação do cliente)" +
                                                                      "- 11 (Cartão Vencido)" +
                                                                      "- 12 (Aluguel de armário vencido e chave não devolvida)" +
                                                                      "- 13 (Estorno)", required = true, defaultValue = "1", allowableValues = "0,1,2,3,4,5,6,7,8,9,10,11,12,13")
                                                              @PathVariable("tipoMensagem") final String tipoMensagem) {
        try {
            clienteService.deletarClienteMensagem(matricula, tipoMensagem);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar alunos",
            notes = "Consulta as informações dos alunos cadastrados podendo aplicar filtros durante a busca.",
            tags = {"Cliente Mensagem"}
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaListAlunoResponseTOPaginacao.class)
    )
    @ResponseBody
    @RequestMapping(value = "/select", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaAlunosSelect(
            @ApiParam(value = "Código da empresa que o aluno está vinculado", required = true, defaultValue = "1")
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,

            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome ou e-mail do aluno.\n" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\", \"email\"]).\n" +
                    "- <strong>niveisIds:</strong> Filtra por um ou mais níveis de aluno (Deve ser informado como uma lista de códigos/IDs ex: [1, 2]).\n" +
                    "- <strong>colaboradorIds:</strong> Filtra por um ou mais colaboradores (carteira) do aluno (Deve ser informado como uma lista de códigos/IDs ex: [10, 15]).\n" +
                    "- <strong>situacoesEnuns:</strong> Filtra pela situação do aluno (Deve ser informado como uma lista de códigos de 2 letras ex: [\"AT\", \"IN\"]).\n" +
                    "- <strong>statusClienteEnuns:</strong> Filtra pelo status do CRM do aluno (Deve ser informado como uma lista ex: [\"EM_ATENDIMENTO\", \"CONVERTIDO\"]).",
                    defaultValue = "{\"quicksearchValue\":\"Silva\", \"quicksearchFields\":[\"nome\"], \"niveisIds\":[1], \"colaboradorIds\":[10], \"situacoesEnuns\":[\"AT\"], \"statusClienteEnuns\":[\"CONVERTIDO\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,

            @ApiParam(value = "Indica se permite consultar alunos de outra empresa", defaultValue = "false")
            @RequestParam(value = "permiteAlunoOutraEmpresa", required = false) Boolean permiteAlunoOutraEmpresa,

            @ApiParam(value = "Indica se permite incluir autorizado", defaultValue = "true")
            @RequestParam(value = "incluirAutorizado", required = false) Boolean incluirAutorizado,
            @ApiIgnore PaginadorDTO paginadorDTO,
            @ApiIgnore HttpServletRequest request) throws JSONException {
        try {
            FiltroAlunoJSON filtroAlunoJSON = new FiltroAlunoJSON(filtros);
            return ResponseEntityFactory.ok(clienteSinteticoService.listaAlunosSelect(filtroAlunoJSON, paginadorDTO, request, empresaId, permiteAlunoOutraEmpresa, incluirAutorizado), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar informações do aluno no App Treino",
            notes = "Consulta as informações de um aluno no App Treino.",
            tags = {"App Treino"}
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaAlunoAppInfoDTO.class)
    )
    @ResponseBody
    @RequestMapping(value = "/alunoApp", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunoAppInfo(@ApiParam(value = "Código identificador do aluno que será consultado", defaultValue = "123")
                                                            @RequestParam(required = false) Integer alunoid,
                                                            @ApiParam(value = "Código do aluno na tabela pessoa", defaultValue = "345")
                                                            @RequestParam(required = false) Integer pessoa,
                                                            @ApiParam(value = "Código do cliente", defaultValue = "1234")
                                                            @RequestParam(required = false) Integer cliente) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.alunoAppInfo(alunoid, pessoa, cliente));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir a observacao", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Atualizar nível de um aluno",
            notes = "Atualiza o nível de um aluno.",
            tags = {"Níveis"}
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida). Não retorna corpo de resposta.", response = ExemploRespostaVazia.class)
    )
    @ResponseBody
    @RequestMapping(value = "/nivel/{matricula}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarNivelAluno(@ApiParam(value = "Código da matrícula do cliente", required = true, defaultValue = "123")
                                                                   @PathVariable("matricula") final Integer matricula,
                                                                   @ApiParam(value = "Nível que o aluno será transferido. Deve ser informado como um objeto Json contendo nivelId com o valor do nível que o aluno será transferido.")
                                                                   @RequestBody String json) {
        try {
            Integer nivelId = new JSONObject(json).optInt("nivelId");
            clienteSinteticoService.alterarNivelAluno(matricula, nivelId);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }


    @ApiOperation(
            value = "Alterar professor de um aluno",
            notes = "Altera o professor responsável por um aluno.",
            tags = {"Clientes"}
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida). Não retorna corpo de resposta.", response = ExemploRespostaVazia.class)
    )
    @ResponseBody
    @RequestMapping(value = "/professor/{matricula}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarProfessorAluno(@ApiParam(value = "Código de matrícula do aluno", required = true, defaultValue = "123")
                                                                       @PathVariable("matricula") final Integer matricula,
                                                                       @ApiParam(value = "Informe um JSON contendo o atributo professorId com o código identificador do professor que passará a ser responsável pelo aluno.")
                                                                       @RequestBody String json) {
        try {
            Integer professorId = new JSONObject(json).optInt("professorId");
            clienteSinteticoService.alterarProfessorAluno(matricula, professorId, request);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Consultar alunos de um colaborador",
            notes = "Consulta os alunos de um colaborador.",
            tags = {"Clientes"}
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaListClienteColaboradoresDTO.class)
    )
    @ResponseBody
    @RequestMapping(value = "/professor", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarProfessorAlunos(@RequestBody String body){
        try {
            if (body == null || body.trim().isEmpty()) {
                return ResponseEntityFactory.erroConhecido("ERRO", "Corpo da requisição não pode estar vazio");
            }
            JSONObject json = new JSONObject(body);
            clienteSinteticoService.alterarProfessorAlunos(request, json);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }
    @ApiIgnore
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ALUNOS)
    @RequestMapping(value = "/alunoColaborador", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obtemAlunosColaborador(@ApiParam(value = "Código da empresa que o aluno e o colaborador estão vinculados", defaultValue = "1")
                                                                      @RequestHeader(value = "empresaId", required = false) Integer empresaId,
                                                                      @ApiParam(value = "Indica se deve visualizar alunos e colaboradores de outras academias vinculadas na rede", defaultValue = "false")
                                                                      @RequestParam(required = false) Boolean carteiras,
                                                                      @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                                                                              "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                                                                              "<strong>Filtros disponíveis:</strong>\n" +
                                                                              "- <strong>matricula:</strong> Filtra pela matrícula ou código da matrícula.\n" +
                                                                              "- <strong>cpf:</strong> Filtra pelo CPF da pessoa.\n" +
                                                                              "- <strong>rg:</strong> Filtra pelo RG da pessoa.\n" +
                                                                              "- <strong>rne:</strong> Filtra pelo RNE (Registro Nacional de Estrangeiro).\n" +
                                                                              "- <strong>passaporte:</strong> Filtra pelo passaporte.\n" +
                                                                              "- <strong>email:</strong> Filtra pelo e-mail.\n" +
                                                                              "- <strong>telefone:</strong> Filtra pelo telefone.\n" +
                                                                              "- <strong>placa:</strong> Filtra pela placa do veículo.\n" +
                                                                              "- <strong>codigoAcesso:</strong> Filtra pelo código de acesso alternativo.\n" +
                                                                              "- <strong>responsavel:</strong> Filtra pelo nome do responsável (pai, mãe, terceiro).\n" +
                                                                              "- <strong>pessoa:</strong> Filtra pelo código da pessoa.\n" +
                                                                              "- <strong>cliente:</strong> Filtra pelo código de cliente.\n" +
                                                                              "- <strong>colaborador:</strong> Filtra pelo código de colaborador.\n" +
                                                                              "- <strong>contrato:</strong> Filtra pelo código do contrato.\n" +
                                                                              "- <strong>treino:</strong> Filtra pelo status do treino do aluno.\n" +
                                                                              "- <strong>tipoDeConsulta:</strong> Filtro por data de cadastro ou boas-vindas (ex: \"hoje\", \"ultimos7Dias\").\n" +
                                                                              "- <strong>empresas:</strong> Lista de códigos de empresas para filtro (ex: [1, 2]).\n" +
                                                                              "- <strong>situacoes:</strong> Lista de situações para filtro (ex: [\"AT\", \"IN\"]).\n" +
                                                                              "- <strong>tipos:</strong> Lista de códigos de tipos de pessoa (cliente, colaborador) para filtro (ex: [1, 2]).\n" +
                                                                              "- <strong>professores:</strong> Lista de códigos de professores para filtro (ex: [10, 15]).\n" +
                                                                              "- <strong>consultores:</strong> Lista de códigos de consultores para filtro (ex: [20, 21]).\n" +
                                                                              "- <strong>categorias:</strong> Lista de códigos de categorias para filtro.\n" +
                                                                              "- <strong>classificacoes:</strong> Lista de códigos de classificações para filtro.\n" +
                                                                              "- <strong>profissoes:</strong> Lista de códigos de profissões para filtro.\n" +
                                                                              "- <strong>grupos:</strong> Lista de códigos de grupos para filtro.",
                                                                              defaultValue = "{\"matricula\":\"12345\", \"cpf\":\"11122233344\", \"empresas\":[1], \"situacoes\":[\"AT\"], \"professores\":[10], \"tipos\":[1]}")
                                                                      @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                      @ApiIgnore PaginadorDTO paginadorDTO) {
        try {
            FiltroPessoaJSON filtroAlunoJSON = new FiltroPessoaJSON(filtros);
            filtroAlunoJSON.setVisualizarOutrasCarteiras(carteiras != null && carteiras);
            return ResponseEntityFactory.ok(clienteService.listaAlunosColaboradores(filtroAlunoJSON, paginadorDTO, empresaId));
        } catch (Exception ex) {
            return ResponseEntityFactory.erroInterno(STATUS_ERRO, ex.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar matrículas dos alunos",
            notes = "Consulta as matrículas dos alunos.",
            tags = {"Clientes"}
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaListInteger.class)
    )
    @ResponseBody
    @RequestMapping(value = "lista/matriculas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaAlunosMatriculas(
            @ApiParam(value = "Código identificador da empresa que os alunos estão vinculados", required = true, defaultValue = "1")
            @RequestHeader(value = "empresaId", required = false) Integer empresaId,
            @ApiParam(value = "Filtra pelo status do cliente.\n\n" +
                    "<strong>Valores disponíveis</strong>\n" +
                    "- 0 - TODOS (Todos)\n" +
                    "- 1 - MINHA_CARTEIRA (Minha carteira)\n" +
                    "- 2 - NA_ACADEMIA (Na academia)\n" +
                    "- 3 - ACOMPANHANDO (Acompanhando)\n" +
                    "- 4 - DESACOMPANHADOS (Desacompanhados)\n" +
                    "- 5 - AGENDADOS (Agendados)\n" +
                    "- 6 - SEM_TREINO (Sem Treino)\n" +
                    "- 7 - TREINO_VENCIDO (Treino Vencido)\n" +
                    "- 8 - NA_ACADEMIA_TREINO_VENCIDO (Na academia com treino vencido)\n" +
                    "- 9 - NA_ACADEMIA_TREINO_A_VENCER (Na academia com treino a vencer)\n" +
                    "- 10 - NA_ACADEMIA_MINHA_CARTEIRA (Na academia da minha carteira)\n" +
                    "- 11 - NA_ACADEMIA_SEM_TREINO (Na academia sem treino)", defaultValue = "0", allowableValues = "TODOS,MINHA_CARTEIRA,NA_ACADEMIA,ACOMPANHANDO,DESACOMPANHADOS,AGENDADOS,SEM_TREINO,TREINO_VENCIDO,NA_ACADEMIA_TREINO_VENCIDO,NA_ACADEMIA_TREINO_A_VENCER,NA_ACADEMIA_MINHA_CARTEIRA,NA_ACADEMIA_SEM_TREINO")
            @RequestParam(value = "statusClienteEnum", required = false) String statusClienteEnum,
            @ApiParam(value = "Filtra pelos alunos que estão nos níveis da lista", example = "1,2,3")
            @RequestParam(value = "niveis", required = false) String niveis,
            @ApiParam(value = "Filtra pela lista de códigos de colaboradores que os clientes estão vinculados", example = "12,23,4")
            @RequestParam(value = "colaboradorZw", required = false) String colaboradorZw,
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>statusClienteEnuns:</strong> Filtra por status de treino/presença. Apenas um valor por vez. <br/>" +
                    "&nbsp;&nbsp;Valores: \"MINHA_CARTEIRA\", \"NA_ACADEMIA\", \"NA_ACADEMIA_SEM_TREINO\", \"NA_ACADEMIA_COM_TREINO_A_VENCER\", \"NA_ACADEMIA_COM_TREINO_VENCIDO\", \"NA_ACADEMIA_DA_MINHA_CARTEIRA\", \"TREINO_VENCIDO\", \"SEM_TREINO\".\n" +
                    "- <strong>niveisIds:</strong> Filtra por um ou mais níveis de aluno (Deve ser informado como uma lista de códigos/IDs ex: [1, 2]).\n" +
                    "- <strong>colaboradorIds:</strong> Filtra pela carteira de um ou mais colaboradores (Deve ser informado como uma lista de códigos/IDs ex: [10, 15]).",
                    defaultValue = "{\"statusClienteEnuns\":[\"NA_ACADEMIA\"], \"niveisIds\":[1], \"colaboradorIds\":[10]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros) throws JSONException {
        try {
            FiltroAlunoJSON filtroAlunoJSON = new FiltroAlunoJSON(filtros);
            if (!UteisValidacao.emptyString(statusClienteEnum)) {
                filtroAlunoJSON.setStatusClienteEnum(new ArrayList<>());
                filtroAlunoJSON.getStatusClienteEnum().add(statusClienteEnum);
            }
            if (!UteisValidacao.emptyString(niveis)) {
                filtroAlunoJSON.setNiveisIds(new ArrayList<>());
//                filtroAlunoJSON.getNiveisIds().add(statusClienteEnum);
            }
            if (!UteisValidacao.emptyString(colaboradorZw)) {
                filtroAlunoJSON.setColaboradorZw(new ArrayList<>());
                for (String codZw : colaboradorZw.split(",")) {
                    try {
                        if (!UteisValidacao.emptyNumber(new Integer(codZw))) {
                            filtroAlunoJSON.getColaboradorZw().add(new Integer(codZw));
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }
            return ResponseEntityFactory.ok(clienteSinteticoService.listaAlunosMatricula(filtroAlunoJSON, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Importar alunos para a empresa",
            notes = "Importa os alunos para a empresa.",
            tags = {"Clientes"}
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaImportarAluno.class)
    )
    @ResponseBody
    @RequestMapping(value = "{ctx}/importar-aluno/{empresa}", method = {RequestMethod.POST, RequestMethod.GET})
    public ResponseEntity<EnvelopeRespostaDTO> importall(@ApiParam(value = "Código CTX", example = "ctx123", required = true)
                                                         @PathVariable String ctx,
                                                         @ApiParam(value = "Código da empresa", example = "1", required = true)
                                                         @PathVariable Integer empresa,
                                                         @ApiParam(value = "Código de matrícula do aluno que será importado. (Se for importar apenas um único aluno)", example = "123")
                                                         @RequestParam(required = false) Integer matricula) {
        try {
            DashboardBIService cs = (DashboardBIService) UtilContext.getBean(DashboardBIService.class);
            if (!UteisValidacao.emptyNumber(matricula)) {
                return ResponseEntityFactory.ok(cs.importarAlunoForaTreino(ctx, empresa, matricula));
            } else {
                cs.importarAlunosForaTreinoAll(ctx, empresa);
                return ResponseEntityFactory.ok("Processo iniciado, aguarde e confie");
            }
        } catch (Exception e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar importar aluno", e);
            return ResponseEntityFactory.erroInterno("Erro", e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar últimos acessos à academia",
            notes = "Consulta os últimos acessoa à academia.",
            tags = {"Lista de Acessos"}
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaConfigListaRapidaAcessoDTO.class)
    )
    @ResponseBody
    @RequestMapping(value = "lista-rapida-acessos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaRapidaAcessos(@ApiParam(value = "Tipo do acesso do cliente\n\n" +
                                                                          "<Valores disponíveis>" +
                                                                          "- 1 TODOS_ALUNOS\n" +
                                                                          "- 2 ALUNOS_VINCULADOS", allowableValues = "TODOS_ALUNOS, ALUNOS_VINCULADOS", defaultValue = "TODOS_ALUNOS")
                                                                  @RequestParam(value = "tipo") Integer tipo,
                                                                  @ApiParam(value = "Limite de acessos que serão consultados", defaultValue = "20")
                                                                  @RequestParam(value = "limite", required = false) Integer limite,
                                                                  @ApiParam(value = "Código identificador da empresa que foi realizado os acessos", defaultValue = "1")
                                                                  @RequestHeader(value = "empresaId", required = false) Integer empresaId) throws JSONException {
        try {
            return ResponseEntityFactory.ok(listaRapidaAcessosService.listaUltimosAcessos(empresaId, TipoListaAcessoEnum.getTipo(tipo), limite));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "lista-rapida-acessos-cache", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaRapidaAcessosCache(@RequestParam(value = "tipo") Integer tipo,
                                                                  @RequestParam(value = "limite", required = false) Integer limite,
            @RequestHeader(value = "empresaId", required = false) Integer empresaId) throws JSONException {
        try {
            return ResponseEntityFactory.ok(listaRapidaAcessosService.listaUltimosAcessos(empresaId, TipoListaAcessoEnum.getTipo(tipo), limite, true));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Salvar configuração das notificações de acesso à academia",
            notes = "Salva as configurações das notificações de acesso à academia",
            tags = {"Lista de Acessos"}
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida). Não retorna corpo de resposta.", response = ExemploRespostaVazia.class)
    )
    @ResponseBody
    @RequestMapping(value = "lista-rapida-acessos-notf/{tipo}/{valor}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> gravarConfiguracaoNotificacaoAcesso(@ApiParam(value = "Tipo da configuração", required = true, defaultValue = "PARCELAS_EM_ATRASO", allowableValues = "SEM_PROGRAMA_DE_TREINO,FALTOSOS,QUATRO_DIAS_OU_MAIS_SEM_EXECUTAR_O_TREINO,PROGRAMA_DE_TREINO_VENCIDO,PROGRAMA_DE_TREINO_A_VENCER,ALUNO_SEM_VINCULO_DE_PROFESSOR_TREINOWEB,PARCELAS_EM_ATRASO,AVALIACAO_FISICA_ATRASADA,SEM_ASSINATURA_DE_CONTRATO,PARQ_NAO_RESPONDIDO,PARQ_NAO_RESPONDIDO,PARQ_POSITIVO,CADASTRO_INCOMPLETO")
                                                                                   @PathVariable(value = "tipo") String tipo,
                                                                                   @ApiParam(value = "Valor da configuração", required = true, defaultValue = "true", allowableValues = "true,false")
                                                                                   @PathVariable(value = "valor") String valor) throws JSONException {
        try {
            listaRapidaAcessosService.gravarCfgNotificacoesAcessosUsuarios(PendenciasAcessoEnum.valueOf(tipo), Boolean.valueOf(valor));
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @RequestMapping(value = "/excluir-aluno", method = RequestMethod.POST)
    public @ResponseBody String excluirAluno(@RequestHeader String key, @RequestBody Integer codigoCliente) {
        try {
            ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(
                    ClienteSinteticoService.class);
            if (codigoCliente == null) {
                return "ERRO: Código do cliente não pode ser nulo";
            }
            clienteService.excluirAluno(key, codigoCliente);
            return "OK";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/validar-sincronizacao-aluno-zw-tr", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> validarSincronizacaoAlunoZwTr(@RequestBody String body,
                                                                             @RequestHeader("empresaId") Integer empresaId) {
        try {
            if (body == null || body.trim().isEmpty()) {
                return ResponseEntityFactory.erroConhecido("ERRO", "Corpo da requisição não pode estar vazio");
            }
            JSONObject json = new JSONObject(body);
            if (!json.has("codigoClienteZw")) {
                return ResponseEntityFactory.erroConhecido("ERRO", "Campo 'codigoClienteZw' é obrigatório");
            }

            Integer codigoClienteZw = json.getInt("codigoClienteZw");

            if (codigoClienteZw <= 0) {
                return ResponseEntityFactory.erroConhecido("ERRO", "codigoClienteZw deve ser um número positivo");
            }
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(clienteSinteticoService.validarSincronizacaoAlunoZwTr(ctx, codigoClienteZw));
        } catch (Exception e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar importar aluno", e);
            return ResponseEntityFactory.erroInterno("Erro", e.getMessage());
        }
    }
}
