package br.com.pacto.controller.json.gympass;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.agenda.AgendaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.gympass.GymPassBookingService;
import br.com.pacto.service.intf.gympass.LogGymPassService;
import br.com.pacto.util.UtilContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 30/03/2020
 */
@Controller
@RequestMapping("/gympass")
public class GymPassController extends SuperController {

    @Autowired
    private AgendaService agendaService;
    @Autowired
    private GymPassBookingService gymPassBookingService;
    @Autowired
    private LogGymPassService logService;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;

    @ResponseBody
    @RequestMapping(value = "{ctx}/sincronizar", method = {RequestMethod.GET, RequestMethod.POST}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> sincronizar(@PathVariable String ctx,
                                                           @RequestHeader(value = "empresaZW") Integer empresaZW,
                                                           @RequestHeader(value = "empresaTW") Integer empresaTW,
                                                           @RequestHeader(value = "turmaId") Integer turmaId) {
        try {
            return ResponseEntityFactory.ok(agendaService.sincronizarTurmaGympassBooking(ctx, empresaZW, empresaTW, turmaId));
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar turma com gympass booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar turma com gympass booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/excluir", method = {RequestMethod.DELETE}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluir(@PathVariable String ctx,
                                                       @RequestHeader(value = "empresaZW") Integer empresaZW,
                                                       @RequestHeader(value = "empresaTW") Integer empresaTW,
                                                       @RequestHeader(value = "excluirTodas") boolean excluirTodas,
                                                       @RequestHeader(value = "turmaId") Integer turmaId) {
        try {
            return ResponseEntityFactory.ok(agendaService.excluirHorariosGympassBooking(ctx, empresaZW, empresaTW, turmaId, excluirTodas));
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar turma com gympass booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar turma com gympass booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/booking", method = {RequestMethod.POST}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> booking(@PathVariable String ctx,
                                                       @RequestBody String booking,
                                                       @RequestParam(required = false) Boolean validando) {
        try {
            return ResponseEntityFactory.ok(agendaService.booking(ctx, booking, validando == null ? Boolean.FALSE : validando));
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/healthGympass", method = {RequestMethod.GET}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> healthGympass(@PathVariable String ctx,
                                                             @RequestParam Integer empresaTR) {
        try {
            return ResponseEntityFactory.ok(gymPassBookingService.healthGympass(ctx, empresaTR));
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao consultar healthGympass", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao consultar healthGympass", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/log", method = {RequestMethod.GET}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> log(@PathVariable String ctx) {
        try {
            return ResponseEntityFactory.ok(logService.ultimos(ctx));
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "{ctx}/aulas/{empresa}", method = {RequestMethod.GET}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aulas(@PathVariable String ctx,
                                                     @PathVariable Integer empresa) {
        try {
            return ResponseEntityFactory.ok(gymPassBookingService.aulas(ctx, empresa));
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/horarios/{empresa}/{dia}", method = {RequestMethod.GET}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> horarios(@PathVariable String ctx,
                                                        @PathVariable String dia,
                                                        @PathVariable Integer empresa,
                                                        @RequestParam(required = false) Integer idClasse
                                                        ) {
        try {
            return ResponseEntityFactory.ok(gymPassBookingService.horarios(ctx, empresa, dia, idClasse));
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/ultimas", method = {RequestMethod.GET}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> ultimas(@PathVariable String ctx
                                                        ) {
        try {
            return ResponseEntityFactory.ok(agendaService.horariosSincronizados(ctx));
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/inativar/{empresa}/{reference}/{exceto}", method = {RequestMethod.POST}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> inativar(@PathVariable String ctx,
                                                        @PathVariable String reference,
                                                        @PathVariable Integer exceto,
                                                     @PathVariable Integer empresa) {
        try {
            return ResponseEntityFactory.ok(gymPassBookingService.inativarUmaAula(ctx, empresa, reference, exceto));
        }catch (Exception e){
            e.printStackTrace();
        }
        return ResponseEntityFactory.ok("serviço desabilitado procure a squad");
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/normalizar/{empresa}", method = {RequestMethod.POST}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> normalizar(@PathVariable String ctx,
                                                     @PathVariable Integer empresa) {
        try {
            gymPassBookingService.normalizar(ctx, empresa);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/delete/slot/{empresa}/{turmaid}/{dia}", method = {RequestMethod.POST}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> normalizar(@PathVariable String ctx,
                                                     @PathVariable Integer empresa,
                                                     @PathVariable Integer turmaid,
                                                     @PathVariable String dia
                                                          ) {
        try {
            gymPassBookingService.excluirSlotsDeUmDia(ctx, empresa, Uteis.getDate(dia, "ddMMyyyy"), turmaid);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/slots-dia", method = {RequestMethod.GET}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> slotsDia(@PathVariable String ctx,
                                                        @RequestParam String inicio,
                                                        @RequestParam String fim) {
        try {
            return ResponseEntityFactory.ok(gymPassBookingService.consultarSlotsGympass(ctx,
                    Uteis.getDate(inicio, "dd/MM/yyyy"),
                    Uteis.getDate(fim, "dd/MM/yyyy")));
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/sync", method = {RequestMethod.GET}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> sync(@PathVariable String ctx) {
        try {
            AgendaService agendaService = UtilContext.getBean(AgendaService.class);
            agendaService.sincronizarTurmasGympassBooking(ctx, 0, null);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/obter-aluno-por-token/{token}", method = {RequestMethod.GET}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterAlunoPorToken(@PathVariable String ctx, @PathVariable String token) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterAlunoGympassPorToken(ctx, token));
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar o aluno gympass", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

}
