package br.com.pacto.controller.json.lesao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.lesao.FiltroLesaoJSON;
import br.com.pacto.bean.lesao.LesaoDTO;
import br.com.pacto.controller.json.wod.TiposWodController;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.lesao.LesaoService;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/lesao")
public class LesaoController {
    @Autowired
    private SessaoService sessaoService;
    private final LesaoService lesaoService;


    @Autowired
    public LesaoController(LesaoService lesaoService){
        Assert.notNull(lesaoService, "O serviço de tipos lesao não foi injetado corretamente");
        this.lesaoService = lesaoService;
    }

    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarLesao(@RequestBody LesaoDTO lesaoDTO){
        Integer codigoUsuario = sessaoService.getUsuarioAtual().getId();
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            return ResponseEntityFactory.ok(lesaoService.gravarLesao(lesaoDTO, ctx, codigoUsuario, false));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar Lesão", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> editarLesao(@RequestBody LesaoDTO lesaoDTO) {
        Integer codigoUsuario = sessaoService.getUsuarioAtual().getId();
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            return ResponseEntityFactory.ok(lesaoService.gravarLesao(lesaoDTO, ctx, codigoUsuario, false));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar Lesão", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarLesao(@RequestParam(value = "filters", required = false) JSONObject filtros, PaginadorDTO paginadorDTO) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            //FiltroNivelWodJSON filtroNivelWodJSON = new FiltroNivelWodJSON(filtros);
            FiltroLesaoJSON filtroLesaoJSON = new FiltroLesaoJSON(filtros);
            return ResponseEntityFactory.ok(lesaoService.listarLesao(ctx, filtroLesaoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar o Nível wod", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarLesao(@PathVariable("id") final Integer id) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(lesaoService.buscarLesao(id,ctx));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar o Nível wod", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/bi-cross/indice-lesoes/{ano}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> montarIndiceLesao(@RequestHeader("empresaId") Integer empresaId,
                                                           @PathVariable("ano") final Integer ano) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(lesaoService.montarIndiceLesao(ctx, empresaId, ano));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao montar o índice de lesões", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/bi-cross/listar-lesoes", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarLesoesBiCross(@RequestHeader("empresaId") Integer empresaId,
                                                            @RequestParam(value = "filters", required = true) JSONObject filtros) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(lesaoService.listarLesoesBiCross(ctx, empresaId, filtros));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao listar lesões para BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
