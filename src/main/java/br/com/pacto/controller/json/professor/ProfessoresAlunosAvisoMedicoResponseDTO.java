package br.com.pacto.controller.json.professor;

import br.com.pacto.bean.professor.ProfessorSintetico;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProfessoresAlunosAvisoMedicoResponseDTO {

    private ProfessorSimplesResponseDTO professor;
    private String nome;
    private Number qtdAlunosMsg;
    private String matricula;
    private String situacao;
    private String terminoContrato;
    private String terminoProgramaVigente;
    private String avisoMedico;

    public ProfessoresAlunosAvisoMedicoResponseDTO() {
    }

    public ProfessoresAlunosAvisoMedicoResponseDTO(ProfessorSintetico professorSintetico){
        this.professor = new ProfessorSimplesResponseDTO(professorSintetico);
        this.nome = professorSintetico.getNomeAbreviado();
    }

    public ProfessorSimplesResponseDTO getProfessor() {
        return professor;
    }

    public void setProfessor(<PERSON>S<PERSON><PERSON><PERSON><PERSON>ponseDTO professor) {
        this.professor = professor;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Number getQtdAlunosMsg() {
        return qtdAlunosMsg;
    }

    public void setQtdAlunosMsg(Number qtdAlunosMsg) {
        this.qtdAlunosMsg = qtdAlunosMsg;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getTerminoContrato() {
        return terminoContrato;
    }

    public void setTerminoContrato(String terminoContrato) {
        this.terminoContrato = terminoContrato;
    }

    public String getTerminoProgramaVigente() {
        return terminoProgramaVigente;
    }

    public void setTerminoProgramaVigente(String terminoProgramaVigente) {
        this.terminoProgramaVigente = terminoProgramaVigente;
    }

    public String getAvisoMedico() {
        return avisoMedico;
    }

    public void setAvisoMedico(String avisoMedico) {
        this.avisoMedico = avisoMedico;
    }
}
