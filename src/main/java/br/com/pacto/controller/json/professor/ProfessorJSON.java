/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.professor;

import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.controller.json.base.SuperJSON;

/**
 *
 * <AUTHOR>
 */
public class <PERSON><PERSON><PERSON><PERSON> extends SuperJSON {

    private Integer codigo;
    private String nome;
    private Boolean ativo;
    private Integer codEmpresa;
    private String avatar;
    private Integer versao;

    public ProfessorJSON() {

    }


    public ProfessorJSON(ProfessorSintetico ps) {
        this.setCodigo(ps.getCodigo());
        this.setAtivo(ps.isAtivo());
        this.setNome(ps.getNome());
        this.setCodEmpresa(ps.getEmpresa().getCodZW());
        this.setAvatar(ps.getAvatar());
        this.setVersao(ps.getVersao());
    }


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Integer getCodEmpresa() {
        return codEmpresa;
    }

    public void setCodEmpresa(Integer codEmpresa) {
        this.codEmpresa = codEmpresa;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public Integer getVersao() {
        return versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public static ProfessorJSON obterJSON(ProfessorSintetico ps) {
        ProfessorJSON json = new ProfessorJSON();
        json.setCodigo(ps.getCodigo());
        json.setAtivo(ps.isAtivo());
        json.setNome(ps.getNome());
        json.setCodEmpresa(ps.getEmpresa().getCodZW());
        json.setAvatar(ps.getAvatar());
        json.setVersao(ps.getVersao());
        return json;
    }

}
