package br.com.pacto.controller.json.usuario;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.colaborador.FiltroColaboradorJSON;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.usuario.NotificacaoRecursoEmpresaTO;
import br.com.pacto.bean.usuario.UsuarioColaboradorTO;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.service.login.TokenDTO;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * paulo 19/10/2018
 */

@Controller
@RequestMapping("/psec/usuarios")
public class UsuarioController {

    private UsuarioService usuarioService;
    private SessaoService sessaoService;

    @Autowired
    public UsuarioController(UsuarioService usuarioService, SessaoService sessaoService) {
        Assert.notNull(usuarioService, "O serviço de usuário não foi injetado corretamente");
        Assert.notNull(usuarioService, "O serviço de sessão não foi injetado corretamente");
        this.usuarioService = usuarioService;
        this.sessaoService = sessaoService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/colaboradores", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaUsuarioColaborador(
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            PaginadorDTO paginadorDTO
    ) throws JSONException {
        try {
            FiltroColaboradorJSON filtros = new FiltroColaboradorJSON(filters);
            return ResponseEntityFactory.ok(
                    usuarioService.listaUsuarioColaborador(filtros, paginadorDTO, empresaId),
                    paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar usuários de colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/colaboradores/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalhesUsuarioColaborador(@PathVariable("id") Integer id, HttpServletRequest request){
        try {
            return ResponseEntityFactory.ok(usuarioService.obterUsuarioColaborador(id, request));
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar usuário de colaboradore", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value= "/colaboradores", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarUsuarioColaborador(
            @RequestBody UsuarioColaboradorTO usuarioColaboradorTO,
            @RequestHeader("empresaId") Integer empresaId,
            HttpServletRequest request) throws Exception {
        try {

            return ResponseEntityFactory.ok(usuarioService.cadastrarUsuarioColaborador(sessaoService.getUsuarioAtual().getChave(), usuarioColaboradorTO, empresaId, request));
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar usuário", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/colaboradores/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarUsuarioColaborador(
            @PathVariable("id") Integer id,
            @RequestBody UsuarioColaboradorTO usuarioColaboradorTO,
            @RequestHeader("empresaId") Integer empresaId,
            HttpServletRequest request){
        try {

            return ResponseEntityFactory.ok(usuarioService.alterarUsuarioColaborador(request, id, usuarioColaboradorTO, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar o usuário", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/solicitar-atendimento", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> linkAberturaChamado(@RequestHeader("empresaId") Integer empresaId, HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(usuarioService.urlSolicitarAtendimento(empresaId, request));
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioController.class.getName()).log(Level.SEVERE, "Erro ao carregar url de abertura de chamado", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value= "/notificarRecursoEmpresa", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public void cadastrarUsuarioColaborador(
            @RequestBody NotificacaoRecursoEmpresaTO notificacaoRecurso) {
        try {
            SuperControle.notificarRecursoEmpresa(notificacaoRecurso.getChave(), notificacaoRecurso.getRecursoNotificar(),
                    notificacaoRecurso.getNomeUsuario(), notificacaoRecurso.getEmpresaId(), notificacaoRecurso.getNomeEmpresa(),
                    notificacaoRecurso.getCidade(), notificacaoRecurso.getEstado(), notificacaoRecurso.getPais());
        } catch (Exception e) {
            Logger.getLogger(UsuarioController.class.getName()).log(Level.SEVERE, "Erro ao tentar notificar recurso: ", notificacaoRecurso.getRecursoNotificar());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/codigo-verificacao-email/{id}", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarEmail(
            @PathVariable(value = "id") Integer idUsuario,
            @RequestParam String email,
            HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(usuarioService.solicitarCodigoVerficacaoEmail(sessaoService.getUsuarioAtual().getChave(), idUsuario, email, request));
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioController.class.getName()).log(Level.SEVERE, "Erro no envio do código de verificação do e-mail", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/validar-codigo-verificacao/{id}", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> validarCodigoVerificacaoEmail(
            @PathVariable(value = "id") Integer idUsuario, @RequestBody TokenDTO tokenDTO, HttpServletRequest request
    ) {
        try {
            usuarioService.processarToken(sessaoService.getUsuarioAtual().getChave(), idUsuario, tokenDTO, request);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioController.class.getName()).log(Level.SEVERE, "Erro ao validar o código de verificação!", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/recuperar-senha/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> recuperarSenha(@PathVariable("id") Integer idUsuario, HttpServletRequest request) {
        try {
            usuarioService.recuperarSenhaNovoLogin(sessaoService.getUsuarioAtual().getChave(), idUsuario, request);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioController.class.getName()).log(Level.SEVERE, "Erro solicitar a recuperação de senha!", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.DESVINCULAR_USUARIO)
    @RequestMapping(value = "/desvincular-usuario/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> desvincularUsuarioNovoLogin(@PathVariable("id") Integer idUsuario, HttpServletRequest request) {
        try {
            usuarioService.desvincularUsuarioNovoLogin(sessaoService.getUsuarioAtual().getChave(), idUsuario, request);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioController.class.getName()).log(Level.SEVERE, "Erro ao desvincular usuário da chave " + sessaoService.getUsuarioAtual().getChave(), e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }
}
