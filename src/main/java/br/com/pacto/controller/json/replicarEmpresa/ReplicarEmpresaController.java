package br.com.pacto.controller.json.replicarEmpresa;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.configuracoes.ConfiguracaoDobras;
import br.com.pacto.controller.json.base.*;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.replicarEmpresa.ConfiguracaoReplicarRedeEmpresaService;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static org.apache.commons.lang3.StringUtils.isBlank;

@Controller
@RequestMapping("/replicar-empresa")
public class ReplicarEmpresaController {

    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;

    @Autowired
    private HttpServletRequest httpServletRequest;

    private final ConfiguracaoReplicarRedeEmpresaService configuracaoReplicarRedeEmpresaService;

    @Autowired
    public ReplicarEmpresaController(ConfiguracaoReplicarRedeEmpresaService configuracaoReplicarRedeEmpresaService){
        Assert.notNull(configuracaoReplicarRedeEmpresaService, "O serviço de configuracao replicar rede empresa não foi injetado corretamente");
        this.configuracaoReplicarRedeEmpresaService = configuracaoReplicarRedeEmpresaService;
    }

    @ResponseBody
    @RequestMapping(value = "/configuracao/findByChaveOrigem", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarConfiguracaoTreinoRedeEmpresa(@RequestParam(value = "chaveOrigem") String chaveOrigem) throws JSONException {
        try {
            return ResponseEntityFactory.ok(configuracaoReplicarRedeEmpresaService.findByChaveOrigem(chaveOrigem));
        } catch (ServiceException e) {
            Logger.getLogger(ReplicarEmpresaController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as configuracoes replicar rede empresa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/configuracao/replicar", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> replicarConfiguracaoTreinoRedeEmpresa(@RequestParam(value = "chaveOrigem") String chaveOrigem,
                                                                                     @RequestParam(value = "chaveDestino") String chaveDestino,
                                                                                     @RequestParam(value = "tokenAtual") String tokenAtual) throws JSONException {
        try {
            if(isBlank(chaveDestino) || chaveDestino.contains("undefined")){
                return ResponseEntityFactory.ok("empty");
            }
            return ResponseEntityFactory.ok(configuracaoReplicarRedeEmpresaService.replicar(chaveOrigem, chaveDestino, tokenAtual));
        } catch (ServiceException e) {
            Logger.getLogger(ReplicarEmpresaController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as configuracoes replicar rede empresa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/configuracoes/saveReplica", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveReplica(@RequestParam(value = "chaveDestino") String chaveDestino, @RequestParam(value = "configName") String configName, @RequestBody() String configDTO) {
        Class classs = null;
        Object obj = null;
        try {
            if (configName.equals("gerais")) {
                classs = ConfiguracoesGeraisDTO.class;
                obj = JSONMapper.getObject(new JSONObject(configDTO), ConfiguracoesGeraisDTO.class);
            } else if (configName.equals("gestao")) {
                classs = ConfiguracoesGestaoDTO.class;
                obj = JSONMapper.getObject(new JSONObject(configDTO), ConfiguracoesGestaoDTO.class);
            } else if (configName.equals("notificacoes")) {
                classs = ConfiguracoesNotificacaoDTO.class;
                obj = JSONMapper.getObject(new JSONObject(configDTO), ConfiguracoesNotificacaoDTO.class);
            } else if (configName.equals("aulas")) {
                classs = ConfiguracoesAulasDTO.class;
                ConfiguracoesAulasDTO aula = JSONMapper.getObject(new JSONObject(configDTO), ConfiguracoesAulasDTO.class);
                aula.setMinutos_agendar_com_antecedencia(Uteis.removerTudoMenosNumero(aula.getMinutos_agendar_com_antecedencia()));
                aula.setMinutos_desmarcar_com_antecedencia(Uteis.removerTudoMenosNumero(aula.getMinutos_desmarcar_com_antecedencia()));
                obj = JSONMapper.getObject(new JSONObject(aula), ConfiguracoesAulasDTO.class);
            } else if (configName.equals("apps")) {
                classs = ConfiguracoesAplicativosDTO.class;
                obj = JSONMapper.getObject(new JSONObject(configDTO), ConfiguracoesAplicativosDTO.class);
            } else if (configName.equals("treino")) {
                classs = ConfiguracoesTreinoDTO.class;
                obj = JSONMapper.getObject(new JSONObject(configDTO), ConfiguracoesTreinoDTO.class);
            } else if (configName.equals("avaliacao")) {
                classs = ConfiguracoesAvaliacaoDTO.class;
                ConfiguracoesAvaliacaoDTO avaliacao = JSONMapper.getObject(new JSONObject(configDTO), ConfiguracoesAvaliacaoDTO.class);
                if (avaliacao.getCfg_dobras_cutaneas()) {
                    Gson gson = new Gson();
                    List<ConfiguracaoDobras> cfgsDobras = gson.fromJson(avaliacao.getOrdens_dobras(), new TypeToken<ArrayList<ConfiguracaoDobras>>() {
                    }.getType());
                    try {
                        configuracaoSistemaService.gravarCfgsDobras(chaveDestino, cfgsDobras);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                obj = JSONMapper.getObject(new JSONObject(avaliacao), ConfiguracoesAvaliacaoDTO.class);
            } else if (configName.equals("ia")) {
                classs = ConfiguracoesIaDTO.class;
                obj = JSONMapper.getObject(new JSONObject(configDTO), ConfiguracoesIaDTO.class);
            } else {
                throw new Exception("Nenhuma configuração encontrada.");
            }

            if (configName.equals("notificacoes")) {
                configuracaoSistemaService.gravarCfgsNotificacaoDTO(chaveDestino, classs, obj);
            } else {
                configuracaoSistemaService.gravarCfgsDTO(chaveDestino, classs, obj);
            }
            configuracaoSistemaService.purgeCache(chaveDestino);
            configuracaoSistemaService.notificarOuvintes(chaveDestino, httpServletRequest);
            return ResponseEntityFactory.ok("sucesso");
        } catch (ServiceException e) {
            Logger.getLogger(ReplicarEmpresaController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as configuracoes replicar rede empresa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
