/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.base;

import br.com.pacto.base.impl.EntityManagerFactoryService;
import br.com.pacto.base.jpa.MigracaoDadosJPAService;
import br.com.pacto.base.jpa.service.intf.PovoadorService;
import br.com.pacto.base.scheduling.agendamento.TaskNotificationAgendamento;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.agenda.TipoLembreteEnum;
import br.com.pacto.bean.atividade.AtividadeAnimacao;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoAndamento;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.agendamento.AgendamentoJSON;
import br.com.pacto.controller.json.animacao.AnimacaoJSON;
import br.com.pacto.controller.json.atividade.read.AtividadeJSON;
import br.com.pacto.controller.json.ficha.read.FichaJSON;
import br.com.pacto.controller.json.programa.read.ProgramaTreinoJSON;
import br.com.pacto.controller.json.programa.ProgramaTreinoJSONControle;
import br.com.pacto.controller.json.stats.StatsJSON;
import br.com.pacto.controller.json.usuario.FotosJSONControle;
import br.com.pacto.dao.intf.atividade.AtividadeAnimacaoDao;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.agenda.AgendamentoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.creditopersonal.CreditoPersonalService;
import br.com.pacto.service.intf.ficha.FichaService;
import br.com.pacto.service.intf.notificacao.NotificacaoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.impl.UtilReflection;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
import servicos.integracao.adm.AdmWSConsumer;
import servicos.integracao.adm.client.EmpresaWS;
import servicos.integracao.admapp.AdmAppWSConsumer;
import servicos.integracao.admapp.client.Midia;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/EndpointControl")
public class EndpointDocController extends SuperControle {

    private RequestMappingHandlerMapping handlerMapping;
    @Autowired
    private ClienteSinteticoDao clienteSinteticoDao;
    @Autowired
    private AgendamentoService agendamentoService;
    @Autowired
    private NotificacaoService notificacaoService;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private ProgramaTreinoService programaTreinoService;
    @Autowired
    private FichaService fichaService;
    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private UsuarioDao usuarioDao;
    @Autowired
    private ConfiguracaoSistemaService configService;

    @Autowired
    public EndpointDocController(RequestMappingHandlerMapping handlerMapping) {
        this.handlerMapping = handlerMapping;
    }

//    @RequestMapping(value = "/show", method = RequestMethod.GET)
//    public void mostrar(Model model) {
//        model.addAttribute("handlerMethods", this.handlerMapping.getHandlerMethods());
//    }
    @RequestMapping(value = "/show", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap mostrar() {
        ModelMap mm = new ModelMap();
        Map<RequestMappingInfo, HandlerMethod> mapa = this.handlerMapping.getHandlerMethods();
        for (Map.Entry<RequestMappingInfo, HandlerMethod> entry : mapa.entrySet()) {
            RequestMappingInfo requestMappingInfo = entry.getKey();
            HandlerMethod handlerMethod = entry.getValue();
            mm.addAttribute(handlerMethod.getMethod().toGenericString(), requestMappingInfo.getPatternsCondition().toString());
        }
        return mm;
    }

    @RequestMapping(value = "/empresas", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap empresas() {
        ModelMap mm = new ModelMap();
        mm.addAttribute(EntityManagerFactoryService.getEmpresas());
        return mm;
    }

    @RequestMapping(value = "{ctx}/soundex", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap soundex(@PathVariable String ctx, @RequestParam String valor) {
        ModelMap mm = new ModelMap();
        try {
//            String hql = "select o from ClienteSintetico o where convert(nome,'UTF-8','SQL_ASCII') like convert('%" + valor + "%','UTF-8','SQL_ASCII')";
//            mm.addAttribute("clientes", clienteSinteticoDao.findByParam(ctx, hql, new HashMap<String, Object>()));
            mm.addAttribute("clientes", clienteSinteticoDao.listOfObjects(ctx,
                    "select convert_to(nome, 'SQL_ASCII') from ClienteSintetico where convert_to(upper(nome), 'SQL_ASCII') like ('%' || convert_to(upper('" + valor + "'),'SQL_ASCII') || '%')"));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/pushProximosAgendados", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap pushProximosAgendados(@PathVariable String ctx, final String dataAtual, final String tipoLembrete) {
        ModelMap mm = new ModelMap();
        try {
            TipoLembreteEnum tEnum = TipoLembreteEnum.valueOf(tipoLembrete);
            
            List<Agendamento> l = agendamentoService.consultarPrevistosNosProximosMinutos(ctx,
                    Calendario.getDate(Calendario.MASC_DATAHORA, dataAtual), null, null,
                    tEnum.equals(TipoLembreteEnum.DUAS_HORAS) ? 0 : tEnum.getnMinutos() - 120,
                    tEnum);
            List<AgendamentoJSON> arr = new ArrayList<AgendamentoJSON>();
            for (Agendamento agendamento : l) {
                agendamento = notificacaoService.loadLazyAttributes(ctx, agendamento);
                AgendamentoJSON json = new AgendamentoJSON(agendamento.getCodigo(),
                        Calendario.getData(agendamento.getInicio(), Calendario.MASC_DATAHORA),
                        Calendario.getHora(agendamento.getInicio(), Calendario.MASC_HORA),
                        Calendario.getHora(agendamento.getFim(), Calendario.MASC_HORA),
                        agendamento.getTipoEvento().getNome() + " com " + agendamento.getNomeProfessor(),
                        agendamento.getStatus().getDescricao(), agendamento.getNomeProfessor(),
                        agendamento.getStatus().getCor(), agendamento.getInicio().getTime());
                arr.add(json);
                Usuario u = usuarioService.obterPorAtributo(ctx, "cliente.codigo", agendamento.getCliente().getCodigo());
                if (u != null) {
                    notificacaoService.notificarLembreteAgendamento(ctx, agendamento, TipoLembreteEnum.valueOf(tipoLembrete));
                }
            }
            mm.addAttribute(arr);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    private void notificar(final String ctx, Agendamento agendamento, TipoLembreteEnum tipoLembrete) throws ServiceException {
        notificacaoService.notificarLembreteAgendamento(ctx, agendamento, tipoLembrete);
    }

    private void notificar(final String ctx, Agendamento agendamento, final TipoLembreteEnum tipoLembrete,
            final TipoNotificacaoEnum tipoNotificacao,
            final String idMensagem) throws ServiceException {
        notificacaoService.notificarAgendamentoNovoOuAlterado(ctx, agendamento, tipoLembrete, tipoNotificacao, idMensagem);
    }

    private void notificar(final String ctx, ProgramaTreino programa, TipoLembreteEnum tipoLembrete,
            TipoNotificacaoEnum tipoNotificacao, final String msg) throws ServiceException {
        notificacaoService.notificarPrograma(ctx, programa, Calendario.hoje(), tipoLembrete, tipoNotificacao, msg);
    }

    @RequestMapping(value = "{ctx}/pushTaskAgendamento", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap pushTaskAgendamento(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            Uteis.logar(null, "Started -> " + TaskNotificationAgendamento.class.getSimpleName() + " " + ctx);
            //
            List<Agendamento> listaProximas2Horas = agendamentoService.consultarPrevistosNosProximosMinutos(ctx,
                    Calendario.hoje(), null, null,0, TipoLembreteEnum.DUAS_HORAS);
            for (Agendamento agendamento : listaProximas2Horas) {
                notificar(ctx,agendamento, TipoLembreteEnum.DUAS_HORAS);
            }
            
            List<Agendamento> listaProximasUmDia = agendamentoService.consultarPrevistosNosProximosMinutos(ctx,
                    Calendario.hoje(), null, null, TipoLembreteEnum.UM_DIA.getnMinutos() - 120, TipoLembreteEnum.UM_DIA);
            for (Agendamento agendamento : listaProximasUmDia) {
                notificar(ctx,agendamento, TipoLembreteEnum.UM_DIA);
            }
            //            
            List<Agendamento> listaNovos = agendamentoService.consultarAgendamentosNovosOuAlterados(ctx,
                    null, null, TipoLembreteEnum.AGENDAMENTO_NOVO);
            for (Agendamento agendamento : listaNovos) {
                notificar(ctx, agendamento, TipoLembreteEnum.AGENDAMENTO_NOVO,
                        TipoNotificacaoEnum.AGENDAMENTO_NOVO, "lembrete.agendamento.novo");
            }
            List<Agendamento> listaAlterados = agendamentoService.consultarAgendamentosNovosOuAlterados(ctx,
                    null, null, TipoLembreteEnum.AGENDAMENTO_ALTERADO);
            for (Agendamento agendamento : listaAlterados) {
                notificar(ctx, agendamento, TipoLembreteEnum.AGENDAMENTO_ALTERADO,
                        TipoNotificacaoEnum.AGENDAMENTO_ALTERADO, "lembrete.agendamento.alterado");
            }
            mm.addAttribute(STATUS_SUCESSO);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/pushNovosAgendamentoOuAlterados", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap pushNovosAgendamentoOuAlterados(@PathVariable String ctx,
            final String tipoLembrete, final String tipoNotf, final String idMensagem) {
        ModelMap mm = new ModelMap();
        try {
            List<Agendamento> l = agendamentoService.consultarAgendamentosNovosOuAlterados(ctx, null, null,
                    TipoLembreteEnum.valueOf(tipoLembrete));
            List<AgendamentoJSON> arr = new ArrayList<AgendamentoJSON>();
            for (Agendamento agendamento : l) {
                agendamento = notificacaoService.loadLazyAttributes(ctx, agendamento);
                AgendamentoJSON json = new AgendamentoJSON(agendamento.getCodigo(),
                        Calendario.getData(agendamento.getInicio(), Calendario.MASC_DATAHORA),
                        Calendario.getHora(agendamento.getInicio(), Calendario.MASC_HORA),
                        Calendario.getHora(agendamento.getFim(), Calendario.MASC_HORA),
                        agendamento.getTipoEvento().getNome() + " com " + agendamento.getNomeProfessor(),
                        agendamento.getStatus().getDescricao(), agendamento.getNomeProfessor(),
                        agendamento.getStatus().getCor(), agendamento.getInicio().getTime());
                arr.add(json);
                Usuario u = usuarioService.obterPorAtributo(ctx, "cliente.codigo", agendamento.getCliente().getCodigo());
                if (u != null) {
                    notificacaoService.notificarAgendamentoNovoOuAlterado(ctx,
                            agendamento, TipoLembreteEnum.valueOf(tipoLembrete),
                            TipoNotificacaoEnum.valueOf(tipoNotf), idMensagem);
                }
            }
            mm.addAttribute(arr);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/pushSolicitarAgendamentoRenovacao", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap pushSolicitarAgendamentoRenovacao(@PathVariable String ctx, final String dataAtual) {
        ModelMap mm = new ModelMap();
        try {
            Date d = Calendario.getDate(Calendario.MASC_DATA, dataAtual);
            ConfiguracaoSistema configSeriesSet = configService.consultarPorTipo(ctx, ConfiguracoesEnum.AGRUPAMENTO_SERIES_SET);
            List<ProgramaTreino> lista7Dias = programaTreinoService.consultarPrevistosRenovarNosProximosDias(ctx,
                    Calendario.getDate(Calendario.MASC_DATA, dataAtual), null, null, TipoLembreteEnum.SETE_DIAS);
            List<ProgramaTreinoJSON> arr = new ArrayList<ProgramaTreinoJSON>();
            for (ProgramaTreino programa : lista7Dias) {
                programa = programaTreinoService.obterPorId(ctx, programa.getCodigo());
                ProgramaTreinoJSON p = ProgramaTreinoJSONControle.preencherProgramaJSON(programa, ctx, null, configSeriesSet.getValorAsBoolean(), "", null);
                arr.add(p);
                notificacaoService.notificarPrograma(ctx, programa, d, TipoLembreteEnum.SETE_DIAS,
                        TipoNotificacaoEnum.SOLICITAR_RENOVACAO,
                        viewUtils.getMensagem("programa.agendarRenovacao"));
            }
            mm.addAttribute(STATUS_SUCESSO, arr);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/pushLembrarCompromisso", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap pushLembrarCompromisso(@PathVariable String ctx, final String dataAtual) {
        ModelMap mm = new ModelMap();
        try {
            Date d = Calendario.getDate(Calendario.MASC_DATAHORA, dataAtual);
            List<ProgramaTreino> lista7Dias = programaTreinoService.consultarAtrasados(ctx,
                    Calendario.getDate(Calendario.MASC_DATA, dataAtual), null, null, TipoNotificacaoEnum.LEMBRAR_ALUNO_COMPROMISSO,
                    TipoLembreteEnum.SETE_DIAS);
            List<String> arr = new ArrayList<String>();
            for (ProgramaTreino programa : lista7Dias) {
                programa = programaTreinoService.obterPorId(ctx, programa.getCodigo());
                ProgramaTreinoAndamento programaTreinoAndamento = programaTreinoService.obterAndamento(ctx, programa);
                if (programaTreinoAndamento != null) {
                    if (programaTreinoAndamento.getUltimoTreino() != null) {
                        final String msg = String.format(viewUtils.getMensagem("programa.lembrarCompromisso"), new Object[]{
                            programa.getDiasPorSemana(),
                            Calendario.getData(programaTreinoAndamento.getUltimoTreino(), Calendario.MASC_DATAHORA)});
                        notificacaoService.notificarPrograma(ctx, programa, d, TipoLembreteEnum.SETE_DIAS,
                                TipoNotificacaoEnum.LEMBRAR_ALUNO_COMPROMISSO, msg);
                        arr.add(msg);
                    }
                } else {
                    final String msg = String.format(viewUtils.getMensagem("programa.comecarCompromisso"), new Object[]{
                        programa.getDiasPorSemana()});
                    notificacaoService.notificarPrograma(ctx, programa, d, TipoLembreteEnum.SETE_DIAS,
                            TipoNotificacaoEnum.LEMBRAR_ALUNO_COMPROMISSO, msg);
                    arr.add(msg);
                }
            }
            mm.addAttribute(STATUS_SUCESSO, arr);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/obterNomeFotos", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap obterNomeFotos(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            AtividadeAnimacaoDao atividadeAnimacaoDao = (AtividadeAnimacaoDao) UtilContext.getBean(AtividadeAnimacaoDao.class);
            List<AtividadeAnimacao> lista = atividadeAnimacaoDao.findAll(ctx);
            List<AnimacaoJSON> arrJson = new ArrayList<AnimacaoJSON>();
            for (AtividadeAnimacao atvAnim : lista) {
                if (atvAnim.getAtividade() == null || atvAnim.getAnimacao() == null) {
                    atividadeAnimacaoDao.delete(ctx, atvAnim);
                    Uteis.logar(null, "Excluiu órfão -> '" + atvAnim.getCodigo());
                } else {
                    AnimacaoJSON img = new AnimacaoJSON(atvAnim.getAnimacao().getUrl(), atvAnim.getAtividade().getNome());
                    Uteis.logar(null, ctx + " -> update midia set titulo = '" + img.getAtividade() + "' where tipo = 0 and nome = '" + img.getUrl() + "';");
                    arrJson.add(img);
                }
            }
            Ordenacao.ordenarLista(arrJson, "atividade");
            mm.addAttribute(arrJson);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/obterFichasPredefinidas", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap obterFichasPredefinidas(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            boolean treinoIndependente = false;
            try {
                treinoIndependente = SuperControle.independente(ctx);
            } catch (Exception e) {
                e.printStackTrace();
            }
            ConfiguracaoSistema configSeriesSet = configService.consultarPorTipo(ctx, ConfiguracoesEnum.AGRUPAMENTO_SERIES_SET);
            List<Ficha> l = fichaService.obterFichasPredefinidas(ctx, true);
            List<FichaJSON> arr = new ArrayList<FichaJSON>();
            Map<Integer, AtividadeJSON> mapaTodasAtividades = new HashMap<Integer, AtividadeJSON>();
            for (Ficha ficha : l) {
                FichaJSON fichaJSON = FichaJSON.obterJSON(treinoIndependente, null, null, ficha,
                        SuperControle.getUrlImagem(ctx), mapaTodasAtividades, configSeriesSet.getValorAsBoolean(), ctx, null);
                arr.add(fichaJSON);
            }
            mm.addAttribute(arr);
            mm.addAttribute(mapaTodasAtividades.values());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/resetPwd", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap resetPwd(@PathVariable String ctx, @RequestParam final String userName,
            @RequestParam final String pwd) {
        ModelMap mm = new ModelMap();
        try {
            Usuario u = usuarioDao.findObjectByAttribute(ctx, "userName", userName);
            if (u != null) {
                u.setSenha(Uteis.encriptar(pwd));
                usuarioDao.update(ctx, u);
                mm.addAttribute(STATUS_SUCESSO);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/povoar", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap povoar(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            PovoadorService povoador = (PovoadorService) UtilContext.getBean(PovoadorService.class);
            povoador.run(ctx);
            mm.addAttribute(STATUS_SUCESSO);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/povoarAndamento", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap povoarAndamento(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            programaTreinoService.povoarAndamentoTreino(ctx);
            mm.addAttribute(STATUS_SUCESSO);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    //
    @RequestMapping(value = "{ctx}/obterMidiasRemotas", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap obterMidiasRemotas(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        List<Midia> resultado = new ArrayList<Midia>();
        try {
            List<Midia> lista = AdmAppWSConsumer.obterMidias(ctx);
            for (Midia midia: lista) {
                resultado.add(midia);
            }

            mm.addAttribute(RETURN, resultado);
        } catch (Exception e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, e);
        }
        return mm;
    }

    @RequestMapping(value = "/obterURLs", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap obterURLs(@RequestParam Boolean clean) {
        ModelMap mm = new ModelMap();
        try {
//            List<StatsJSON> nova = new ArrayList<StatsJSON>();
//            if (clean) {
//                for (StatsJSON statsJSON : tmp) {
//                    if (statsJSON.getUrl().contains("/login") || statsJSON.getUrl().contains("/status")) {
//                        nova.add(statsJSON);
//                    }
//                }
//            }
//            Ordenacao.ordenarLista(clean ? nova : tmp, "url");
//            mm.addAttribute(clean ? nova : tmp);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/audit", method = {RequestMethod.POST})
    public @ResponseBody
    ModelMap audit(@PathVariable String ctx,
            @RequestParam final String classCanonical,
            @RequestParam final String attribute,
            @RequestParam Object value) {
        ModelMap mm = new ModelMap();
        try {

            mm.addAttribute(new ArrayList<>());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/simularAdmWS", method = {RequestMethod.POST})
    public @ResponseBody
    ModelMap simularAdmWS(@PathVariable String ctx,
            @RequestParam final String url) {
        ModelMap mm = new ModelMap();
        try {
            AdmWSConsumer service = UtilContext.getBean(AdmWSConsumer.class);
            List<EmpresaWS> l = service.obterEmpresas(ctx);
            if (l != null && !l.isEmpty()) {
                mm.addAttribute(l);
            } else {
                mm.addAttribute("Empresas não encontradas");
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "/executarMigracao", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap executarMigracao(@RequestParam String key,
            @RequestParam final String metodo) {
        ModelMap mm = new ModelMap();
        List<String> resultado = new ArrayList();
        try {
            MigracaoDadosJPAService ms = (MigracaoDadosJPAService) UtilContext.getBean(MigracaoDadosJPAService.class);
            if (key != null && metodo != null) {
                if (key.equals("todas")) {
                    Set<String> keys = EntityManagerFactoryService.getEmpresas().keySet();
                    for (String k : keys) {
                        try {
                            UtilReflection.invoke(ms, metodo, new Class[]{String.class}, new Object[]{k});
                            resultado.add(k + " " + STATUS_SUCESSO);
                        } catch (Exception e) {
                            resultado.add(STATUS_ERRO + " " + k + " " + e.getMessage());
                        }
                    }
                } else if (!key.isEmpty()) {
                    UtilReflection.invoke(ms, metodo, new Class[]{String.class}, new Object[]{key});
                    resultado.add(key + " " + STATUS_SUCESSO);
                }
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(FotosJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        mm.addAttribute(resultado);
        return mm;
    }
    
    @RequestMapping(value = "{ctx}/expirarCreditos", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap expirarCreditos(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            CreditoPersonalService creditoService = (CreditoPersonalService) UtilContext.getBean(CreditoPersonalService.class);
            creditoService.expirarCreditos(ctx, Calendario.hoje());
            mm.addAttribute(STATUS_SUCESSO);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/atualizarProfessorCarteiraProgramaVirgente", method = RequestMethod.POST)
    public @ResponseBody ModelMap atualizarProfessorCarteiraProgramaVirgente(@PathVariable String ctx, @RequestParam String matricula) {
        ModelMap mm = new ModelMap();
        try {
            programaTreinoService.atualizarProfessorCarteiraProgramaVirgente(ctx, matricula);
            mm.addAttribute(STATUS_SUCESSO);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
}
