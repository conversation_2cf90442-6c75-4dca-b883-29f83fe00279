package br.com.pacto.controller.json.agendamento;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.cliente.perfil.FichaDoDiaDTO;
import br.com.pacto.service.intf.agendatotal.AgendaCardsService;
import br.com.pacto.service.intf.agendatotal.AlunosAulaService;
import br.com.pacto.service.intf.locacao.AlunoLocacaoHorarioPlayService;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by alcides on 14/07/2023.
 */
@Controller
@Api(tags = "Agendamentos")
@RequestMapping("/psec/agenda-cards")
public class AgendaCardsController {

    @Autowired
    private AgendaCardsService agendaCardsService;
    @Autowired
    private AlunosAulaService alunosAulaService;
    @Autowired
    private AlunoLocacaoHorarioPlayService alunoLocacaoHorarioPlayService;

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @ApiOperation(value="Retorna a agenda", notes = "Este método retorna a agenda", tags = "Agendamentos")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Agenda encontrada", response = ObterCardsResponseDTO.class),
    })
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterCards(@ApiParam(value = "ID da empresa", defaultValue = "1", required = true) @RequestHeader(value = "empresaId") Integer empresaId,
                                                          @ApiParam(value = "Referência", defaultValue = "20250310", required = true) @RequestParam(value = "ref") Long ref,
                                                          @ApiParam(value = "Período", defaultValue = "DIA", required = true) @RequestParam(value = "periodo") PeriodoFiltrarEnum periodo,
                                                          @ApiParam(value = "Filtros", defaultValue = "{\"tipo\" : \"TODAS\", \"professoresIds\" : [1, 2], \"ambientesIds;\" : [1, 2], \"modalidadesIds;\" : [1, 2], \"turmaId;\" : 1, \"turno\" : \"\", \"search\" : \"\", \"situacaoHorario\" : \"\"}", required = false) @RequestParam(value = "filtros", required = false) String filtros,
                                                          HttpServletRequest request) throws JSONException {
        try {
            FiltroTurmaDTO filtroTurmaDTO = new FiltroTurmaDTO(new JSONObject(filtros));
            return ResponseEntityFactory.ok(agendaCardsService.montarCards(request, empresaId,
                        Calendario.getDate("yyyyMMdd", ref.toString()),
                        periodo, filtroTurmaDTO));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar montar agenda cards", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno("erro_agenda", e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/aula-detalhada/{horario-turma-id}/{dia}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aulaDetalhada(
            @RequestHeader("empresaId") Integer empresaId,
            @PathVariable("horario-turma-id") Integer aulaHorario,
            @PathVariable("dia") String dia,
            PaginadorDTO paginadorDTO,
            HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(alunosAulaService.alunosAula(empresaId,
                    Calendario.getDate("yyyyMMdd", dia), aulaHorario, paginadorDTO, request), paginadorDTO);
        } catch (ServiceException e) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (ParseException e) {
            return ResponseEntityFactory.erroInterno("erro_format data", "Erro ao formatar a data de ref");
        }
    }

    @ApiIgnore
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/obterIdAula/{empresaId}/{horario-turma-id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterIdAula(
            @PathVariable("empresaId") Integer empresaId,
            @PathVariable("horario-turma-id") Integer horarioTurma) {
        try {
            return ResponseEntityFactory.ok(agendaCardsService.obterIdAula(empresaId, horarioTurma));
        } catch (Exception ex) {
            return ResponseEntityFactory.erroInterno("nao_encontrado", "Aula não encontrada");
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/locacao-play-detalhada/{locacao-horario-id}/{ambiente}/{dia}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> locacaoPlayDetalhada(
            @RequestHeader("empresaId") Integer empresaId,
            @PathVariable("locacao-horario-id") Integer locacaoHorario,
            @PathVariable("ambiente") Integer ambiente,
            @PathVariable("dia") String dia,
            PaginadorDTO paginadorDTO,
            HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(alunoLocacaoHorarioPlayService.alunosLocacaoPlay(empresaId,
                    Calendario.getDate("yyyyMMdd", dia), locacaoHorario, ambiente, paginadorDTO, request), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (ParseException e) {
            return ResponseEntityFactory.erroInterno("erro_format data", "Erro ao formatar a data de ref");
        }
    }

    @ApiIgnore
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/disponibilidades/{tipo}/{dia}/{horaSelecionada}/{horaSelecionadaFinal}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aulaDetalhada(
            @RequestHeader("empresaId") Integer empresaId,
            @PathVariable("tipo") String tipo,
            @PathVariable("dia") String dia,
            @PathVariable("horaSelecionada") String horaSelecionada,
            @PathVariable("horaSelecionadaFinal") String horaSelecionadaFinal,
            PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(agendaCardsService.disponibilidades(empresaId,
                    new Date(Long.parseLong(dia)), horaSelecionada, horaSelecionadaFinal, tipo, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/disponibilidadesV2/{tipo}/{dia}/{horaSelecionada}/{horaSelecionadaFinal}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aulaDetalhadaV2(
            @RequestHeader("empresaId") Integer empresaId,
            @PathVariable("tipo") String tipo,
            @PathVariable("dia") String dia,
            @PathVariable("horaSelecionada") String horaSelecionada,
            @PathVariable("horaSelecionadaFinal") String horaSelecionadaFinal,
            PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(agendaCardsService.disponibilidadesV2(empresaId,
                    new Date(Long.parseLong(dia)), horaSelecionada, horaSelecionadaFinal, tipo, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
