package br.com.pacto.controller.json.parceiros;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by <PERSON><PERSON> on 26/09/2018.
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ParceiroTO {

    private Integer id;
    private String nome;
    private String dadosImagem;
    private String tipoImagem;
    private Boolean situacao;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDadosImagem() {
        return dadosImagem;
    }

    public void setDadosImagem(String dadosImagem) {
        this.dadosImagem = dadosImagem;
    }

    public Boolean getSituacao() {
        return situacao;
    }

    public void setSituacao(Boolean situacao) {
        this.situacao = situacao;
    }

    public String getTipoImagem() {
        return tipoImagem;
    }

    public void setTipoImagem(String tipoImagem) {
        this.tipoImagem = tipoImagem;
    }
}
