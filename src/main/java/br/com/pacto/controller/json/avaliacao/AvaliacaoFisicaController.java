package br.com.pacto.controller.json.avaliacao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.anamnese.Anamnese;
import br.com.pacto.bean.anamnese.RespostaCliente;
import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.bean.avaliacao.AvaliacaoFisicaDTOUpdate;
import br.com.pacto.bean.avaliacao.AvaliacaoVo2AstrandDTO;
import br.com.pacto.bean.avaliacao.ImportarAvaliacaoBioimpedanciaDTO;
import br.com.pacto.bean.avaliacao.RespostaClienteParQ;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.empresa.IdiomaBancoEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.ConfiguracaoController;
import br.com.pacto.dao.intf.avaliacao.AvaliacaoFisicaDao;
import br.com.pacto.dao.intf.avaliacao.ParQDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.intf.anamnese.AnamneseService;
import br.com.pacto.service.intf.avaliacao.AvaliacaoFisicaService;
import br.com.pacto.service.intf.avaliacao.EvolucaoFisicaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.ServletContextAware;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.pacto.controller.json.base.SuperControle.STATUS_ERRO;


/**
 * Created by paulo 09/11/2018
 */
@Controller
@RequestMapping("/psec/avaliacoes-fisica")
public class AvaliacaoFisicaController implements ServletContextAware {

    private final AvaliacaoFisicaService avaliacaoFisicaService;
    private final ClienteSinteticoService clienteSinteticoService;
    private final UsuarioService usuarioService;
    private final SessaoService sessaoService;
    private final EvolucaoFisicaService evolucaoFisicaService;
    private ServletContext sc;
    @Autowired
    private AvaliacaoFisicaDao avaliacaoFisicaDao;
    @Autowired
    private ClienteSinteticoService clienteService;

    @Autowired
    private ParQDao parQDao;

    @Autowired
    private AnamneseService anamneseService;

    @Override
    public void setServletContext(ServletContext servletContext) {
        this.sc = servletContext;
    }

    @Autowired
    public AvaliacaoFisicaController(AvaliacaoFisicaService avaliacaoFisicaService, ClienteSinteticoService clienteSinteticoService,
                                     UsuarioService usuarioService,
                                     SessaoService sessaoService, EvolucaoFisicaService evolucaoFisicaService) {
        this.evolucaoFisicaService = evolucaoFisicaService;
        Assert.notNull(usuarioService, "O serviço de usuario não foi injetado corretamente");
        Assert.notNull(avaliacaoFisicaService, "O serviço de avaliação física não foi injetado corretamente");
        Assert.notNull(sessaoService, "O serviço de sessaoService não foi injetado corretamente");
        Assert.notNull(clienteSinteticoService, "O serviço de clienteSinteticoService não foi injetado corretamente");
        this.avaliacaoFisicaService = avaliacaoFisicaService;
        this.clienteSinteticoService = clienteSinteticoService;
        this.sessaoService = sessaoService;
        this.usuarioService = usuarioService;
    }


    @ResponseBody
    @RequestMapping(value = "/alunos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterCatalogoAlunos(@RequestParam(value = "nome", required = false) String filtroNome,
                                                                   @RequestHeader(value = "empresaId") Integer empresaId ,
                                                                   HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterCatalogoAlunosAvaliacaoFisica(request, filtroNome, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar catalogar os alunos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/alunos/{id}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<EnvelopeRespostaDTO> salvarAvaliacaoFisica(@PathVariable(value = "id") Integer id,
                                                              @RequestBody AvaliacaoFisicaDTOUpdate avaliacaoFisica) {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.inserir(avaliacaoFisica, id));
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar a avaliação física", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<EnvelopeRespostaDTO> alterarAvaliacaoFisica(@PathVariable(value = "id") Integer id,
                                                               @RequestBody AvaliacaoFisicaDTOUpdate avaliacaoFisica) {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.alterar(avaliacaoFisica, id));
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar a avaliação física", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{id}/vo2", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<EnvelopeRespostaDTO> alterarVo2AvaliacaoFisica(@PathVariable(value = "id") Integer id,
                                                                  @RequestBody AvaliacaoVo2AstrandDTO dados) {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.alterarVo2(id, dados));
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar cálcular Vo2 da avaliação física", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/alunos/{id}/avaliacao-recente", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliacaoRecente(@PathVariable("id") final Integer id) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            return ResponseEntityFactory.ok(avaliacaoFisicaService.avaliacaoAtual(ctx, id, usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId())));

        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter a avaliação vigente", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/alunos/{id}/todas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> todas(@PathVariable("id") final Integer id) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(avaliacaoFisicaService.todasCliente(ctx, id, usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId())));

        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter a avaliação vigente", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{id}/imprimir", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> imprimir(@PathVariable("id") final Integer id,
                                                        @RequestParam(value = "idiomaBanco", required = false) final String idiomaBanco, HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            String language = UteisValidacao.emptyString(idiomaBanco) ? "PT" : IdiomaBancoEnum.getFromOrdinal(Integer.valueOf(idiomaBanco)).name();
            return ResponseEntityFactory.ok(avaliacaoFisicaService.comporUrlPdf(ctx, id, request, true, language));
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }
    @ResponseBody
    @RequestMapping(value = "/imprimirComparar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> imprimirComparar(@RequestParam(value = "avaliacoes", required = false) List<Integer> avaliacoes, HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            List<AvaliacaoFisica> listaAvaliacao = new ArrayList<>();
            for (Integer i : avaliacoes ){
                AvaliacaoFisica avaliacaoFisica = new AvaliacaoFisica();
                avaliacaoFisica = avaliacaoFisicaService.obterPorId(ctx, i);
                listaAvaliacao.add(avaliacaoFisica);
            }
            ViewUtils bean = UtilContext.getBean(ViewUtils.class);
            return ResponseEntityFactory.ok(avaliacaoFisicaService.gerarPDFComparativo(
                    ctx, listaAvaliacao, usuario, bean, request,
                    null, false));
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }
    @ResponseBody
    @RequestMapping(value = "/enviarComparativo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> enviarComparativo(@RequestParam(value = "avaliacoes", required = false) List<Integer> avaliacoes,
                                                                 boolean email,
                                                                 Integer alunoId, HttpServletRequest request) {
        try {
            if(email) {
                avaliacaoFisicaService.enviarComparativoSpa(avaliacoes, request, sc);
                return ResponseEntityFactory.ok();
            }else {
                return ResponseEntityFactory.ok(avaliacaoFisicaService.montarLinksComparativoWpp(alunoId, avaliacoes, request, UtilContext.getBean(ViewUtils.class)));
            }
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{id}/enviar-por-email", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> enviar(@PathVariable("id") final Integer id,
                                                      @RequestParam("email")final String email,
                                                      @RequestHeader("empresaId") Integer empresaId,
                                                      HttpServletRequest request,
                                                      @RequestParam(value = "idiomaBanco", required = false) final String idiomaBanco) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            IdiomaBancoEnum language = IdiomaBancoEnum.getFromOrdinal(Integer.valueOf((UteisValidacao.emptyString(idiomaBanco) ? "0" : idiomaBanco)));
            avaliacaoFisicaService.enviarEmailAvaliacao(empresaId,ctx, id, email, usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId()), request, sc, language);
            return ResponseEntityFactory.ok();
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{id}/enviar-por-wpp", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> enviarWpp(@PathVariable("id") final Integer id,
                                                      @RequestParam("telefone")final String telefone, HttpServletRequest request,
                                                         @RequestParam(value = "idiomaBanco", required = false) final String idiomaBanco) {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.montarLinksWpp(id, telefone, request, (UteisValidacao.emptyString(idiomaBanco) ? "0" : idiomaBanco)));
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirAnamnese(@PathVariable("id") final Integer id) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            avaliacaoFisicaService.excluir(ctx, avaliacaoFisicaService.obterPorId(ctx, id), null);
            return ResponseEntityFactory.ok(true);
        } catch (ValidacaoException e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @RequestMapping(value = "origem/{id}/{origem}", method = RequestMethod.DELETE)
    public @ResponseBody
    ResponseEntity<EnvelopeRespostaDTO> excluirAnamneseOrigem(@PathVariable("id") final Integer id,
                                                              @PathVariable("origem") final String origem) {

        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            avaliacaoFisicaService.excluir(ctx, avaliacaoFisicaService.obterPorId(ctx, id),origem);
            return ResponseEntityFactory.ok(true);
        } catch (ValidacaoException e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/parq-perguntas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterPerguntasParQ(@RequestParam(required = false) final Integer codigoAnamnese) throws Exception {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.obterPerguntasParQ(null, codigoAnamnese));
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter parq-perguntas anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/evolucao-fisica/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterEvolucaoFisica(@PathVariable("id") Integer id) {
        try {
            return ResponseEntityFactory.ok(evolucaoFisicaService.obterEvolucaoFisica(id));
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter evolucao fésica", e);
            if (e.getMessage().equalsIgnoreCase("Não contém avaliacao evolucão")) {
                return ResponseEntityFactory.erroRegistroNotFoun(e.getChaveExcecao(), e.getMessage());
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterAvaliacaoFisica(@PathVariable() Integer id) {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.obterAvaliacaoFisica(id));
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter avaliacão física");
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/valores-rml/{alunoId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterValoresRML(@PathVariable() Integer alunoId) {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.obterTabelaRML(alunoId));
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter avaliacão física");
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/alunos/{id}/produto-vigente", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> produtoVigente(@PathVariable("id") final Integer id,
                                                              @RequestParam("idProduto") final String idProduto) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            String vigente = "true";
            Integer produtoLancado = null;
            IntegracaoCadastrosWSConsumer integracaoWS = UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
            ClienteSinteticoService cliente = UtilContext.getBean(ClienteSinteticoService.class);
            ClienteSintetico clienteSintetico = cliente.obterPorId(ctx, id);
            String produtoVigente = integracaoWS.verificarAlunoTemProdutoVigenteRetornandoQuantidade(Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg), ctx, clienteSintetico.getCodigoCliente(),
                    Integer.valueOf(idProduto));
            JSONArray produtos = new JSONArray(produtoVigente);
            if(produtos.length() == 0){
                vigente = "O aluno não tem um produto de Avaliação Física vigente.";
            }else{
                for(int i = 0; i < produtos.length(); i++){
                    List<AvaliacaoFisica> avLancada = avaliacaoFisicaService.obterAvaliacaoMovProduto(ctx, produtos.getJSONObject(i).getInt("produtoId"));
                    if(UteisValidacao.emptyList(avLancada) || produtos.getJSONObject(i).getInt("quantidade") > avLancada.size()){
                        produtoLancado = produtos.getJSONObject(i).getInt("produtoId");
                    }
                }
                if(produtoLancado == null){
                    vigente = "O produto vigente já foi usado.";
                }
            }
            return ResponseEntityFactory.ok(vigente);

        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter a avaliação vigente", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }
    @ResponseBody
    @RequestMapping(value = "/alunos/{id}/{idProduto}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<EnvelopeRespostaDTO> salvarAvaliacaoFisicaComProduto(@PathVariable(value = "id") Integer id,
                                                                        @RequestBody AvaliacaoFisicaDTOUpdate avaliacaoFisica,
                                                                        @PathVariable(value = "idProduto") Integer idProduto) {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.inserir(avaliacaoFisica, id, idProduto));
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar a avaliação física", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/extrairAvaliacaoBioimpedancia", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> extrairAvaliacaoBioimpedancia(@RequestBody ImportarAvaliacaoBioimpedanciaDTO avaliacaoBioimpedanciaDTO) {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.extrairInformacoesAvaliacaoBioimpedancia(avaliacaoBioimpedanciaDTO));
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar extrair informações da avaliação bioimpedância", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/processo/remover-avaliacao-postural-duplicada", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAvaliacaoPosturalDuplicada(@RequestParam("todas") final boolean todas,
                                                                                 @RequestParam("codigoAvaliacaoPostural") final Integer codigoAvaliacaoPostural) {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            String retorno = avaliacaoFisicaService.processoRemoverAvaliacaoPosturalDuplicada(ctx, todas, codigoAvaliacaoPostural);
            return ResponseEntityFactory.ok(retorno);
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo de remoção de avaliacaopostural duplicadas", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/v2/listarAlunosParQ/{codigoCliente}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarAlunosParQV2(@PathVariable final Integer codigoCliente,
                                                                  PaginadorDTO paginadorDTO,
                                                                  HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer empresa = sessaoService.getUsuarioAtual().getEmpresaAtual();

            ClienteSintetico cliente = clienteSinteticoService.consultarSimplesPorCodigoCliente(ctx, codigoCliente);
            if (cliente == null || UteisValidacao.emptyNumber(cliente.getCodigo())) {
                return ResponseEntityFactory.erroRegistroNotFoun("Cliente não encontrado", "Cliente não encontrado");
            } else {
                JSONObject resultado = avaliacaoFisicaService.consultarParQsAluno(ctx, empresa, cliente.getCodigo(), paginadorDTO);
                Map<String, Object> resultadoMap = resultado.toMap();
                EnvelopeRespostaDTO envelope = EnvelopeRespostaDTO.of(resultadoMap, paginadorDTO);
                return ResponseEntityFactory.ok(envelope);
            }
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao listar alunos com PAR-Q", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @RequestMapping(value = "/{ctx}/imprimirParQAssinaturaDigital", method = RequestMethod.GET)
    public
    @ResponseBody
    ModelMap imprimirParQAssinaturaDigital(@PathVariable final String ctx,
                                           @RequestParam final String matricula,
                                           @RequestParam(required = false) final String codigoRespostaParq,
                                           HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            Integer codigoResposta = UteisValidacao.emptyString(codigoRespostaParq) ? 0 : Integer.parseInt(codigoRespostaParq);
            ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx, matricula);
            RespostaClienteParQ rcp = new RespostaClienteParQ();
            if (!UteisValidacao.emptyNumber(codigoResposta)) {
                rcp = parQDao.consultarRespostaParQPorCodigo(ctx, codigoResposta);
            } else {
                rcp = parQDao.consultarRespostaParQPorCliente(ctx, cliente.getCodigo());
            }

            Anamnese questionarioParq = anamneseService.consultarParq(ctx, rcp.getUsuario_codigo(), getViewUtils());
            questionarioParq.setPerguntas(anamneseService.obterPerguntasAnamnese(ctx, questionarioParq.getCodigo()));
            List<RespostaCliente> respostasCliente = avaliacaoFisicaService.obterRespostasCliente(ctx, cliente.getCodigo(), rcp.getCodigo());
            mm.addAttribute("return",avaliacaoFisicaService.gerarPDFParQAssinaturaDigital(ctx, questionarioParq, respostasCliente, rcp, getViewUtils(), request, sc, true, cliente.getCodigo()));
            if (!UteisValidacao.emptyString(rcp.getUrlAssinatura())) {
                mm.addAttribute("assinatura", rcp.getFullUrlAssinatura());
            }else{
                mm.addAttribute("assinatura", "");
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    public ViewUtils getViewUtils() {
        return UtilContext.getBean(ViewUtils.class);
    }

}
