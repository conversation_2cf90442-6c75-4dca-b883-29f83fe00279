package br.com.pacto.controller.json.cliente;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.enumerador.cliente.TiposConsultaCatalogoAlunoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 16/08/2018.
 */
@Controller
@RequestMapping("/psec/treino-alunos")
public class CatalogoAlunosController {

    private final ClienteSinteticoService clienteSinteticoService;

    @Autowired
    public CatalogoAlunosController(ClienteSinteticoService clienteSinteticoService){
        Assert.notNull(clienteSinteticoService, "O serviço de cliente sintético não foi injetado corretamente");
        this.clienteSinteticoService = clienteSinteticoService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ALUNOS)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterCatalogoAlunos(@RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                   @RequestParam(value = "filtroNome", required = false) String filtroNome,
                                                                   @RequestParam(value = "tipo", required = false) TiposConsultaCatalogoAlunoEnum tipo,
                                                                   HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterCatalogoAlunos(request, empresaId, filtroNome, tipo));
        } catch (ServiceException e) {
            Logger.getLogger(CatalogoAlunosController.class.getName()).log(Level.SEVERE, "Erro ao tentar catalogar os alunos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


}
