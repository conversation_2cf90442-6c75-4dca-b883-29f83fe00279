package br.com.pacto.controller.json.turma;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.turma.TurmaService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/turmas")
public class TurmasController extends SuperController {


    private TurmaService turmaService;

    @Autowired
    public TurmasController(TurmaService turmaService){
        Assert.notNull(turmaService, "O serviço de turmas não foi injetado corretamente");
        this.turmaService = turmaService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTurmas(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                           PaginadorDTO paginadorDTO,
                                                           @RequestHeader(value = "empresaId", required = true) Integer empresaId)throws JSONException {
        try {
            return ResponseEntityFactory.ok(turmaService.listarTurmas(filtros, paginadorDTO, empresaId), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar turmas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTurmas(@PathVariable Integer id) throws JSONException {
        try {
            return ResponseEntityFactory.ok(turmaService.obterTurma(id));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar turmas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> save(@RequestBody TurmaResponseDTO turmaDTO) {
        try {
            return ResponseEntityFactory.ok(turmaService.save(turmaDTO));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar turma", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/update", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> update(@RequestBody TurmaResponseDTO turmaDTO) {
        try {
            return ResponseEntityFactory.ok(turmaService.update(turmaDTO));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar turma", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/sync-turma-mgb/{codTurma}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> syncTurmaMgb(@PathVariable Integer codTurma, @RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            turmaService.syncTurmaMgb(empresaId, codTurma);
            return ResponseEntityFactory.ok("A sincronização foi iniciada e pode levar alguns minutos, dependendo da quantidade de horários da turma. Acompanhe as turmas sincronizadas na plataforma do MGB.");
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao sincronizar turma com mgb", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/saveOrUpdateHorario", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdateHorario(@RequestBody List<HorarioTurmaResponseDTO> horarioDTO,
                                                                   @RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(turmaService.saveOrUpdateHorario(horarioDTO, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar turma", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/saveHorario", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveHorario(@RequestBody HorarioTurmaResponseDTO horarioDTO) {
        try {
            return ResponseEntityFactory.ok(turmaService.saveHorario(horarioDTO));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar turma", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/updateHorario", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> updateHorario(@RequestBody HorarioTurmaResponseDTO horarioDTO) {
        try {
            return ResponseEntityFactory.ok(turmaService.updateHorario(horarioDTO));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar turma", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/saveAmbiente", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveAmbiente(@RequestBody AmbienteDTO ambienteDTO) {
        try {
            return ResponseEntityFactory.ok(turmaService.saveAmbiente(ambienteDTO));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar ambiente", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/saveNivelTurma", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveNivelTurma(@RequestBody NivelTurmaDTO nivelTurmaDTO) {
        try {
            return ResponseEntityFactory.ok(turmaService.saveNivelTurma(nivelTurmaDTO));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar nivel turma", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/horarioTurma/{turma}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarHorariosTurma(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                            PaginadorDTO paginadorDTO,
                                                                   @PathVariable Integer turma)throws JSONException {
        try {
            return ResponseEntityFactory.ok(turmaService.listarHorariosTurma(filtros, paginadorDTO, turma), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar turmas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/horarioTurma/remover/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerHorarioTurma(@PathVariable Integer id,
                                                                   @RequestHeader(value = "empresaId", required = true) Integer empresaId)throws JSONException {
        try {
            return ResponseEntityFactory.ok(turmaService.removerHorarioTurma(id, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover horario turma", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/horarioTurma/validar/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> validarHorarioTurma(@PathVariable Integer id)throws JSONException {
        try {
            return ResponseEntityFactory.ok(turmaService.validarExisteAlunosHorarioTurma(id));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar validar horario turma", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
