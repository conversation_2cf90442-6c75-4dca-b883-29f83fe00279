package br.com.pacto.controller.json.modalidade;


import br.com.pacto.util.enumeradores.PaletaCoresEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados para cadastro e alteração de modalidade")
public class ModalidadeTO {

    @ApiModelProperty(value = "Código único identificador da modalidade", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome da modalidade", example = "Cardio", required = true)
    private String nome;
    @ApiModelProperty(value = "Identificador da cor da modalidade. \n\n" +
            "<strong>Cores disponíveis:</strong>\n" +
            "- 0 a 9: Azul (A a J)\n" +
            "- 10 a 14: <PERSON><PERSON><PERSON> (A a E)\n" +
            "- 15 a 24: Roxo (A a J)\n" +
            "- 25 a 34: <PERSON><PERSON><PERSON><PERSON> (A a J)\n" +
            "- 35 a 44: <PERSON><PERSON> (A a J)\n" +
            "- 45 a 54: Marrom (A a J)\n" +
            "- 55 a 64: Verde (A a J)\n" +
            "- 65 a 69: Verde Limão (A a E)\n" +
            "- 70 a 74: Amarelo (A a E)", example = "4", required = true)
    private Integer corId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCorId() {
        return corId;
    }

    public void setCorId(Integer corId) {
        this.corId = corId;
    }
}
