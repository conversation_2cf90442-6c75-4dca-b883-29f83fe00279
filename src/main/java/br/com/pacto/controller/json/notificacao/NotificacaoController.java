package br.com.pacto.controller.json.notificacao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.notificacao.NotificacaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Novo controlador de notificações.
 * TODO: este controlador deve ser substituído para utilizar as features do Spring 5 quando fizermos a migração
 *
 * <AUTHOR> Karlus
 * @since 12/07/2018
 */
@Controller
@RequestMapping("/psec/notifications")
public class NotificacaoController {

    private final NotificacaoService service;

    /**
     * @param service       {@link NotificacaoService}
     */
    @Autowired
    public NotificacaoController(NotificacaoService service) {
        Assert.notNull(service, "O serviço de notificações não foi injetado corretamente");

        this.service = service;
    }

    /**
     * Recupera as notificações de um usuário.
     *
     * @return As notificações deste usuário em um JSON
     */
    @ResponseBody
    @Permissao(recursos = RecursoEnum.NOTIFICACOES)
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> getNotificacoes(PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(service.getNotificacoes(paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(NotificacaoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as notificações", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    /**
     * Marca uma notificação como LIDA
     *
     * @param id ID da notificação que será marcada como lida
     * @return Um objeto de resposta informando se tudo ocorreu bem na marcação
     */
    @ResponseBody
    @Permissao(recursos = RecursoEnum.NOTIFICACOES)
    @RequestMapping(value = "{id}/markAsSeen", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> marcarComoVisualizada(@PathVariable("id") final Integer id) {
        try {
            service.marcarComoLida(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(NotificacaoController.class.getName()).log(Level.SEVERE, "Erro ao tentar marcar a notificação como lida", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    /**
     * Procura a quantidade de notificações não lidas pelo usuário.
     *
     * @return Um objeto de resposta contendo a quantidade de notificações não lidas.
     */
    @ResponseBody
    @Permissao(recursos = RecursoEnum.NOTIFICACOES)
    @RequestMapping(value = "unseenCount", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> getQuantidadeNaoLidas() {
        try {
            return ResponseEntityFactory.ok(service.getQuantidadeNaoLidas());
        } catch (ServiceException e) {
            Logger.getLogger(NotificacaoController.class.getName()).log(Level.SEVERE, "Erro ao calcular a quantidade de notificações não lidas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
