package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.musculo.PontosMuscularEnum;
import br.com.pacto.objeto.Uteis;

/**
 * <AUTHOR> 18/01/2019
 */
public class DistribuicaoMusculoDTO {

    private PontosMuscularEnum pontoMuscular;
    private Double valor;

    public DistribuicaoMusculoDTO(PontosMuscularEnum pontoMuscular, double quantEx, double quantAtividade) {
        this.pontoMuscular = pontoMuscular;
        this.valor = Uteis.arredondarForcando2CasasDecimais(quantEx / quantAtividade);
    }

    public PontosMuscularEnum getPontoMuscular() {
        return pontoMuscular;
    }

    public void setPontoMuscular(PontosMuscularEnum pontoMuscular) {
        this.pontoMuscular = pontoMuscular;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }
}
