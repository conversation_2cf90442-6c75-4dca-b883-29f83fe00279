package br.com.pacto.controller.json.programa;

import br.com.pacto.controller.json.ficha.FichaDeTreinoGeradaPorIADTO;

import java.util.List;

public class ProgramaDeTreinoGeradoPorIADTO {
    private String nome;
    private String workoutID;
    private long inicio;
    private long termino;
    private int alunoId;
    private boolean emRevisaoProfessor;
    private boolean geradoPorIA;
    private Integer diasPorSemana;
    private List<FichaDeTreinoGeradaPorIADTO> fichas;

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getWorkoutID() {
        return workoutID;
    }

    public void setWorkoutID(String workoutID) {
        this.workoutID = workoutID;
    }

    public long getInicio() {
        return inicio;
    }

    public void setInicio(long inicio) {
        this.inicio = inicio;
    }

    public long getTermino() {
        return termino;
    }

    public void setTermino(long termino) {
        this.termino = termino;
    }

    public int getAlunoId() {
        return alunoId;
    }

    public void setAlunoId(int alunoId) {
        this.alunoId = alunoId;
    }

    public boolean isEmRevisaoProfessor() {
        return emRevisaoProfessor;
    }

    public void setEmRevisaoProfessor(boolean emRevisaoProfessor) {
        this.emRevisaoProfessor = emRevisaoProfessor;
    }

    public boolean isGeradoPorIA() {
        return geradoPorIA;
    }

    public void setGeradoPorIA(boolean geradoPorIA) {
        this.geradoPorIA = geradoPorIA;
    }

    public List<FichaDeTreinoGeradaPorIADTO> getFichas() {
        return fichas;
    }

    public void setFichas(List<FichaDeTreinoGeradaPorIADTO> fichas) {
        this.fichas = fichas;
    }

    public Integer getDiasPorSemana() {
        return diasPorSemana;
    }

    public void setDiasPorSemana(Integer diasPorSemana) {
        this.diasPorSemana = diasPorSemana;
    }
}