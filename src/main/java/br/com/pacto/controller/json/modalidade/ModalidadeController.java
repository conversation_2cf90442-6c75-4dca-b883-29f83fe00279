package br.com.pacto.controller.json.modalidade;


import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.modalidade.ModalidadeService;
import br.com.pacto.swagger.respostas.ExemploRespostaVazia;
import br.com.pacto.swagger.respostas.modalidade.ExemploRespostaListCoresResponseTO;
import br.com.pacto.swagger.respostas.modalidade.ExemploRespostaListModalidadeResponseTO;
import br.com.pacto.swagger.respostas.modalidade.ExemploRespostaListModalidadeResponseTOPaginacao;
import br.com.pacto.swagger.respostas.modalidade.ExemploRespostaModalidadeResponseTO;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by Joao Moita on 26/09/2018.
 */

@Controller
@RequestMapping("/psec/modalidades")
public class ModalidadeController {


    private ModalidadeService modalidadeService;

    @Autowired
    public ModalidadeController(ModalidadeService modalidadeService) {
        Assert.notNull(modalidadeService, "O serviço de modalidade não foi injetado corretamente");
        this.modalidadeService = modalidadeService;
    }


    @ApiOperation(
            value = "Consultar cores disponíveis para modalidades",
            notes = "Obtém todas as cores disponíveis na paleta de cores do sistema para uso nas modalidades. " +
                    "Retorna uma lista com todas as cores configuradas, exceto as cores vermelhas que são filtradas.",
            tags = "Modalidade"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListCoresResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.MODALIDADES)
    @RequestMapping(value = "cores", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaCores() {
        try {
            return ResponseEntityFactory.ok(modalidadeService.obterTodasCores());
        } catch (ServiceException e) {
            Logger.getLogger(ModalidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter as cores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Cadastrar modalidade",
            notes = "Cadastra uma nova modalidade no sistema com nome e cor específicos. " +
                    "A modalidade deve ter um nome único e uma cor válida da paleta disponível.",
            tags = "Modalidade"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaModalidadeResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.MODALIDADES)
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastroModalidade(@RequestBody ModalidadeTO modalidadeTO) {
        try {
            return ResponseEntityFactory.ok(modalidadeService.cadastroModalidade(modalidadeTO));
        } catch (ServiceException e) {
            Logger.getLogger(ModalidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar a modalidade", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Consultar detalhes da modalidade",
            notes = "Obtém os detalhes completos de uma modalidade específica através do seu identificador único. " +
                    "Retorna todas as informações da modalidade incluindo nome, cor e identificador.",
            tags = "Modalidade"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaModalidadeResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.MODALIDADES)
    @RequestMapping(value = "{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalhesModalidade(
            @ApiParam(value = "Identificador único da modalidade", defaultValue = "1", required = true)
            @PathVariable("id") Integer id) {
        try {
            return ResponseEntityFactory.ok(modalidadeService.detalhesModalidade(id));
        } catch (ServiceException e) {
            Logger.getLogger(ModalidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter os detalhes da modalidade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar modalidades com paginação",
            notes = "Lista todas as modalidades cadastradas no sistema com suporte a filtros e paginação. " +
                    "Permite busca por nome da modalidade e ordenação dos resultados.",
            tags = "Modalidade"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListModalidadeResponseTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.MODALIDADES)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarModalidades(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome da modalidade.\n" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\"]).",
                    defaultValue = "{\"quicksearchValue\":\"Cardio\", \"quicksearchFields\":[\"nome\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiIgnore PaginadorDTO paginadorDTO,
            @ApiParam(value = "Identificador da empresa", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId", required = true) Integer empresaId) throws JSONException {
        try {
            FiltroModalidadeJSON filtroModalidadeJSON = new FiltroModalidadeJSON(filtros);
            return ResponseEntityFactory.ok(modalidadeService.listaModalidades(filtroModalidadeJSON, paginadorDTO, empresaId), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ModalidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar modalidades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Alterar modalidade",
            notes = "Altera os dados de uma modalidade existente através do seu identificador único. " +
                    "Permite modificar o nome e a cor da modalidade. Em sistemas integrados com ZW, apenas a cor pode ser alterada.",
            tags = "Modalidade"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaModalidadeResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.MODALIDADES)
    @RequestMapping(value = "{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarModalidade(
            @ApiParam(value = "Identificador único da modalidade a ser alterada", defaultValue = "1", required = true)
            @PathVariable("id") Integer id,
            @RequestBody ModalidadeTO modalidadeTO) {
        try {
            return ResponseEntityFactory.ok(modalidadeService.alterarModalidade(id, modalidadeTO));
        } catch (ServiceException e) {
            Logger.getLogger(ModalidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar a modalidade", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Remover modalidade",
            notes = "Remove uma modalidade do sistema através do seu identificador único. " +
                    "A remoção é permanente e só é permitida em sistemas independentes (não integrados com ZW).",
            tags = "Modalidade"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.MODALIDADES)
    @RequestMapping(value = "{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerModalidade(
            @ApiParam(value = "Identificador único da modalidade a ser removida", defaultValue = "1", required = true)
            @PathVariable("id") Integer id) {
        try {
            modalidadeService.removerModalidade(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(ModalidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover a modalidade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Obter todas as modalidades",
            notes = "Obtém todas as modalidades ativas disponíveis para uma empresa específica. " +
                    "Pode filtrar modalidades específicas para turmas quando o parâmetro turma for informado.",
            tags = "Modalidade"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListModalidadeResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.MODALIDADES)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterTodasModalidades(
            @ApiParam(value = "Identificador da empresa", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiParam(value = "Filtrar modalidades específicas para turmas", defaultValue = "true")
            @RequestParam(required = false) Boolean turma) {
        try {
            return ResponseEntityFactory.ok(modalidadeService.obterTodasModalidades(empresaId, turma));
        } catch (ServiceException e) {
            Logger.getLogger(ModalidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar todas as modalidades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
