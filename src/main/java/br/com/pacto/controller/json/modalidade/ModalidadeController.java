package br.com.pacto.controller.json.modalidade;


import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.modalidade.ModalidadeService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by <PERSON><PERSON> on 26/09/2018.
 */

@Controller
@RequestMapping("/psec/modalidades")
public class ModalidadeController {


    private ModalidadeService modalidadeService;

    @Autowired
    public ModalidadeController(ModalidadeService modalidadeService){
        Assert.notNull(modalidadeService, "O serviço de modalidade não foi injetado corretamente");
        this.modalidadeService = modalidadeService;
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.MODALIDADES)
    @RequestMapping(value = "cores", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaCores(){
        try {
            return ResponseEntityFactory.ok(modalidadeService.obterTodasCores());
        } catch (ServiceException e) {
            Logger.getLogger(ModalidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter as cores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.MODALIDADES)
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastroModalidade(@RequestBody ModalidadeTO modalidadeTO){
        try {
            return ResponseEntityFactory.ok(modalidadeService.cadastroModalidade(modalidadeTO));
        } catch (ServiceException e) {
            Logger.getLogger(ModalidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar a modalidade", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.MODALIDADES)
    @RequestMapping(value = "{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalhesModalidade(@PathVariable("id") Integer id){
        try {
            return ResponseEntityFactory.ok(modalidadeService.detalhesModalidade(id));
        } catch (ServiceException e) {
            Logger.getLogger(ModalidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter os detalhes da modalidade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.MODALIDADES)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarModalidades(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                 PaginadorDTO paginadorDTO,
                                                                 @RequestHeader(value = "empresaId", required = true)Integer empresaId) throws JSONException {
        try {
            FiltroModalidadeJSON filtroModalidadeJSON = new FiltroModalidadeJSON(filtros);
            return ResponseEntityFactory.ok(modalidadeService.listaModalidades(filtroModalidadeJSON, paginadorDTO, empresaId), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ModalidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar modalidades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.MODALIDADES)
    @RequestMapping(value = "{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarModalidade(@PathVariable("id") Integer id, @RequestBody ModalidadeTO modalidadeTO){
        try {
            return ResponseEntityFactory.ok(modalidadeService.alterarModalidade(id, modalidadeTO));
        } catch (ServiceException e) {
            Logger.getLogger(ModalidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar a modalidade", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.MODALIDADES)
    @RequestMapping(value = "{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerModalidade(@PathVariable("id") Integer id){
        try {
            modalidadeService.removerModalidade(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(ModalidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover a modalidade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.MODALIDADES)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterTodasModalidades(@RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                     @RequestParam(required = false) Boolean turma) {
        try {
            return ResponseEntityFactory.ok(modalidadeService.obterTodasModalidades(empresaId, turma));
        } catch (ServiceException e) {
            Logger.getLogger(ModalidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar todas as modalidades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
