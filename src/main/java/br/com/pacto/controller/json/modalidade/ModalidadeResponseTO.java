package br.com.pacto.controller.json.modalidade;

import br.com.pacto.bean.aula.Modalidade;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModalidadeResponseTO {

    private Integer id;
    private String nome;
    private CoresResponseTO cor;

    public ModalidadeResponseTO(Modalidade modalidade, Boolean treinoIndependente) {
        this.id = treinoIndependente ? modalidade.getCodigo() : modalidade.getCodigoZW();
        this.nome = modalidade.getNome();
        this.cor = new CoresResponseTO(modalidade.getCor());
    }

    public ModalidadeResponseTO(Integer id, String nome, CoresResponseTO cor) {
        this.id = id;
        this.nome = nome;
        this.cor = cor;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public CoresResponseTO getCor() {
        return cor;
    }

    public void setCor(CoresResponseTO cor) {
        this.cor = cor;
    }
}
