package br.com.pacto.controller.json.modalidade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by paulo 08/08/2019
 */
@ApiModel(description = "Informações simplificadas das modalidades")
public class ModalidadeSimplesDTO {

    @ApiModelProperty(value = "Código único identificador da modalidade", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome da modalidade", example = "Spinning")
    private String nome;
    @ApiModelProperty(value = "Cor da modalidade", example = "blue")
    private String cor;
    @ApiModelProperty(value = "Uri da imagem da modalidade", example = "www.pactosolucoes.com.br/imagens/spinning.png")
    private String imageUri;

    public ModalidadeSimplesDTO(Integer id, String nome, String cor) {
        this.id = id;
        this.nome = nome;
        this.cor = cor;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getImageUri() {
        return imageUri == null ? "" : imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }
}
