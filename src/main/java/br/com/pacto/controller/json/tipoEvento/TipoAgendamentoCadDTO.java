package br.com.pacto.controller.json.tipoEvento;

import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.enumerador.agenda.TipoDuracaoEvento;
import br.com.pacto.util.enumeradores.PaletaCoresEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;

@ApiModel(description = "DTO para cadastro e atualização de tipos de agendamento, contendo todas as configurações necessárias para criação de um novo tipo de evento.")
public class TipoAgendamentoCadDTO {

    @ApiModelProperty(value = "Identificador único do tipo de agendamento", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do tipo de agendamento", example = "Avaliação Física", required = true)
    private String nome;

    @ApiModelProperty(value = "Duração do intervalo inferior em formato HHMM", example = "0030")
    private String duracao_intervalo_inferior;

    @ApiModelProperty(value = "Duração do intervalo superior em formato HHMM", example = "0060")
    private String duracao_intervalo_superior;

    @ApiModelProperty(value = "Duração fixa do evento em formato HHMM", example = "0045")
    private String duracao_fixa;

    @ApiModelProperty(
            value = "Comportamento do agendamento. \n\n" +
                    "<strong>Valores disponíveis:</strong>\n" +
                    "- CONTATO_INTERPESSOAL (0) - Contato interpessoal\n" +
                    "- PRESCRICAO_TREINO (1) - Prescrição de treino\n" +
                    "- REVISAO_TREINO (2) - Revisão de treino\n" +
                    "- RENOVAR_TREINO (3) - Renovar treino\n" +
                    "- AVALIACAO_FISICA (4) - Avaliação física",
            example = "PRESCRICAO_TREINO",
            required = true
    )
    @Enumerated(EnumType.ORDINAL)
    private TipoAgendamentoEnum comportamento;

    @ApiModelProperty(value = "Indica se o tipo de agendamento está ativo", example = "true")
    private Boolean ativo;

    @ApiModelProperty(
            value = "Tipo de duração do evento. \n\n" +
                    "<strong>Valores disponíveis:</strong>\n" +
                    "- DURACAO_LIVRE - Duração livre\n" +
                    "- DURACAO_PREDEFINIDA - Duração predefinida\n" +
                    "- INTERVALO_DE_TEMPO - Intervalo de tempo",
            example = "DURACAO_PREDEFINIDA"
    )
    @Enumerated(EnumType.ORDINAL)
    private TipoDuracaoEvento tipo_duracao;

    @ApiModelProperty(value = "Cor em formato hexadecimal para identificação visual", example = "#FF5733")
    private String cor;

    @ApiModelProperty(value = "Número máximo de agendamentos permitidos", example = "5")
    private Integer numero_agendamentos;

    @ApiModelProperty(value = "Quantidade de dias para o agendamento", example = "7")
    private Integer dias;

    @ApiModelProperty(value = "Intervalo mínimo em minutos caso haja falta", example = "15")
    private Integer intervalo_minimo_caso_falta;

    @ApiModelProperty(value = "Indica se é restrito apenas à carteira do professor", example = "true")
    private Boolean somente_carteira_professor;

    @ApiModelProperty(value = "Indica se permite agendamento via aplicativo", example = "true")
    private Boolean permitir_app;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDuracao_intervalo_inferior() {
        return duracao_intervalo_inferior;
    }

    public void setDuracao_intervalo_inferior(String duracao_intervalo_inferior) {
        this.duracao_intervalo_inferior = duracao_intervalo_inferior;
    }

    public String getDuracao_intervalo_superior() {
        return duracao_intervalo_superior;
    }

    public void setDuracao_intervalo_superior(String duracao_intervalo_superior) {
        this.duracao_intervalo_superior = duracao_intervalo_superior;
    }

    public TipoAgendamentoEnum getComportamento() {
        return comportamento;
    }

    public void setComportamento(TipoAgendamentoEnum comportamento) {
        this.comportamento = comportamento;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public TipoDuracaoEvento getTipo_duracao() {
        return tipo_duracao;
    }

    public void setTipo_duracao(TipoDuracaoEvento tipo_duracao) {
        this.tipo_duracao = tipo_duracao;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public Integer getNumero_agendamentos() {
        return numero_agendamentos;
    }

    public void setNumero_agendamentos(Integer numero_agendamentos) {
        this.numero_agendamentos = numero_agendamentos;
    }

    public Integer getDias() {
        return dias;
    }

    public void setDias(Integer dias) {
        this.dias = dias;
    }

    public Integer getIntervalo_minimo_caso_falta() {
        return intervalo_minimo_caso_falta;
    }

    public void setIntervalo_minimo_caso_falta(Integer intervalo_minimo_caso_falta) {
        this.intervalo_minimo_caso_falta = intervalo_minimo_caso_falta;
    }

    public Boolean getSomente_carteira_professor() {
        return somente_carteira_professor;
    }

    public void setSomente_carteira_professor(Boolean somente_carteira_professor) {
        this.somente_carteira_professor = somente_carteira_professor;
    }

    public String getDuracao_fixa() {
        return duracao_fixa;
    }

    public void setDuracao_fixa(String duracao_fixa) {
        this.duracao_fixa = duracao_fixa;
    }

    public Boolean getPermitir_app() {
        return permitir_app;
    }

    public void setPermitir_app(Boolean permitir_app) {
        this.permitir_app = permitir_app;
    }
}
