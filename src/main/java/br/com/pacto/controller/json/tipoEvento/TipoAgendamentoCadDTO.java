package br.com.pacto.controller.json.tipoEvento;

import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.enumerador.agenda.TipoDuracaoEvento;
import br.com.pacto.util.enumeradores.PaletaCoresEnum;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;

public class TipoAgendamentoCadDTO {

    private Integer id;
    private String nome;
    private String duracao_intervalo_inferior;
    private String duracao_intervalo_superior;
    private String duracao_fixa;
    @Enumerated(EnumType.ORDINAL)
    private TipoAgendamentoEnum comportamento;
    private Boolean ativo;
    @Enumerated(EnumType.ORDINAL)
    private TipoDuracaoEvento tipo_duracao;
    private String cor;
    private Integer numero_agendamentos;
    private Integer dias;
    private Integer intervalo_minimo_caso_falta;
    private Boolean somente_carteira_professor;
    private Boolean permitir_app;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDuracao_intervalo_inferior() {
        return duracao_intervalo_inferior;
    }

    public void setDuracao_intervalo_inferior(String duracao_intervalo_inferior) {
        this.duracao_intervalo_inferior = duracao_intervalo_inferior;
    }

    public String getDuracao_intervalo_superior() {
        return duracao_intervalo_superior;
    }

    public void setDuracao_intervalo_superior(String duracao_intervalo_superior) {
        this.duracao_intervalo_superior = duracao_intervalo_superior;
    }

    public TipoAgendamentoEnum getComportamento() {
        return comportamento;
    }

    public void setComportamento(TipoAgendamentoEnum comportamento) {
        this.comportamento = comportamento;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public TipoDuracaoEvento getTipo_duracao() {
        return tipo_duracao;
    }

    public void setTipo_duracao(TipoDuracaoEvento tipo_duracao) {
        this.tipo_duracao = tipo_duracao;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public Integer getNumero_agendamentos() {
        return numero_agendamentos;
    }

    public void setNumero_agendamentos(Integer numero_agendamentos) {
        this.numero_agendamentos = numero_agendamentos;
    }

    public Integer getDias() {
        return dias;
    }

    public void setDias(Integer dias) {
        this.dias = dias;
    }

    public Integer getIntervalo_minimo_caso_falta() {
        return intervalo_minimo_caso_falta;
    }

    public void setIntervalo_minimo_caso_falta(Integer intervalo_minimo_caso_falta) {
        this.intervalo_minimo_caso_falta = intervalo_minimo_caso_falta;
    }

    public Boolean getSomente_carteira_professor() {
        return somente_carteira_professor;
    }

    public void setSomente_carteira_professor(Boolean somente_carteira_professor) {
        this.somente_carteira_professor = somente_carteira_professor;
    }

    public String getDuracao_fixa() {
        return duracao_fixa;
    }

    public void setDuracao_fixa(String duracao_fixa) {
        this.duracao_fixa = duracao_fixa;
    }

    public Boolean getPermitir_app() {
        return permitir_app;
    }

    public void setPermitir_app(Boolean permitir_app) {
        this.permitir_app = permitir_app;
    }
}
