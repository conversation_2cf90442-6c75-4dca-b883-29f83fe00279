package br.com.pacto.controller.json.ranking;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.ambiente.AmbienteController;
import br.com.pacto.controller.json.ambiente.AmbienteTO;
import br.com.pacto.controller.json.crossfit.RankingJSON;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.wod.WodService;
import br.com.pacto.util.UteisValidacao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by Joao Moita on 28/09/2018.
 */
@Controller
@RequestMapping("/psec/ranking")
public class RankingController {

    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private WodService ws;

    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_WOD)
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> rankingAlunos(
            @RequestParam("wodId") final Integer wodId,
            HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<RankingJSON> ranking = ws.ranking(ctx, wodId, request);

            return ResponseEntityFactory.ok(ranking);
        } catch (Exception e) {
            Logger.getLogger(RankingController.class.getName()).log(Level.SEVERE, "Erro ao tentar ranking alunos", e);
            return ResponseEntityFactory.erroInterno("erro_ranking_aluno", e.getMessage());
        }
    }
}
