package br.com.pacto.controller.json.empresa;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.avaliacao.AvaliacaoFisicaController;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 29/08/2018.
 */
@Controller
@RequestMapping("/psec/empresas")
public class EmpresaController {

    private final EmpresaService empresaService;
    private final UsuarioService usuarioService;

    @Autowired
    private SessaoService sessaoService;

    @Autowired
    public EmpresaController(EmpresaService empresaService, UsuarioService usuarioService){
        Assert.notNull(empresaService, "O serviço de empresa não foi injetado corretamente");
        Assert.notNull(usuarioService, "O serviço de usuário não foi injetado corretamente");
        this.empresaService = empresaService;
        this.usuarioService = usuarioService;
    }

    @ResponseBody
//    @Permissao(recursos = RecursoEnum.EMPRESA)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarEmpresas() {
        try {
            return ResponseEntityFactory.ok(empresaService.listarEmpresas());
        } catch (ServiceException e) {
            Logger.getLogger(EmpresaController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as empresas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
//    @Permissao(recursos = RecursoEnum.EMPRESA)
    @RequestMapping(value = "/linkzw", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> linkzw(@RequestParam("usuarioOamd") String usuarioOamd,
                                                      @RequestParam("urlLogin") String urlLogin,
                                                      @RequestHeader("empresaId") Integer empresaId,
                                                      @RequestParam(value = "tokenOamd", required = false) String tokenOamd,
                                                      @RequestParam(value = "sessionId", required = false) String sessionId
    ) {
        try {
            return ResponseEntityFactory.ok(usuarioService.urlZW(usuarioOamd, urlLogin, empresaId, sessionId, tokenOamd));
        } catch (ServiceException e) {
            Logger.getLogger(EmpresaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter url para o zw", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/ativar", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> ativarEmpresa(@RequestParam(value = "chave") String chave,
                                                             @RequestBody CreateClientDTO colaborador,
                                                             HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(empresaService.ativarEmpresa(chave, colaborador, request));
        } catch (ServiceException e) {
            e.printStackTrace();
            Logger.getLogger(EmpresaController.class.getName()).log(Level.SEVERE, "Erro ao tentar ativar empresa", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
//    @Permissao(recursos = RecursoEnum.EMPRESA)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarEmpresa(@PathVariable() Integer id) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(empresaService.obterPorId(ctx, id));
        } catch (ServiceException e) {
            Logger.getLogger(EmpresaController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as empresas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/empdto/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarEmpresaDTO(@PathVariable() Integer id) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(empresaService.obterPorIdEmpDTO(ctx, id));
        } catch (ServiceException e) {
            Logger.getLogger(EmpresaController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as empresas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/alterarEmpresa", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarEmpresa(@RequestBody EmpresaDTO empresa) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(empresaService.alterarEmpDTO(ctx, empresa));
        } catch (ServiceException e) {
            e.printStackTrace();
            Logger.getLogger(EmpresaController.class.getName()).log(Level.SEVERE, "Erro ao tentar ativar empresa", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/ativas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarUnidadesAtivas() {
        try {
            return ResponseEntityFactory.ok(empresaService.obterUnidadesAtivas());
        } catch (ServiceException e) {
            Logger.getLogger(EmpresaController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as unidades ativas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(EmpresaController.class.getName()).log(Level.SEVERE, "Erro inesperado ao listar unidades ativas", e);
            return ResponseEntityFactory.erroInterno("ERRO_LISTAR_UNIDADES_ATIVAS", e.getMessage());
        }
    }



}
