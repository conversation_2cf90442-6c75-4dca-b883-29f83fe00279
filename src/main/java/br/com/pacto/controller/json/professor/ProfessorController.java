package br.com.pacto.controller.json.professor;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.bi.AgrupamentoIndicadorDashboardEnum;
import br.com.pacto.bean.bi.ConfiguracaoRankingProfessores;
import br.com.pacto.bean.bi.IndicadorDashboardEnum;
import br.com.pacto.bean.gestao.CategoriaIndicadorEnum;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;

import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.aula.GestaoSalaCheiaService;
import br.com.pacto.service.intf.gestao.DashboardBIService;
import br.com.pacto.service.intf.gestao.RankingProfessoresService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.impl.Ordenacao;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by ulisses on 29/08/2018.
 */
@Controller
@RequestMapping("/psec/professores")
public class ProfessorController {

    private final ProfessorSinteticoService professorSinteticoService;
    @Autowired
    private RankingProfessoresService rankingProfessoresService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private GestaoSalaCheiaService gestaoSalaCheiaService;

    @Autowired
    public ProfessorController(ProfessorSinteticoService professorSinteticoService){
        Assert.notNull(professorSinteticoService, "O serviço de professor sintético não foi injetado corretamente");
        this.professorSinteticoService = professorSinteticoService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/indicadores-carteira-professores", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> indicadoresCarteiraProfessor(
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @RequestParam(value = "configs", required = false) JSONObject configs,
            @RequestParam(value = "sort", required = false) String sort,
            @RequestHeader("empresaId") Integer empresaId,
            PaginadorDTO paginadorDTO,
            HttpServletRequest request
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filters, configs);
            filtroGestaoJSON.setCategoria(CategoriaIndicadorEnum.CARTEIRAS_PROF);
            return ResponseEntityFactory.ok(professorSinteticoService.carregarIndicadores(request, paginadorDTO, empresaId, filtroGestaoJSON, sort), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar indicadores da carteira de professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/indicadores-carteira-professores/alunos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosPorIndicadoresCarteiraProfessor(
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @RequestParam(value = "configs", required = false) JSONObject configs,
            @RequestParam(value = "sort", required = false) String sort,
            @RequestHeader("empresaId") Integer empresaId,
            PaginadorDTO paginadorDTO,
            HttpServletRequest request
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filtros, configs);
            filtroGestaoJSON.setCategoria(CategoriaIndicadorEnum.CARTEIRAS_PROF);
            return ResponseEntityFactory.ok(professorSinteticoService.carregarAlunosPorIndicador(request, paginadorDTO, empresaId, filtroGestaoJSON, sort), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar alunos por indicador da carteira de professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/indicadores-atividade", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> indicadoresAtividadeProfessor(
        @RequestParam(value = "filters", required = false) JSONObject filters,
        @RequestParam(value = "configs", required = false) JSONObject configs,
        @RequestParam(value = "sort", required = false) String sort,
        @RequestHeader("empresaId") Integer empresaId,
        PaginadorDTO paginadorDTO,
        HttpServletRequest request
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filters, configs);
            filtroGestaoJSON.setCategoria(CategoriaIndicadorEnum.ATIVIDADES_PROF);
            return ResponseEntityFactory.ok(professorSinteticoService.carregarIndicadores(request, paginadorDTO, empresaId, filtroGestaoJSON, sort));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar indicadores da atividade de professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/indicadores-atividades-acumuladas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> indicadoresAtividadesAcumuladasProfessor(
            @RequestHeader("empresaId") Integer empresaId,
            HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(professorSinteticoService.carregarIndicadoresAcumulados(request, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar indicadores da atividade de professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/alunos-aviso-medico", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosAvisoMedicoGeral(
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @RequestParam(value = "configs", required = false) JSONObject configs,
            @RequestParam(value = "sort", required = false) String sort,
            @RequestHeader("empresaId") Integer empresaId,
            PaginadorDTO paginadorDTO,
            HttpServletRequest request
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filters, configs);
            return ResponseEntityFactory.ok(professorSinteticoService.alunosAvisoMedico(request, paginadorDTO, empresaId, filtroGestaoJSON, sort, true));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar indicadores da atividade de professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/alunos-aviso-medico/alunos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosAvisoMedicoAlunos(
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @RequestParam(value = "configs", required = false) JSONObject configs,
            @RequestParam(value = "sort", required = false) String sort,
            @RequestHeader("empresaId") Integer empresaId,
            PaginadorDTO paginadorDTO,
            HttpServletRequest request
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filters, configs);
            return ResponseEntityFactory.ok(professorSinteticoService.alunosAvisoMedico(request, paginadorDTO, empresaId, filtroGestaoJSON, sort, false));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar indicadores da atividade de professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/indicadores-atividade/alunos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosPorIndicadoresAtividadeProfessor(
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @RequestParam(value = "configs", required = false) JSONObject configs,
            @RequestParam(value = "sort", required = false) String sort,
            @RequestHeader("empresaId") Integer empresaId,
            PaginadorDTO paginadorDTO,
            HttpServletRequest request
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filters, configs);
            filtroGestaoJSON.setCategoria(CategoriaIndicadorEnum.ATIVIDADES_PROF);
            return ResponseEntityFactory.ok(professorSinteticoService.carregarAlunosPorIndicador(request, paginadorDTO, empresaId, filtroGestaoJSON, sort));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar alunos por indicador da atividade de professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/configuracao", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirConfiguracaoRankingProfessores(
            @RequestHeader("empresaId") Integer empresaId,
            @RequestBody ConfiguracaoRankingDTO configuracaoRankingDTO
    ) {
        try {
            return ResponseEntityFactory.ok(professorSinteticoService.incluirConfiguracao(empresaId, configuracaoRankingDTO));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar configuração no ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/configuracao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarConfiguracoesRankingProfessores(
            @RequestHeader("empresaId") Integer empresaId,
            @RequestParam(value = "filters", required = false) JSONObject filters,
            PaginadorDTO paginadorDTO
    ) {
        try {
            return ResponseEntityFactory.ok(professorSinteticoService.listarConfiguracoesRanking(empresaId, paginadorDTO, filters), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as configurações do ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/configuracao", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> gravarConfigs(
            @RequestHeader("empresaId") Integer empresaId,
            @RequestBody Map<String, String> valores
    ) {
        try {
            return ResponseEntityFactory.ok(professorSinteticoService.gravarConfiguracoesRanking(empresaId, valores));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as configurações do ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/configuracao/toggle/{indicador}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> gravarConfigs(
            @RequestHeader("empresaId") Integer empresaId,
            @PathVariable("indicador") String indicador
    ) {
        try {
            return ResponseEntityFactory.ok(professorSinteticoService.toggleConfiguracoesRanking(empresaId, IndicadorDashboardEnum.valueOf(indicador)));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as configurações do ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/configuracao/{agrup}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarConfiguracoesRankingProfessoresAgrupamento(
            @RequestHeader("empresaId") Integer empresaId,
            @PathVariable("agrup") String agrup,
            @RequestParam(value = "filters", required = false) JSONObject filters,
            PaginadorDTO paginadorDTO
    ) {
        try {
            Map<AgrupamentoIndicadorDashboardEnum, List<ConfiguracaoRankingDTO>> map = professorSinteticoService.listarConfiguracoesRanking(empresaId, null, null);
            List<ConfiguracaoRankingDTO> dtos = map.get(AgrupamentoIndicadorDashboardEnum.valueOf(agrup));
            if(filters != null && !UteisValidacao.emptyString(filters.optString("quicksearchValue"))){
                String quickSearchValue = filters.optString("quicksearchValue");
                dtos = dtos.stream().filter(d -> d.getIndicadorSort().toLowerCase().contains(quickSearchValue.toLowerCase()))
                        .collect(Collectors.toList());
            }
            if(paginadorDTO != null){
                paginadorDTO.setQuantidadeTotalElementos(new Long(dtos.size()));
                if(paginadorDTO.getSortMap() == null){
                    dtos = Ordenacao.ordenarLista(dtos, "peso");
                    Collections.reverse(dtos);
                } else if(paginadorDTO.getSortMap().isEmpty() || paginadorDTO.getSortMap().get("pontuacao") != null){
                    dtos = Ordenacao.ordenarLista(dtos, "peso");
                } else if(paginadorDTO.getSortMap().get("indicador") != null){
                    dtos = Ordenacao.ordenarLista(dtos, "indicadorSort");
                } else {
                    dtos = Ordenacao.ordenarLista(dtos, "peso");
                }
                if(paginadorDTO.getSortMap() != null && !paginadorDTO.getSortMap().isEmpty() && paginadorDTO.getSortMap().values().contains("DESC")){
                    Collections.reverse(dtos);
                }
                if(paginadorDTO.getSize().intValue() < dtos.size()){
                    int init = paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
                    int end = init + paginadorDTO.getSize().intValue();
                    end = end > dtos.size() ? dtos.size() : end;
                    dtos = dtos.subList(init, end);
                }
            }
            return ResponseEntityFactory.ok(dtos, paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as configurações do ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/configuracao/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerConfiguracaoRankingProfessores(
            @PathVariable("id") Integer configuracaoId
    ) {
        try {
            professorSinteticoService.removerConfiguracaoRanking(configuracaoId);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover a configuração do ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/atualizar-ranking", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarRankingProfessores(
            @RequestHeader("empresaId") Integer empresaId,
            @RequestParam("inicio") Long inicio,
            @RequestParam("fim") Long fim
    ) throws ServiceException {
        try {
            Date inicioDate = new Date(inicio);
            Date fimDate = new Date(fim);
            Date inicioComHora = Calendario.getDataComHora(inicioDate, "00:00");
            rankingProfessoresService.gerarRankingNoBanco(sessaoService.getUsuarioAtual().getChave(),
                    inicioComHora,
                    fimDate,
                    SuperControle.independente(sessaoService.getUsuarioAtual().getChave()), sessaoService.getUsuarioAtual().getId(),
                    empresaId);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar o ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }


    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> carregarRankingProfessores(
        @RequestHeader("empresaId") Integer empresaId,
        @RequestParam(value = "filters", required = false) JSONObject filtros,
        @RequestParam(value = "configs", required = false) JSONObject configs,
        @RequestParam(value = "inicio") Long inicio,
        @RequestParam(value = "fim") Long fim,
        @RequestParam(value = "sort", required = false) String sort,
        PaginadorDTO paginadorDTO,
        HttpServletRequest request
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filtros, configs);
            Date dateInicio = filtroGestaoJSON.getInicio() == null ? new Date(inicio) : filtroGestaoJSON.getInicio();
            Date dateFim = filtroGestaoJSON.getFim() == null ? new Date(fim) : filtroGestaoJSON.getFim();
            return ResponseEntityFactory.ok(rankingProfessoresService.carregarRankingProfessores(request,paginadorDTO,
                    dateInicio,
                    dateFim, empresaId, filtroGestaoJSON, sort, SuperControle.independente(sessaoService.getUsuarioAtual().getChave())), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar o ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/detalhes/{idColaborador}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> carregarRankingProfessores(
        @RequestHeader("empresaId") Integer empresaId,
        @RequestParam(value = "configs", required = false) JSONObject configs,
        @PathVariable("idColaborador") Integer idColaborador,
        @RequestParam(value = "filters", required = false) JSONObject filtros,
        @RequestParam(value = "inicio") Long inicio,
        @RequestParam(value = "fim") Long fim,
        HttpServletRequest request
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filtros);
            Date dateInicio = new Date(inicio);
            Date dateFim = new Date(fim);
            return ResponseEntityFactory.ok(rankingProfessoresService.detalhesProfessor(
                    request,
                    dateInicio,
                    dateFim, empresaId,
                    idColaborador,
                    filtroGestaoJSON, SuperControle.independente(sessaoService.getUsuarioAtual().getChave())));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar o ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/detalhes/compartilhar/{idColaborador}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> compartilharRankingProfessores(
            @RequestHeader("empresaId") Integer empresaId,
            @RequestParam(value = "configs", required = false) JSONObject configs,
            @PathVariable("idColaborador") Integer idColaborador,
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @RequestParam(value = "inicio") Long inicio,
            @RequestParam(value = "fim") Long fim,
            HttpServletRequest request
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filtros);
            Date dateInicio = new Date(inicio);
            Date dateFim = new Date(fim);
            return ResponseEntityFactory.ok(rankingProfessoresService.detalhesCompartilharProfessor(
                    request,
                    dateInicio,
                    dateFim, empresaId,
                    idColaborador,
                    filtroGestaoJSON, SuperControle.independente(sessaoService.getUsuarioAtual().getChave())));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar o ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/indicadores-add", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> indicadoresAdd(
        @RequestHeader("empresaId") Integer empresaId,
        HttpServletRequest request
    ) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<ConfiguracaoRankingProfessores> listConfig = (UtilContext.getBean(DashboardBIService.class)).obterCfgRanking(ctx, null, empresaId, null, true);
            List<IndicadorDashboardEnum> indicadoresAdicionados = new ArrayList(){{
               for(ConfiguracaoRankingProfessores cfg : listConfig){
                   add(cfg.getIndicador());
               }
            }};
            return ResponseEntityFactory.ok(Ordenacao.ordenarLista(indicadoresAdicionados, "labelSort"));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar o ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/podium", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> podium(
        @RequestHeader("empresaId") Integer empresaId,
        @RequestParam(value = "filters", required = false) String filtros,
        @RequestParam(value = "inicio") Long inicio,
        @RequestParam(value = "fim") Long fim,
        HttpServletRequest request
    ) {
        try {
            JSONObject filtro = new JSONObject();
            if(!UteisValidacao.emptyString(filtros)) {
                filtro = new JSONObject(filtros);
            }
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filtro);
            Date dateInicio = new Date(inicio);
            Date dateFim = new Date(fim);
            return ResponseEntityFactory.ok(rankingProfessoresService.podium(
                    request,
                    dateInicio,
                    dateFim, empresaId,
                    filtroGestaoJSON, SuperControle.independente(sessaoService.getUsuarioAtual().getChave())));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar o ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/config-relatorio/{configId}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirAlterarFiltroConfigRel(@RequestParam(value = "config", required = false) String config,
                                                                             @PathVariable("configId") String configId) throws ServiceException {
        try{
            return ResponseEntityFactory.ok(professorSinteticoService.incluirAtualizarConfigRelatorio(configId, config));
        }catch (ServiceException e ){
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao salvar config relátorio");
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/config-relatorio/{configId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterConfigRel(@PathVariable("configId") String configId) throws Exception {
        try{
            return ResponseEntityFactory.ok(professorSinteticoService.obterConfigRelatorio(configId));
        }catch (ServiceException e){
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao obter config relátorio");
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/substituidos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> professoresSubstituidos(
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            PaginadorDTO paginadorDTO) {
        try{
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filtros);
            return ResponseEntityFactory.ok(gestaoSalaCheiaService.montarBiSubstituidosSpa(filtroGestaoJSON, paginadorDTO), paginadorDTO);
        }catch (ServiceException e){
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao obter relátorio de professores substituidos");
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/conferir", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> conferir(
            @RequestHeader("empresaId") Integer empresaId,
            @RequestParam(value = "filters", required = false) String filtros,
            @RequestParam(value = "inicio") Long inicio,
            @RequestParam(value = "fim") Long fim
    ) {
        try {
            JSONObject filtro = new JSONObject();
            if(!UteisValidacao.emptyString(filtros)) {
                filtro = new JSONObject(filtros);
            }
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filtro);
            Date dateInicio = new Date(inicio);
            Date dateFim = new Date(fim);
            rankingProfessoresService.conferir(
                    dateInicio,
                    dateFim, empresaId,
                    filtroGestaoJSON, SuperControle.independente(sessaoService.getUsuarioAtual().getChave()));
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar o ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
