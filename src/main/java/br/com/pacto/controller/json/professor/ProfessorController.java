package br.com.pacto.controller.json.professor;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.bi.AgrupamentoIndicadorDashboardEnum;
import br.com.pacto.bean.bi.ConfiguracaoRankingProfessores;
import br.com.pacto.bean.bi.IndicadorDashboardEnum;
import br.com.pacto.bean.gestao.CategoriaIndicadorEnum;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;

import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.aula.GestaoSalaCheiaService;
import br.com.pacto.service.intf.gestao.DashboardBIService;
import br.com.pacto.service.intf.gestao.RankingProfessoresService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.swagger.respostas.professor.*;
import br.com.pacto.swagger.respostas.professor.ExemploRespostaCompartilharRankingProfessor;
import br.com.pacto.swagger.respostas.professor.ExemploRespostaIndicadoresAdd;
import br.com.pacto.swagger.respostas.professor.ExemploRespostaPodiumRanking;
import br.com.pacto.swagger.respostas.professor.ExemploRespostaConfigRelatorio;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.impl.Ordenacao;
import io.swagger.annotations.*;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by ulisses on 29/08/2018.
 */
@Controller
@RequestMapping("/psec/professores")
public class ProfessorController {

    private final ProfessorSinteticoService professorSinteticoService;
    @Autowired
    private RankingProfessoresService rankingProfessoresService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private GestaoSalaCheiaService gestaoSalaCheiaService;

    @Autowired
    public ProfessorController(ProfessorSinteticoService professorSinteticoService){
        Assert.notNull(professorSinteticoService, "O serviço de professor sintético não foi injetado corretamente");
        this.professorSinteticoService = professorSinteticoService;
    }

    @ApiOperation(value = "Indicadores da carteira de professores",
                  notes = "Carrega os indicadores relacionados à carteira de professores, incluindo métricas de alunos ativos, inativos e outros indicadores relevantes",
                  tags = "Colaborador")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Indicadores carregados com sucesso", response = ExemploRespostaIndicadoresCarteiraProfessores.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/indicadores-carteira-professores", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> indicadoresCarteiraProfessor(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do professor.\n" +
                    "- <strong>dataInicio:</strong> Data de início do período para análise (timestamp).\n" +
                    "- <strong>dataFim:</strong> Data de fim do período para análise (timestamp).\n" +
                    "- <strong>colaboradorIds:</strong> Lista de IDs dos professores para filtro (ex: [1, 2, 3]).\n" +
                    "- <strong>modalidadeIds:</strong> Lista de IDs das modalidades para filtro (ex: [1, 2]).\n" +
                    "- <strong>status:</strong> Lista de situações dos alunos para filtro (ex: [\"AT\", \"IN\"]).",
                    defaultValue = "{\"quicksearchValue\":\"João\", \"dataInicio\":1640995200000, \"dataFim\":1672531199000, \"colaboradorIds\":[1], \"modalidadeIds\":[1], \"status\":[\"AT\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @ApiParam(value = "Configurações adicionais para o filtro",
                    defaultValue = "{\"incluirProfessorInativo\":false}")
            @RequestParam(value = "configs", required = false) JSONObject configs,
            @RequestParam(value = "sort", required = false) String sort,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filters, configs);
            filtroGestaoJSON.setCategoria(CategoriaIndicadorEnum.CARTEIRAS_PROF);
            return ResponseEntityFactory.ok(professorSinteticoService.carregarIndicadores(request, paginadorDTO, empresaId, filtroGestaoJSON, sort), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar indicadores da carteira de professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Consultar alunos por indicadores da carteira de professores",
                  notes = "Carrega a lista de alunos relacionados aos indicadores da carteira de professores",
                  tags = "Colaborador")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Alunos carregados com sucesso", response = ExemploRespostaAlunosCarteiraProfessores.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/indicadores-carteira-professores/alunos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosPorIndicadoresCarteiraProfessor(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do professor.\n" +
                    "- <strong>dataInicio:</strong> Data de início do período para análise (timestamp).\n" +
                    "- <strong>dataFim:</strong> Data de fim do período para análise (timestamp).\n" +
                    "- <strong>colaboradorIds:</strong> Lista de IDs dos professores para filtro (ex: [1, 2, 3]).\n" +
                    "- <strong>modalidadeIds:</strong> Lista de IDs das modalidades para filtro (ex: [1, 2]).\n" +
                    "- <strong>status:</strong> Lista de situações dos alunos para filtro (ex: [\"AT\", \"IN\"]).",
                    defaultValue = "{\"quicksearchValue\":\"João\", \"dataInicio\":1640995200000, \"dataFim\":1672531199000, \"colaboradorIds\":[1], \"modalidadeIds\":[1], \"status\":[\"AT\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiParam(value = "Configurações adicionais para o filtro",
                    defaultValue = "{\"incluirProfessorInativo\":false}")
            @RequestParam(value = "configs", required = false) JSONObject configs,
            @RequestParam(value = "sort", required = false) String sort,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filtros, configs);
            filtroGestaoJSON.setCategoria(CategoriaIndicadorEnum.CARTEIRAS_PROF);
            return ResponseEntityFactory.ok(professorSinteticoService.carregarAlunosPorIndicador(request, paginadorDTO, empresaId, filtroGestaoJSON, sort), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar alunos por indicador da carteira de professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Indicadores de atividade dos professores",
                  notes = "Carrega os indicadores relacionados às atividades dos professores, incluindo treinos novos, renovados, revisados e acompanhamentos",
                  tags = "Colaborador")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Indicadores de atividade carregados com sucesso", response = ExemploRespostaIndicadoresAtividadeProfessores.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/indicadores-atividade", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> indicadoresAtividadeProfessor(
        @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                "<strong>Filtros disponíveis:</strong>\n" +
                "- <strong>quicksearchValue:</strong> Termo de busca para nome do professor.\n" +
                "- <strong>dataInicio:</strong> Data de início do período para análise (timestamp).\n" +
                "- <strong>dataFim:</strong> Data de fim do período para análise (timestamp).\n" +
                "- <strong>colaboradorIds:</strong> Lista de IDs dos professores para filtro (ex: [1, 2, 3]).\n" +
                "- <strong>modalidadeIds:</strong> Lista de IDs das modalidades para filtro (ex: [1, 2]).\n" +
                "- <strong>situacaoPrograma:</strong> Lista de situações do programa para filtro (ex: [\"NOVOS\", \"RENOVADOS\"]).",
                defaultValue = "{\"quicksearchValue\":\"Maria\", \"dataInicio\":1640995200000, \"dataFim\":1672531199000, \"colaboradorIds\":[1], \"modalidadeIds\":[1], \"situacaoPrograma\":[\"NOVOS\"]}")
        @RequestParam(value = "filters", required = false) JSONObject filters,
        @ApiParam(value = "Configurações adicionais para o filtro",
                defaultValue = "{\"incluirProfessorInativo\":false}")
        @RequestParam(value = "configs", required = false) JSONObject configs,
        @RequestParam(value = "sort", required = false) String sort,
        @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
        @RequestHeader("empresaId") Integer empresaId,
        @ApiIgnore PaginadorDTO paginadorDTO,
        HttpServletRequest request
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filters, configs);
            filtroGestaoJSON.setCategoria(CategoriaIndicadorEnum.ATIVIDADES_PROF);
            return ResponseEntityFactory.ok(professorSinteticoService.carregarIndicadores(request, paginadorDTO, empresaId, filtroGestaoJSON, sort));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar indicadores da atividade de professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Indicadores de atividades acumuladas dos professores",
                  notes = "Carrega os indicadores acumulados das atividades dos professores ao longo do tempo",
                  tags = "Colaborador")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Indicadores acumulados carregados com sucesso", response = ExemploRespostaIndicadoresAcumulados.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/indicadores-atividades-acumuladas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> indicadoresAtividadesAcumuladasProfessor(
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(professorSinteticoService.carregarIndicadoresAcumulados(request, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar indicadores da atividade de professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Consultar alunos com aviso médico - visão geral",
                  notes = "Carrega informações gerais sobre alunos com aviso médico por professor",
                  tags = "Alunos com aviso médico")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Alunos com aviso médico carregados com sucesso", response = ExemploRespostaAlunosAvisoMedicoGeral.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/alunos-aviso-medico", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosAvisoMedicoGeral(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do professor.\n" +
                    "- <strong>dataInicio:</strong> Data de início do período para análise (timestamp).\n" +
                    "- <strong>dataFim:</strong> Data de fim do período para análise (timestamp).\n" +
                    "- <strong>colaboradorIds:</strong> Lista de IDs dos professores para filtro (ex: [1, 2, 3]).",
                    defaultValue = "{\"quicksearchValue\":\"Carlos\", \"dataInicio\":1640995200000, \"dataFim\":1672531199000, \"colaboradorIds\":[1]}")
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @ApiParam(value = "Configurações adicionais para o filtro",
                    defaultValue = "{\"incluirProfessorInativo\":false}")
            @RequestParam(value = "configs", required = false) JSONObject configs,
            @RequestParam(value = "sort", required = false) String sort,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filters, configs);
            return ResponseEntityFactory.ok(professorSinteticoService.alunosAvisoMedico(request, paginadorDTO, empresaId, filtroGestaoJSON, sort, true));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar indicadores da atividade de professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Consultar alunos com aviso médico - detalhado",
                  notes = "Carrega informações detalhadas sobre alunos específicos com aviso médico",
                  tags = "Alunos com aviso médico")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Alunos com aviso médico carregados com sucesso", response = ExemploRespostaAlunosAvisoMedicoDetalhado.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/alunos-aviso-medico/alunos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosAvisoMedicoAlunos(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do professor.\n" +
                    "- <strong>dataInicio:</strong> Data de início do período para análise (timestamp).\n" +
                    "- <strong>dataFim:</strong> Data de fim do período para análise (timestamp).\n" +
                    "- <strong>colaboradorIds:</strong> Lista de IDs dos professores para filtro (ex: [1, 2, 3]).",
                    defaultValue = "{\"quicksearchValue\":\"Ana\", \"dataInicio\":1640995200000, \"dataFim\":1672531199000, \"colaboradorIds\":[1]}")
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @ApiParam(value = "Configurações adicionais para o filtro",
                    defaultValue = "{\"incluirProfessorInativo\":false}")
            @RequestParam(value = "configs", required = false) JSONObject configs,
            @RequestParam(value = "sort", required = false) String sort,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filters, configs);
            return ResponseEntityFactory.ok(professorSinteticoService.alunosAvisoMedico(request, paginadorDTO, empresaId, filtroGestaoJSON, sort, false));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar indicadores da atividade de professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Alunos por indicadores de atividade dos professores",
                  notes = "Carrega a lista de alunos relacionados aos indicadores de atividade dos professores",
                  tags = "Atividades dos professores")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Alunos carregados com sucesso", response = ExemploRespostaAlunosAtividadeProfessores.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/indicadores-atividade/alunos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosPorIndicadoresAtividadeProfessor(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do professor.\n" +
                    "- <strong>dataInicio:</strong> Data de início do período para análise (timestamp).\n" +
                    "- <strong>dataFim:</strong> Data de fim do período para análise (timestamp).\n" +
                    "- <strong>colaboradorIds:</strong> Lista de IDs dos professores para filtro (ex: [1, 2, 3]).\n" +
                    "- <strong>modalidadeIds:</strong> Lista de IDs das modalidades para filtro (ex: [1, 2]).\n" +
                    "- <strong>situacaoPrograma:</strong> Lista de situações do programa para filtro (ex: [\"NOVOS\", \"RENOVADOS\"]).",
                    defaultValue = "{\"quicksearchValue\":\"Pedro\", \"dataInicio\":1640995200000, \"dataFim\":1672531199000, \"colaboradorIds\":[1], \"modalidadeIds\":[1], \"situacaoPrograma\":[\"RENOVADOS\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @ApiParam(value = "Configurações adicionais para o filtro",
                    defaultValue = "{\"incluirProfessorInativo\":false}")
            @RequestParam(value = "configs", required = false) JSONObject configs,
            @RequestParam(value = "sort", required = false) String sort,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filters, configs);
            filtroGestaoJSON.setCategoria(CategoriaIndicadorEnum.ATIVIDADES_PROF);
            return ResponseEntityFactory.ok(professorSinteticoService.carregarAlunosPorIndicador(request, paginadorDTO, empresaId, filtroGestaoJSON, sort));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar alunos por indicador da atividade de professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Incluir configuração de ranking de professores",
                  notes = "Inclui uma nova configuração para o ranking de professores",
                  tags = "Ranking")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configuração incluída com sucesso", response = ExemploRespostaConfiguracaoRanking.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/configuracao", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirConfiguracaoRankingProfessores(
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Dados da configuração de ranking a ser incluída", required = true)
            @RequestBody ConfiguracaoRankingDTO configuracaoRankingDTO
    ) {
        try {
            return ResponseEntityFactory.ok(professorSinteticoService.incluirConfiguracao(empresaId, configuracaoRankingDTO));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar configuração no ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Listar configurações de ranking de professores",
                  notes = "Lista as configurações existentes para o ranking de professores",
                  tags = "Ranking")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações listadas com sucesso", response = ExemploRespostaListaConfiguracaoRanking.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/configuracao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarConfiguracoesRankingProfessores(
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do indicador.",
                    defaultValue = "{\"quicksearchValue\":\"Alunos\"}")
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @ApiIgnore PaginadorDTO paginadorDTO
    ) {
        try {
            return ResponseEntityFactory.ok(professorSinteticoService.listarConfiguracoesRanking(empresaId, paginadorDTO, filters), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as configurações do ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Gravar configurações de ranking de professores",
                  notes = "Grava as configurações de ranking dos professores com base nos valores fornecidos",
                  tags = "Ranking")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações gravadas com sucesso", response = ExemploRespostaListaConfiguracaoRanking.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/configuracao", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> gravarConfigs(
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Mapa contendo os valores das configurações a serem gravadas", required = true)
            @RequestBody Map<String, String> valores
    ) {
        try {
            return ResponseEntityFactory.ok(professorSinteticoService.gravarConfiguracoesRanking(empresaId, valores));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as configurações do ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Alternar configuração de indicador no ranking",
                  notes = "Alterna o estado ativo/inativo de um indicador específico no ranking de professores",
                  tags = "Ranking")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configuração alternada com sucesso", response = ExemploRespostaToggleConfiguracao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/configuracao/toggle/{indicador}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> toggleConfiguracaoIndicador(
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Nome do indicador a ser alternado", required = true, defaultValue = "ATIVOS")
            @PathVariable("indicador") String indicador
    ) {
        try {
            return ResponseEntityFactory.ok(professorSinteticoService.toggleConfiguracoesRanking(empresaId, IndicadorDashboardEnum.valueOf(indicador)));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as configurações do ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Listar configurações de ranking de professores por agrupamento",
                  notes = "Lista as configurações de ranking de professores filtradas por categoria de agrupamento (ALUNOS, TREINO, AGENDA). " +
                          "Requer permissão de RANKING_PROFESSORES e autenticação via empresaId. " +
                          "Permite filtrar por nome do indicador usando quicksearchValue e suporta paginação e ordenação dos resultados.",
                  tags = "Colaborador")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "peso,desc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações de ranking listadas com sucesso", response = ExemploRespostaListaConfiguracaoRanking.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/configuracao/{agrup}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarConfiguracoesRankingProfessoresAgrupamento(
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Categoria de agrupamento das configurações de ranking. \n\n" +
                    "<strong>Valores disponíveis</strong>\n" +
                    "- ALUNOS (Indicadores relacionados aos alunos)\n" +
                    "- TREINO (Indicadores relacionados aos treinos)\n" +
                    "- AGENDA (Indicadores relacionados à agenda)", required = true, defaultValue = "ALUNOS")
            @PathVariable("agrup") String agrup,
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para filtrar por nome do indicador.",
                    defaultValue = "{\"quicksearchValue\":\"Alunos Ativos\"}")
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @ApiIgnore PaginadorDTO paginadorDTO
    ) {
        try {
            Map<AgrupamentoIndicadorDashboardEnum, List<ConfiguracaoRankingDTO>> map = professorSinteticoService.listarConfiguracoesRanking(empresaId, null, null);
            List<ConfiguracaoRankingDTO> dtos = map.get(AgrupamentoIndicadorDashboardEnum.valueOf(agrup));
            if(filters != null && !UteisValidacao.emptyString(filters.optString("quicksearchValue"))){
                String quickSearchValue = filters.optString("quicksearchValue");
                dtos = dtos.stream().filter(d -> d.getIndicadorSort().toLowerCase().contains(quickSearchValue.toLowerCase()))
                        .collect(Collectors.toList());
            }
            if(paginadorDTO != null){
                paginadorDTO.setQuantidadeTotalElementos(new Long(dtos.size()));
                if(paginadorDTO.getSortMap() == null){
                    dtos = Ordenacao.ordenarLista(dtos, "peso");
                    Collections.reverse(dtos);
                } else if(paginadorDTO.getSortMap().isEmpty() || paginadorDTO.getSortMap().get("pontuacao") != null){
                    dtos = Ordenacao.ordenarLista(dtos, "peso");
                } else if(paginadorDTO.getSortMap().get("indicador") != null){
                    dtos = Ordenacao.ordenarLista(dtos, "indicadorSort");
                } else {
                    dtos = Ordenacao.ordenarLista(dtos, "peso");
                }
                if(paginadorDTO.getSortMap() != null && !paginadorDTO.getSortMap().isEmpty() && paginadorDTO.getSortMap().values().contains("DESC")){
                    Collections.reverse(dtos);
                }
                if(paginadorDTO.getSize().intValue() < dtos.size()){
                    int init = paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
                    int end = init + paginadorDTO.getSize().intValue();
                    end = end > dtos.size() ? dtos.size() : end;
                    dtos = dtos.subList(init, end);
                }
            }
            return ResponseEntityFactory.ok(dtos, paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as configurações do ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Remover configuração de ranking de professores",
                  notes = "Remove uma configuração específica do ranking de professores pelo seu ID. " +
                          "A operação é irreversível e afetará o cálculo do ranking dos professores.",
                  tags = "Ranking")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configuração de ranking removida com sucesso", response = ExemploRespostaSucessoRankingProfessores.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/configuracao/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerConfiguracaoRankingProfessores(
            @ApiParam(value = "ID da configuração de ranking a ser removida", required = true, defaultValue = "1")
            @PathVariable("id") Integer configuracaoId
    ) {
        try {
            professorSinteticoService.removerConfiguracaoRanking(configuracaoId);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover a configuração do ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Atualizar ranking de professores",
                  notes = "Inicia o processo de atualização do ranking de professores para o período especificado. " +
                          "Requer permissão de RANKING_PROFESSORES e autenticação via empresaId. " +
                          "O processo é executado em background e calcula as pontuações dos professores baseado nas configurações ativas.",
                  tags = "Ranking")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Processo de atualização do ranking iniciado com sucesso", response = ExemploRespostaSucessoRankingProfessores.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/atualizar-ranking", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarRankingProfessores(
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Timestamp da data de início do período para atualização do ranking", required = true, defaultValue = "1640995200000")
            @RequestParam("inicio") Long inicio,
            @ApiParam(value = "Timestamp da data de fim do período para atualização do ranking", required = true, defaultValue = "1672531199000")
            @RequestParam("fim") Long fim
    ) throws ServiceException {
        try {
            Date inicioDate = new Date(inicio);
            Date fimDate = new Date(fim);
            Date inicioComHora = Calendario.getDataComHora(inicioDate, "00:00");
            rankingProfessoresService.gerarRankingNoBanco(sessaoService.getUsuarioAtual().getChave(),
                    inicioComHora,
                    fimDate,
                    SuperControle.independente(sessaoService.getUsuarioAtual().getChave()), sessaoService.getUsuarioAtual().getId(),
                    empresaId);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar o ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }


    }

    @ApiOperation(value = "Carregar ranking de professores",
                  notes = "Carrega o ranking dos professores com base nos critérios e período especificados",
                  tags = "Ranking")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "total,desc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Ranking carregado com sucesso", response = ExemploRespostaRankingProfessores.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> carregarRankingProfessores(
        @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
        @RequestHeader("empresaId") Integer empresaId,
        @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                "<strong>Filtros disponíveis:</strong>\n" +
                "- <strong>quicksearchValue:</strong> Termo de busca para nome do professor.\n" +
                "- <strong>colaboradorIds:</strong> Lista de IDs dos professores para filtro (ex: [1, 2, 3]).\n" +
                "- <strong>modalidadeIds:</strong> Lista de IDs das modalidades para filtro (ex: [1, 2]).",
                defaultValue = "{\"quicksearchValue\":\"Silva\", \"colaboradorIds\":[1], \"modalidadeIds\":[1]}")
        @RequestParam(value = "filters", required = false) JSONObject filtros,
        @ApiParam(value = "Configurações adicionais para o filtro",
                defaultValue = "{\"incluirProfessorInativo\":false}")
        @RequestParam(value = "configs", required = false) JSONObject configs,
        @ApiParam(value = "Timestamp da data de início do período para análise", required = true, defaultValue = "1640995200000")
        @RequestParam(value = "inicio") Long inicio,
        @ApiParam(value = "Timestamp da data de fim do período para análise", required = true, defaultValue = "1672531199000")
        @RequestParam(value = "fim") Long fim,
        @RequestParam(value = "sort", required = false) String sort,
        @ApiIgnore PaginadorDTO paginadorDTO,
        HttpServletRequest request
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filtros, configs);
            Date dateInicio = filtroGestaoJSON.getInicio() == null ? new Date(inicio) : filtroGestaoJSON.getInicio();
            Date dateFim = filtroGestaoJSON.getFim() == null ? new Date(fim) : filtroGestaoJSON.getFim();
            return ResponseEntityFactory.ok(rankingProfessoresService.carregarRankingProfessores(request,paginadorDTO,
                    dateInicio,
                    dateFim, empresaId, filtroGestaoJSON, sort, SuperControle.independente(sessaoService.getUsuarioAtual().getChave())), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar o ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Detalhes do professor no ranking",
                  notes = "Carrega os detalhes específicos de um professor no ranking",
                  tags = "Ranking")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Detalhes do professor carregados com sucesso", response = ExemploRespostaDetalhesProfessorRanking.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/detalhes/{idColaborador}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalhesProfessorRanking(
        @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
        @RequestHeader("empresaId") Integer empresaId,
        @ApiParam(value = "Configurações adicionais para o filtro",
                defaultValue = "{\"incluirProfessorInativo\":false}")
        @RequestParam(value = "configs", required = false) JSONObject configs,
        @ApiParam(value = "ID do colaborador/professor", required = true, defaultValue = "123")
        @PathVariable("idColaborador") Integer idColaborador,
        @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                "<strong>Filtros disponíveis:</strong>\n" +
                "- <strong>quicksearchValue:</strong> Termo de busca para nome do professor.\n" +
                "- <strong>colaboradorIds:</strong> Lista de IDs dos professores para filtro (ex: [1, 2, 3]).",
                defaultValue = "{\"quicksearchValue\":\"Silva\", \"colaboradorIds\":[123]}")
        @RequestParam(value = "filters", required = false) JSONObject filtros,
        @ApiParam(value = "Timestamp da data de início do período para análise", required = true, defaultValue = "1640995200000")
        @RequestParam(value = "inicio") Long inicio,
        @ApiParam(value = "Timestamp da data de fim do período para análise", required = true, defaultValue = "1672531199000")
        @RequestParam(value = "fim") Long fim,
        HttpServletRequest request
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filtros);
            Date dateInicio = new Date(inicio);
            Date dateFim = new Date(fim);
            return ResponseEntityFactory.ok(rankingProfessoresService.detalhesProfessor(
                    request,
                    dateInicio,
                    dateFim, empresaId,
                    idColaborador,
                    filtroGestaoJSON, SuperControle.independente(sessaoService.getUsuarioAtual().getChave())));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar o ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Compartilhar detalhes do ranking de um professor específico",
                  notes = "Obtém informações detalhadas dos indicadores de desempenho de um professor específico no ranking para compartilhamento. " +
                          "Requer permissão de acesso ao ranking de professores e autenticação via ID da empresa. " +
                          "Retorna lista de indicadores com valores, pontuações e períodos de análise organizados por categorias.",
                  tags = "Ranking")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Detalhes do ranking do professor obtidos com sucesso", response = ExemploRespostaCompartilharRankingProfessor.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/detalhes/compartilhar/{idColaborador}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> compartilharRankingProfessores(
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Configurações adicionais para o ranking", required = false)
            @RequestParam(value = "configs", required = false) JSONObject configs,
            @ApiParam(value = "ID do colaborador/professor para obter detalhes", required = true, defaultValue = "1")
            @PathVariable("idColaborador") Integer idColaborador,
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do professor.\n" +
                    "- <strong>colaboradorIds:</strong> Lista de IDs dos professores para filtro (Deve ser informado como uma lista de códigos/IDs ex: [1, 2]).\n" +
                    "- <strong>tiposEvento:</strong> Lista de IDs dos tipos de eventos para filtro (Deve ser informado como uma lista de códigos/IDs ex: [1, 2]).",
                    defaultValue = "{\"quicksearchValue\":\"Silva\", \"colaboradorIds\":[1], \"tiposEvento\":[1]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiParam(value = "Timestamp da data de início do período para análise", required = true, defaultValue = "1640995200000")
            @RequestParam(value = "inicio") Long inicio,
            @ApiParam(value = "Timestamp da data de fim do período para análise", required = true, defaultValue = "1672531199000")
            @RequestParam(value = "fim") Long fim,
            HttpServletRequest request
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filtros);
            Date dateInicio = new Date(inicio);
            Date dateFim = new Date(fim);
            return ResponseEntityFactory.ok(rankingProfessoresService.detalhesCompartilharProfessor(
                    request,
                    dateInicio,
                    dateFim, empresaId,
                    idColaborador,
                    filtroGestaoJSON, SuperControle.independente(sessaoService.getUsuarioAtual().getChave())));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar o ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Obter lista de indicadores adicionados ao ranking",
                  notes = "Obtém a lista de indicadores que foram configurados e estão ativos para o ranking de professores da empresa. " +
                          "Requer permissão de acesso ao ranking de professores e autenticação via ID da empresa. " +
                          "Retorna lista ordenada de indicadores disponíveis para análise de desempenho.",
                  tags = "Colaborador")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista de indicadores obtida com sucesso", response = ExemploRespostaIndicadoresAdd.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/indicadores-add", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> indicadoresAdd(
        @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
        @RequestHeader("empresaId") Integer empresaId,
        HttpServletRequest request
    ) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<ConfiguracaoRankingProfessores> listConfig = (UtilContext.getBean(DashboardBIService.class)).obterCfgRanking(ctx, null, empresaId, null, true);
            List<IndicadorDashboardEnum> indicadoresAdicionados = new ArrayList(){{
               for(ConfiguracaoRankingProfessores cfg : listConfig){
                   add(cfg.getIndicador());
               }
            }};
            return ResponseEntityFactory.ok(Ordenacao.ordenarLista(indicadoresAdicionados, "labelSort"));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar o ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Obter pódium do ranking de professores",
                  notes = "Obtém os professores com melhor desempenho no ranking (pódium) para o período especificado. " +
                          "Requer permissão de acesso ao ranking de professores e autenticação via ID da empresa. " +
                          "Retorna lista dos professores mais bem posicionados com suas pontuações e indicadores de desempenho.",
                  tags = "Colaborador")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Pódium do ranking obtido com sucesso", response = ExemploRespostaPodiumRanking.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/podium", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> podium(
        @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
        @RequestHeader("empresaId") Integer empresaId,
        @ApiParam(value = "Filtros de busca em formato JSON string.\n\n" +
                "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                "<strong>Filtros disponíveis:</strong>\n" +
                "- <strong>quicksearchValue:</strong> Termo de busca para nome do professor.\n" +
                "- <strong>colaboradorIds:</strong> Lista de IDs dos professores para filtro (ex: [1, 2]).\n" +
                "- <strong>tiposEvento:</strong> Lista de IDs dos tipos de eventos para filtro (ex: [1, 2]).",
                defaultValue = "{\"quicksearchValue\":\"Silva\", \"colaboradorIds\":[1], \"tiposEvento\":[1]}")
        @RequestParam(value = "filters", required = false) String filtros,
        @ApiParam(value = "Timestamp da data de início do período para análise", required = true, defaultValue = "1640995200000")
        @RequestParam(value = "inicio") Long inicio,
        @ApiParam(value = "Timestamp da data de fim do período para análise", required = true, defaultValue = "1672531199000")
        @RequestParam(value = "fim") Long fim,
        HttpServletRequest request
    ) {
        try {
            JSONObject filtro = new JSONObject();
            if(!UteisValidacao.emptyString(filtros)) {
                filtro = new JSONObject(filtros);
            }
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filtro);
            Date dateInicio = new Date(inicio);
            Date dateFim = new Date(fim);
            return ResponseEntityFactory.ok(rankingProfessoresService.podium(
                    request,
                    dateInicio,
                    dateFim, empresaId,
                    filtroGestaoJSON, SuperControle.independente(sessaoService.getUsuarioAtual().getChave())));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar o ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Salvar configuração de relatório",
                  notes = "Salva ou atualiza a configuração personalizada de um relatório para o usuário atual. " +
                          "Requer permissão de acesso aos colaboradores e autenticação do usuário. " +
                          "Permite personalizar a exibição e filtros de relatórios específicos.",
                  tags = "Colaborador")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configuração de relatório salva com sucesso", response = ExemploRespostaConfigRelatorio.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/config-relatorio/{configId}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirAlterarFiltroConfigRel(
            @ApiParam(value = "Configuração do relatório em formato JSON string", required = false,
                      defaultValue = "{\"columns\":[\"nome\",\"email\",\"telefone\"], \"filters\":{\"ativo\":true}, \"sorting\":{\"field\":\"nome\",\"order\":\"asc\"}}")
            @RequestParam(value = "config", required = false) String config,
            @ApiParam(value = "Identificador único da configuração do relatório", required = true, defaultValue = "relatorio-professores-001")
            @PathVariable("configId") String configId) throws ServiceException {
        try{
            return ResponseEntityFactory.ok(professorSinteticoService.incluirAtualizarConfigRelatorio(configId, config));
        }catch (ServiceException e ){
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao salvar config relátorio");
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Obter configuração de relatório",
                  notes = "Obtém a configuração personalizada salva de um relatório específico para o usuário atual. " +
                          "Requer permissão de acesso aos colaboradores e autenticação do usuário. " +
                          "Retorna as configurações de exibição e filtros previamente salvos para o relatório.",
                  tags = "Colaborador")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configuração de relatório obtida com sucesso", response = ExemploRespostaConfigRelatorio.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/config-relatorio/{configId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterConfigRel(
            @ApiParam(value = "Identificador único da configuração do relatório", required = true, defaultValue = "relatorio-professores-001")
            @PathVariable("configId") String configId) throws Exception {
        try{
            return ResponseEntityFactory.ok(professorSinteticoService.obterConfigRelatorio(configId));
        }catch (ServiceException e){
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao obter config relátorio");
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Carrega relatório de professores que foram substituídos em aulas",
                  notes = "Carrega relatório de professores que foram substituídos em aulas",
                  tags = "Professores substituídos")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Relatório de professores substituídos carregado com sucesso", response = ExemploRespostaProfessoresSubstituidos.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/substituidos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> professoresSubstituidos(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do professor.\n" +
                    "- <strong>dataInicio:</strong> Data de início do período para análise (timestamp).\n" +
                    "- <strong>dataFim:</strong> Data de fim do período para análise (timestamp).\n" +
                    "- <strong>colaboradorIds:</strong> Lista de IDs dos professores para filtro (ex: [1, 2, 3]).",
                    defaultValue = "{\"quicksearchValue\":\"Santos\", \"dataInicio\":1640995200000, \"dataFim\":1672531199000, \"colaboradorIds\":[1]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        try{
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filtros);
            return ResponseEntityFactory.ok(gestaoSalaCheiaService.montarBiSubstituidosSpa(filtroGestaoJSON, paginadorDTO), paginadorDTO);
        }catch (ServiceException e){
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao obter relátorio de professores substituidos");
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_PROFESSORES)
    @RequestMapping(value = "/ranking/conferir", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> conferir(
            @RequestHeader("empresaId") Integer empresaId,
            @RequestParam(value = "filters", required = false) String filtros,
            @RequestParam(value = "inicio") Long inicio,
            @RequestParam(value = "fim") Long fim
    ) {
        try {
            JSONObject filtro = new JSONObject();
            if(!UteisValidacao.emptyString(filtros)) {
                filtro = new JSONObject(filtros);
            }
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filtro);
            Date dateInicio = new Date(inicio);
            Date dateFim = new Date(fim);
            rankingProfessoresService.conferir(
                    dateInicio,
                    dateFim, empresaId,
                    filtroGestaoJSON, SuperControle.independente(sessaoService.getUsuarioAtual().getChave()));
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar o ranking dos professores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
