package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.ficha.Ficha;

import java.util.Date;

/**
 * <AUTHOR> 16/01/2019
 */
public class FichaExecutada {

    private Integer id;
    private String nome;
    private Date data;

    public FichaExecutada(Ficha ficha) {
        this.id = ficha.getCodigo();
        this.nome = ficha.getNome();
        this.data = ficha.getUltimaExecucao();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }
}
