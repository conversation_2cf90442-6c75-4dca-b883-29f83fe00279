package br.com.pacto.controller.json.tipoEvento;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.programa.AparelhoController;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.tipoEvento.TipoEventoService;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 08/11/2018
 */
@Controller
@RequestMapping("/psec/tipos-agendamento")
public class TipoEventoController {

    private final TipoEventoService tipoEventoService;

    @Autowired
    public TipoEventoController(TipoEventoService tipoEventoService) {
        Assert.notNull(tipoEventoService, "O serviço de tipo evento não foi injetado corretamente");
        this.tipoEventoService = tipoEventoService;
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_EVENTO)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarTipoEvento(@RequestParam(value = "filters", required = false) JSONObject filtro,
                                                                   PaginadorDTO paginadorDTO) {
        try {
            String nome = "";
            if(filtro != null){
                nome =  filtro.optString("quicksearchValue").equals("") ? null : filtro.optString("quicksearchValue");
            }
            Boolean ativos = filtro != null && filtro.has("ativos") ? filtro.optBoolean("ativos") : true;
            return ResponseEntityFactory.ok(tipoEventoService.coltultarTtipoAgendamento(nome,
                    ativos,
                    paginadorDTO, true), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AparelhoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os Tipos de Eventos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_EVENTO)
    @RequestMapping(value = "/all-ativos-inativos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarTipoEventoAtivoInativo(@RequestParam(value = "filters", required = false) JSONObject filtro,
                                                                               PaginadorDTO paginadorDTO) {
        try {
            String nome = "";
            if (filtro != null) {
                nome =  filtro.optString("quicksearchValue").equals("") ? null : filtro.optString("quicksearchValue");
            }
            return ResponseEntityFactory.ok(tipoEventoService.coltultarTtipoAgendamento(nome,
                    false,
                    paginadorDTO, false), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(TipoEventoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os Tipos de Eventos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_EVENTO)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosTipoaEvento() {

        try {
            return ResponseEntityFactory.ok(tipoEventoService.obterTodosAtivos());
        } catch (ServiceException e) {
            Logger.getLogger(TipoEventoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar todos os tipos evento", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_EVENTO)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarTipoAgendamento(@PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(tipoEventoService.buscarTiposAgendamento(id));
        } catch (ServiceException e) {
            Logger.getLogger(TipoEventoController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar o tipo de evento", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_EVENTO)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarTipoAgendamento(@RequestBody TipoAgendamentoCadDTO agendamento) {
        try {
            return ResponseEntityFactory.ok(tipoEventoService.cadastrarTipoAgendamento(agendamento));
        } catch (ServiceException e) {
            Logger.getLogger(TipoEventoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir tipo agendamento", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_EVENTO)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarTipoAgendamento(@PathVariable("id") final Integer id,
                                                                @RequestBody TipoAgendamentoCadDTO tipoEventoDTO) {
        try {
            tipoEventoDTO.setId(id);
            return ResponseEntityFactory.ok(tipoEventoService.atualizarTipoAgendamento(tipoEventoDTO));
        } catch (ServiceException e) {
            Logger.getLogger(TipoEventoController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar tipo de evento", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                if (e.getCause().getMessage().equals("registro_esta_sendo_utilizado")) {
                    return ResponseEntityFactory.erroRegistroEstaSendoUtilizado(e.getChaveExcecao(), e.getCause().getMessage());
                } else {
                    return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
                }
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_EVENTO)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerEvento(@PathVariable("id") final Integer id){
        try {
            tipoEventoService.removerTipoAgendamento(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(TipoEventoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir tipo agendamento", e);
            return ResponseEntityFactory.erroRegistroEstaSendoUtilizado(e.getChaveExcecao(), e.getMessage());
        }
    }

}
