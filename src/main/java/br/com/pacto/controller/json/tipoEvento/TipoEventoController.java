package br.com.pacto.controller.json.tipoEvento;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.programa.AparelhoController;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.tipoEvento.TipoEventoService;
import br.com.pacto.swagger.respostas.tipoEvento.*;
import io.swagger.annotations.*;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by Giovanne Ramos on 08/11/2018
 */
@Api(tags = "Tipo de Agendamento")
@Controller
@RequestMapping("/psec/tipos-agendamento")
public class TipoEventoController {

    private final TipoEventoService tipoEventoService;

    @Autowired
    public TipoEventoController(TipoEventoService tipoEventoService) {
        Assert.notNull(tipoEventoService, "O serviço de tipo evento não foi injetado corretamente");
        this.tipoEventoService = tipoEventoService;
    }

    @ApiOperation(
            value = "Consultar tipos de evento com paginação",
            notes = "Consulta tipos de evento da empresa com suporte a filtros e paginação. " +
                    "Permite filtrar por nome e status ativo/inativo. " +
                    "Retorna uma lista paginada com os dados dos tipos de evento encontrados.",
            tags = "Tipo de Agendamento"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista paginada de tipos de evento)", response = ExemploRespostaListTipoAgendamentoDTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_EVENTO)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarTipoEvento(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do tipo de evento.\n" +
                    "- <strong>ativos:</strong> Filtra por tipos de evento ativos ou inativos (true/false).",
                    defaultValue = "{\"quicksearchValue\":\"Avaliação\", \"ativos\":true}")
            @RequestParam(value = "filters", required = false) JSONObject filtro,
            @ApiIgnore
            PaginadorDTO paginadorDTO) {
        try {
            String nome = "";
            if(filtro != null){
                nome =  filtro.optString("quicksearchValue").equals("") ? null : filtro.optString("quicksearchValue");
            }
            Boolean ativos = filtro != null && filtro.has("ativos") ? filtro.optBoolean("ativos") : true;
            return ResponseEntityFactory.ok(tipoEventoService.coltultarTtipoAgendamento(nome,
                    ativos,
                    paginadorDTO, true), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AparelhoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os Tipos de Eventos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar tipos de evento ativos e inativos com paginação",
            notes = "Consulta todos os tipos de evento da empresa (ativos e inativos) com suporte a filtros e paginação. " +
                    "Permite filtrar por nome. " +
                    "Retorna uma lista paginada com os dados dos tipos de evento encontrados.",
            tags = "Tipo de Agendamento"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista paginada de tipos de evento ativos e inativos)", response = ExemploRespostaListTipoAgendamentoDTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_EVENTO)
    @RequestMapping(value = "/all-ativos-inativos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarTipoEventoAtivoInativo(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do tipo de evento.",
                    defaultValue = "{\"quicksearchValue\":\"Prescrição\"}")
            @RequestParam(value = "filters", required = false) JSONObject filtro,
            @ApiIgnore
            PaginadorDTO paginadorDTO) {
        try {
            String nome = "";
            if (filtro != null) {
                nome =  filtro.optString("quicksearchValue").equals("") ? null : filtro.optString("quicksearchValue");
            }
            return ResponseEntityFactory.ok(tipoEventoService.coltultarTtipoAgendamento(nome,
                    false,
                    paginadorDTO, false), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(TipoEventoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os Tipos de Eventos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar todos os tipos de evento ativos",
            notes = "Consulta todos os tipos de evento ativos da empresa sem paginação. " +
                    "Retorna uma lista completa com todos os tipos de evento ativos disponíveis.",
            tags = "Tipo de Agendamento"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista de todos os tipos de evento ativos)", response = ExemploRespostaListTipoAgendamentoDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_EVENTO)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosTipoaEvento() {

        try {
            return ResponseEntityFactory.ok(tipoEventoService.obterTodosAtivos());
        } catch (ServiceException e) {
            Logger.getLogger(TipoEventoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar todos os tipos evento", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar tipo de evento por ID",
            notes = "Consulta um tipo de evento específico através do seu identificador único. " +
                    "Retorna os dados completos do tipo de evento solicitado.",
            tags = "Tipo de Agendamento"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Dados do tipo de evento)", response = ExemploRespostaTipoAgendamentoDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_EVENTO)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarTipoAgendamento(
            @ApiParam(value = "ID único do tipo de evento a ser consultado", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(tipoEventoService.buscarTiposAgendamento(id));
        } catch (ServiceException e) {
            Logger.getLogger(TipoEventoController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar o tipo de evento", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Cadastrar novo tipo de evento",
            notes = "Cadastra um novo tipo de evento no sistema com todas as configurações necessárias. " +
                    "Inclui informações como nome, comportamento, duração, cor e outras configurações específicas.",
            tags = "Tipo de Agendamento"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Tipo de evento cadastrado com sucesso)", response = ExemploRespostaTipoAgendamentoCadDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_EVENTO)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarTipoAgendamento(
            @ApiParam(value = "Dados completos do tipo de evento para cadastro", required = true)
            @RequestBody TipoAgendamentoCadDTO agendamento) {
        try {
            return ResponseEntityFactory.ok(tipoEventoService.cadastrarTipoAgendamento(agendamento));
        } catch (ServiceException e) {
            Logger.getLogger(TipoEventoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir tipo agendamento", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Atualizar tipo de evento existente",
            notes = "Atualiza as informações de um tipo de evento existente incluindo configurações de duração, " +
                    "comportamento, cor e outras propriedades. Todos os campos enviados serão atualizados.",
            tags = "Tipo de Agendamento"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Tipo de evento atualizado com sucesso)", response = ExemploRespostaTipoAgendamentoDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_EVENTO)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarTipoAgendamento(
            @ApiParam(value = "ID único do tipo de evento a ser atualizado", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id,
            @ApiParam(value = "Dados atualizados do tipo de evento", required = true)
            @RequestBody TipoAgendamentoCadDTO tipoEventoDTO) {
        try {
            tipoEventoDTO.setId(id);
            return ResponseEntityFactory.ok(tipoEventoService.atualizarTipoAgendamento(tipoEventoDTO));
        } catch (ServiceException e) {
            Logger.getLogger(TipoEventoController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar tipo de evento", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                if (e.getCause().getMessage().equals("registro_esta_sendo_utilizado")) {
                    return ResponseEntityFactory.erroRegistroEstaSendoUtilizado(e.getChaveExcecao(), e.getCause().getMessage());
                } else {
                    return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
                }
            }
        }
    }

    @ApiOperation(
            value = "Remover tipo de evento",
            notes = "Remove um tipo de evento do sistema através do seu identificador único. " +
                    "Esta operação é irreversível e só pode ser realizada se o tipo de evento não estiver sendo utilizado.",
            tags = "Tipo de Agendamento"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Tipo de evento removido com sucesso)", response = ExemploRespostaRemocaoTipoEvento.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_EVENTO)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerEvento(
            @ApiParam(value = "ID único do tipo de evento a ser removido", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id){
        try {
            tipoEventoService.removerTipoAgendamento(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(TipoEventoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir tipo agendamento", e);
            return ResponseEntityFactory.erroRegistroEstaSendoUtilizado(e.getChaveExcecao(), e.getMessage());
        }
    }

}
