package br.com.pacto.controller.json.configDisponibilidade;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.agendamento.DisponibilidadeConfigGeradoraDTO;
import br.com.pacto.controller.json.agendamento.FiltroDisponibilidadeDTO;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.agenda.DisponibilidadeService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;


/**
 * Created by paulo 30/08/2019
 */
@Controller
@RequestMapping("/psec/disponibilidade-config")
public class ConfigDisponibilidadeController {

    private DisponibilidadeService disponibilidadeService;

    @Autowired
    public ConfigDisponibilidadeController(DisponibilidadeService disponibilidadeService) {
        Assert.notNull(disponibilidadeService, "O serviço de disponibilidade não foi injetado corretamente");
        this.disponibilidadeService = disponibilidadeService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA_DISPONIBILIDADE)
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarDisponibilidade(@RequestBody DisponibilidadeConfigGeradoraDTO disponibilidadeConfig) {

        try {
            return ResponseEntityFactory.ok(disponibilidadeService.criarAlterarDisponibilidadeConfig(disponibilidadeConfig));
        } catch (ServiceException e) {
            Logger.getLogger(ConfigDisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar disponibilidade", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA_DISPONIBILIDADE)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> editarDisponibilidade(@PathVariable(value = "id")Integer disponibilidadeId,
                                                                     @RequestBody DisponibilidadeConfigGeradoraDTO disponibilidadeConfig) {

        try {
            disponibilidadeConfig.setId(disponibilidadeId);
            return ResponseEntityFactory.ok(disponibilidadeService.criarAlterarDisponibilidadeConfig(disponibilidadeConfig));
        } catch (ServiceException e) {
            Logger.getLogger(ConfigDisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar disponibilidade", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA_DISPONIBILIDADE)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerDisponibilidade(
            @RequestHeader(value = "empresaId")Integer empresaId,
            @PathVariable(value = "id")Integer disponibilidadeId) {

        try {
            disponibilidadeService.removerDisponibilidade(empresaId, disponibilidadeId, false);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(ConfigDisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover disponibilidade geradora", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA_DISPONIBILIDADE)
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarDisponibilidade(@RequestHeader(value = "empresaId")Integer empresaId,
                                                                        @RequestParam(value = "filters")JSONObject filters,
                                                                        PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroDisponibilidadeDTO filtros = new FiltroDisponibilidadeDTO(filters);
            return ResponseEntityFactory.ok(disponibilidadeService.todasDisponibilidades(empresaId, paginadorDTO, filtros), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ConfigDisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar disponibilidades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
