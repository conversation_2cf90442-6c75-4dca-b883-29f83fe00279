package br.com.pacto.controller.json.professor;

import java.util.ArrayList;
import java.util.List;

public class DetalhesProfessorRankingDTO {

    private String dataCadastro;
    private String modalidade;
    private Integer cargaHoraria;
    private List<InfoProfessorRankingDTO> linha = new ArrayList<>();
    private List<InfoProfessorRankingDTO> treino = new ArrayList<>();
    private List<InfoProfessorRankingDTO> agenda = new ArrayList<>();
    private List<InfoProfessorRankingDTO> alunos = new ArrayList<>();
    private List<InfoProfessorRankingDTO> content = new ArrayList<>();

    public Integer getCargaHoraria() { return cargaHoraria; }

    public void setCargaHoraria(Integer cargaHoraria) { this.cargaHoraria = cargaHoraria; }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public List<InfoProfessorRankingDTO> getLinha() {
        return linha;
    }

    public void setLinha(List<InfoProfessorRankingDTO> linha) {
        this.linha = linha;
    }

    public List<InfoProfessorRankingDTO> getTreino() {
        return treino;
    }

    public void setTreino(List<InfoProfessorRankingDTO> treino) {
        this.treino = treino;
    }

    public List<InfoProfessorRankingDTO> getAgenda() {
        return agenda;
    }

    public void setAgenda(List<InfoProfessorRankingDTO> agenda) {
        this.agenda = agenda;
    }

    public List<InfoProfessorRankingDTO> getAlunos() {
        return alunos;
    }

    public void setAlunos(List<InfoProfessorRankingDTO> alunos) {
        this.alunos = alunos;
    }

    public String getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(String dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public List<InfoProfessorRankingDTO> getContent() { return content; }

    public void setContent(List<InfoProfessorRankingDTO> content) { this.content = content; }
}
