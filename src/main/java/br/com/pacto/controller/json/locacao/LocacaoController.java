package br.com.pacto.controller.json.locacao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.locacao.AlunoLocacaoHorarioPlayCheckinCheckoutDTO;
import br.com.pacto.bean.locacao.AlunoLocacaoHorarioPlayDTO;
import br.com.pacto.bean.locacao.LocacaoPlayCanceladaFinalizadaDTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.controller.json.aulaDia.AulasController;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.locacao.AlunoLocacaoHorarioPlayService;
import br.com.pacto.service.intf.locacao.LocacaoPlayCanceladaFinalizadaService;
import br.com.pacto.service.intf.locacao.LocacaoService;
import br.com.pacto.service.intf.locacao.LocacaoHorarioService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/locacoes")
public class LocacaoController extends SuperController {

    private final LocacaoService locacaoService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private LocacaoHorarioService locacaoHorarioService;
    @Autowired
    private AlunoLocacaoHorarioPlayService alunoLocacaoHorarioPlayService;
    @Autowired
    private LocacaoPlayCanceladaFinalizadaService locacaoPlayCanceladaFinalizadaService;

    @Autowired
    public LocacaoController(LocacaoService locacaoService) {
        Assert.notNull(locacaoService, "O serviço de locação não foi injetado corretamente");
        this.locacaoService = locacaoService;
    }

    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultar(@RequestParam(value = "filters", required = false) JSONObject filters,
                                                           PaginadorDTO paginadorDTO,
                                                           @RequestHeader(value = "empresaId") Integer empresaId) throws JSONException {
        try {
            FiltrosLocacaoJSON filtros = new FiltrosLocacaoJSON(filters);
            return ResponseEntityFactory.ok(locacaoService.consultar(filtros, empresaId, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LocacaoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar locações", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{codigo}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultar(@PathVariable Integer codigo) throws JSONException {
        try {
            return ResponseEntityFactory.ok(locacaoService.consultarPorCodigo(codigo));
        } catch (ServiceException e) {
            Logger.getLogger(LocacaoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar locações", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrar(
            @RequestHeader (value = "empresaId", required = true) Integer empresaId,
            @RequestBody LocacaoTO locacaoTO){
        try {
            return ResponseEntityFactory.ok(locacaoService.cadastrar(locacaoTO, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }
    @ResponseBody
    @RequestMapping(value = "/addAlunoHorarioPlay", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrar(@RequestBody AlunoLocacaoHorarioPlayDTO alunoLocacaoHorarioPlayDTO){
        try {
            return ResponseEntityFactory.ok(alunoLocacaoHorarioPlayService.addAlunoHorarioPlay(alunoLocacaoHorarioPlayDTO, sessaoService.getUsuarioAtual().getChave()));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }
    @ResponseBody
    @RequestMapping(value = "/cancelaLocacaoHorarioPlay", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cancelaLocacaoHorarioPlay(@RequestBody LocacaoPlayCanceladaFinalizadaDTO locacaoPlayCanceladaFinalizadaDTO){
        try {
            return ResponseEntityFactory.ok(locacaoPlayCanceladaFinalizadaService.cancelaLocacaoHorarioPlay(locacaoPlayCanceladaFinalizadaDTO));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/addAlunoHorarioPlayCheckinCheckout", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> addAlunoHorarioPlayCheckinCheckout(@RequestBody AlunoLocacaoHorarioPlayCheckinCheckoutDTO dto){
        try {
            return ResponseEntityFactory.ok(alunoLocacaoHorarioPlayService.addAlunoHorarioPlayCheckinCheckout(dto, sessaoService.getUsuarioAtual().getChave()));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{codigoLocacao}/locacao-horarios", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdateLocacaoHorarios(@PathVariable (value = "codigoLocacao") Integer codigoLocacao,
                                                                           @RequestBody List<LocacaoHorarioTO> horarios){
        try {
            return ResponseEntityFactory.ok(locacaoService.saveOrUpdateLocacaoHorarios(codigoLocacao, horarios));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/locacao-horario/{codigo}/{ambiente}/{dia}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> locacaoHorario(@PathVariable Integer codigo,
                                                              @PathVariable Integer ambiente,
                                                              @PathVariable String dia) throws JSONException {
        try {
            return ResponseEntityFactory.ok(locacaoHorarioService.findById(codigo, dia, ambiente));
        } catch (ServiceException e) {
            Logger.getLogger(LocacaoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir locação horário", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/aluno-locacao-horario-play/{codigo}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> deleteAlunoLocacaoHorarioPlay(@PathVariable Integer codigo) throws JSONException {
        try {
            return ResponseEntityFactory.ok(alunoLocacaoHorarioPlayService.deleteAlunoLocacaoHorarioPlay(codigo, sessaoService.getUsuarioAtual().getChave()));
        } catch (ServiceException e) {
            Logger.getLogger(LocacaoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir locação horário", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/locacao-horario/{codigo}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> deletarLocacaoHorario(@PathVariable Integer codigo) throws JSONException {
        try {
            locacaoService.deletarLocacaoHorario(codigo);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(LocacaoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir locação horário", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/agendar/{dia}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendar(
            @RequestHeader (value = "empresaId") Integer empresaId,
            @PathVariable String dia,
            @RequestBody AgendamentoLocacaoDTO agendamentoLocacaoDTO) {
        try {
            return ResponseEntityFactory.ok(locacaoService.agendar(
                    Calendario.getDate("yyyyMMdd", dia),
                    agendamentoLocacaoDTO, empresaId, sessaoService.getUsuarioAtual().getChave(),
                    sessaoService.getUsuarioAtual().getId()));
        } catch (ParseException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar agendar a locação", e);
            return ResponseEntityFactory.erroInterno("error_date", e.getMessage());
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar agendar a locação", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterar(
            @RequestHeader (value = "empresaId", required = true) Integer empresaId,
            @RequestBody LocacaoTO locacaoTO){
        try {
            return ResponseEntityFactory.ok(locacaoService.alterar(locacaoTO, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{codigo}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> deletar(@PathVariable Integer codigo) throws JSONException {
        try {
            locacaoService.deletar(codigo);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(LocacaoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar locações", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/disponibilidades/{dia}/{horaSelecionada}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aulaDetalhada(
            @RequestHeader("empresaId") Integer empresaId,
            @PathVariable("dia") String dia,
            @PathVariable("horaSelecionada") String horaSelecionada,
            PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(locacaoService.disponibilidades(empresaId, sessaoService.getUsuarioAtual().getChave(),
                    new Date(Long.parseLong(dia)), horaSelecionada, -1, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/config/agendamento/{idHorario}/{agendamento}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aulaDetalhada(
            @PathVariable("idHorario") Integer idHorario,
            @PathVariable("agendamento") Integer agendamento,
            @RequestParam(value = "data", required = false) String data) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            if (UteisValidacao.emptyNumber(agendamento)) {
                return ResponseEntityFactory.ok(locacaoService.configAgendamento(idHorario, data, ctx));
            }
            return ResponseEntityFactory.ok(locacaoService.editAgendamento(agendamento, data, ctx));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/tiposHorarios", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterLocacaoTipoHorario() throws JSONException {
        List<TipoHorarioLocacaoDTO> tiposHorarios = new ArrayList<>();
        for(TipoHorarioLocacaoEnum thl: TipoHorarioLocacaoEnum.values()) {
            tiposHorarios.add(new TipoHorarioLocacaoDTO(thl.getCodigo(), thl.getDescricao()));
        }
        return ResponseEntityFactory.ok(tiposHorarios);
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/detalhes-agendamento/{codigoAgendamentoLocacao}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalhesAgendamentoLocacao(@PathVariable("codigoAgendamentoLocacao") Integer codigoAgendamentoLocacao, @RequestParam(value = "data", required = false) String data) {
        try {
            return ResponseEntityFactory.ok(locacaoService.editAgendamento(codigoAgendamentoLocacao, data, sessaoService.getUsuarioAtual().getChave()));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/cancelar-agendamento-locacao/{codigoAgendamentoLocacao}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cancelarAgendamentoLocacao(@PathVariable("codigoAgendamentoLocacao") Integer codigoAgendamentoLocacao,
                                                                          @RequestBody Map<String, String> requestBody) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer codigoUsuarioZW = sessaoService.getUsuarioAtual().getId();
            return ResponseEntityFactory.ok(locacaoService.cancelarAgendamentoLocacao(ctx, codigoUsuarioZW, false, codigoAgendamentoLocacao, requestBody.get("justificativa")));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/finalizar-agendamento-locacao/{codigoAgendamentoLocacao}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> finalizarAgendamentoLocacao(@PathVariable("codigoAgendamentoLocacao") Integer codigoAgendamentoLocacao,
                                                                           @RequestParam("data") String data,
                                                                           @RequestHeader(value = "empresaId") Integer empresaId,
                                                                           @RequestBody AgendamentoLocacaoDTO agendamentoLocacaoDTO) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer codigoUsuarioZW = sessaoService.getUsuarioAtual().getId();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            return ResponseEntityFactory.ok(
                    locacaoService.finalizarAgendamentoLocacao(ctx,
                            codigoUsuarioZW,
                            codigoAgendamentoLocacao,
                            agendamentoLocacaoDTO, Date.from(LocalDate.parse(data, formatter).atStartOfDay(ZoneId.systemDefault()).toInstant()), empresaId)
            );
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/validar-reagendamento/{codigoAgendamentoLocacao}/{novaData}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> validarDataReagendamento(@PathVariable("codigoAgendamentoLocacao") Integer codigoAgendamentoLocacao,
                                                                        @PathVariable("novaData") String novaData) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Date date;
            try {
                date = new Date(Long.parseLong(novaData));
            } catch (Exception e) {
                throw new ServiceException("A data fornecida não está no formato válido.");
            }

            return ResponseEntityFactory.ok(locacaoService.validarDataReagendamento(ctx, codigoAgendamentoLocacao, date));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/reagendar/{codigoAgendamentoLocacao}/{dia}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> reagendar(@PathVariable("codigoAgendamentoLocacao") Integer codigoAgendamentoLocacao,
                                                         @PathVariable("dia") String dia,
                                                         @RequestBody AgendamentoLocacaoDTO agendamentoLocacaoDTO){
        try {
            Date date;
            try {
                date = new Date(Long.parseLong(dia));
            } catch (Exception e) {
                throw new ServiceException("A data fornecida não está no formato válido.");
            }
            locacaoService.reagendar(codigoAgendamentoLocacao, agendamentoLocacaoDTO, date, sessaoService.getUsuarioAtual().getChave());
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar reagendar a locação", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/sincronizar-venda-avulsa/{agendamentoLocacaoCodigo}/{vendaAvulsaCodigo}", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> sincronizarVendaAvulsa(@PathVariable("agendamentoLocacaoCodigo") Integer agendamentoLocacaoCodigo,
                                                                      @PathVariable("vendaAvulsaCodigo") Integer vendaAvulsaCodigo) {
        try {
            locacaoService.sincronizarVendaAvulsa(agendamentoLocacaoCodigo, vendaAvulsaCodigo);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar a venda avulsa da locação", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

}
