package br.com.pacto.controller.json.wod;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.wod.FiltroNivelWodJSON;
import br.com.pacto.bean.wod.NivelWodTO;
import br.com.pacto.controller.json.benchmark.BenchmarkController;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.nivelwod.NivelWodService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by Denis Silva on 28/05/2024.
 */
@Controller
@RequestMapping("/psec/nivel-wod")
public class NivelWodController {

    private final NivelWodService nivelWodService;

    @Autowired
    public NivelWodController(NivelWodService nivelWodService){
        Assert.notNull(nivelWodService, "O serviço de tipos de wod não foi injetado corretamente");
        this.nivelWodService = nivelWodService;
    }




    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarNivelsWod(
            @RequestParam(value = "filters", required = false) JSONObject filtros,PaginadorDTO paginadorDTO)throws JSONException {
        try {
            FiltroNivelWodJSON filtroNivelWodJSON = new FiltroNivelWodJSON(filtros);

            return ResponseEntityFactory.ok(nivelWodService.listarNiveisWod(filtroNivelWodJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(BenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar Níveis wod", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarTiposWod(@PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(nivelWodService.buscarNivelWod(id));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar o Nível wod", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }



    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarNivelWod(@RequestBody NivelWodTO nivelWodTO) {
        try {
            return ResponseEntityFactory.ok(nivelWodService.cadastrarNivelWod(nivelWodTO));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar Nível do wod", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "reverter/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> reverterNivelWod(@PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(nivelWodService.reverterNivelWod(id));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar Nível do wod", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> auterarNivelWod(@RequestBody NivelWodTO nivelWodTO) {
        try {
            return ResponseEntityFactory.ok(nivelWodService.editarNivelWod(nivelWodTO));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar Nível do wod", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> deleteNivelWod(@PathVariable("id") Integer id) {
        try {
            nivelWodService.excluirNivelWod(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar deletar Nível do wod", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }
}
