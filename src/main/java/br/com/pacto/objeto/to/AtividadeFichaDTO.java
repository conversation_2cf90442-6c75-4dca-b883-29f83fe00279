package br.com.pacto.objeto.to;

import br.com.pacto.bean.atividade.MetodoExecucaoEnum;
import br.com.pacto.bean.serie.SerieEndpointTO;
import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by paulo 21/01/2020
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AtividadeFichaDTO {

    private Integer id;
    private Integer sequencia;
    private Integer atividadeId;
    private Integer esforco;
    private String metodoExecucao;
    private Integer fichaId;
    private List<SerieEndpointTO> series;
    private List<Integer> atividadesSequenciaSet;
    private List<Integer> atividadesIdSet;
    private String complementoNomeAtividade;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getSequencia() {
        return sequencia;
    }

    public void setSequencia(Integer sequencia) {
        this.sequencia = sequencia;
    }

    public Integer getAtividadeId() {
        return atividadeId;
    }

    public void setAtividadeId(Integer atividadeId) {
        this.atividadeId = atividadeId;
    }

    public Integer getEsforco() {
        return esforco;
    }

    public void setEsforco(Integer esforco) {
        this.esforco = esforco;
    }

    public String getMetodoExecucao() {
        return metodoExecucao;
    }

    public MetodoExecucaoEnum getMetodoExecucaoEnum() {

        if(UteisValidacao.emptyString(getMetodoExecucao())){
            return null;
        }
        return MetodoExecucaoEnum.valueOf(getMetodoExecucao());
    }


    public void setMetodoExecucao(String metodoExecucao) {
        this.metodoExecucao = metodoExecucao;
    }

    public Integer getFichaId() {
        return fichaId;
    }

    public void setFichaId(Integer fichaId) {
        this.fichaId = fichaId;
    }

    public List<SerieEndpointTO> getSeries() {
        if (series == null) {
            series = new ArrayList<>();
        }
        return series;
    }

    public void setSeries(List<SerieEndpointTO> series) {
        getSeries().addAll(series);
    }

    public List<Integer> getAtividadesSequenciaSet() {
        if (atividadesSequenciaSet == null) {
            atividadesSequenciaSet = new ArrayList<>();
        }
        return atividadesSequenciaSet;
    }

    public void setAtividadesSequenciaSet(List<Integer> atividadesSequenciaSet) {
        this.atividadesSequenciaSet = atividadesSequenciaSet;
    }

    public List<Integer> getAtividadesIdSet() {
        if (atividadesIdSet == null) {
            atividadesIdSet = new ArrayList<>();
        }
        return atividadesIdSet;
    }

    public void setAtividadesIdSet(List<Integer> atividadesIdSet) {
        this.atividadesIdSet = atividadesIdSet;
    }

    public String getComplementoNomeAtividade() {
        return complementoNomeAtividade;
    }

    public void setComplementoNomeAtividade(String complementoNomeAtividade) {
        this.complementoNomeAtividade = complementoNomeAtividade;
    }
}
