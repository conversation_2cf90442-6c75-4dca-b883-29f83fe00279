/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.pessoa;

import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import java.io.Serializable;
import java.util.*;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import br.com.pacto.objeto.Uteis;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class Pessoa implements Serializable {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String fotoKey;
    private String nome;
    @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, fetch = FetchType.EAGER, mappedBy = "pessoa", targetEntity = Email.class)
    @Fetch(FetchMode.SUBSELECT)
    private List<Email> emails = new ArrayList<>();
    @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, fetch = FetchType.EAGER, mappedBy = "pessoa", targetEntity = Telefone.class)
    @Fetch(FetchMode.SUBSELECT)
    private List<Telefone> telefones = new ArrayList<>();
    @Temporal(TemporalType.DATE)
    private Date dataNascimento;
    private String sexo;
    @Transient
    private Integer diaNasc;
    @Transient
    private Integer mesNasc;
    @Transient
    private Integer anoNasc;
    private Boolean parq;
    
    public void setarInteirosNascimento(){
        dataNascimento = dataNascimento == null ? Calendario.hoje() : dataNascimento;
        Calendar cal = Calendario.getInstance(dataNascimento);
        diaNasc = cal.get(Calendar.DAY_OF_MONTH);
        mesNasc = cal.get(Calendar.MONTH) + 1;
        anoNasc = cal.get(Calendar.YEAR);
    }
    
    public void setarDataNascimento(){
        Calendar cal = Calendario.getInstance(dataNascimento);
        mesNasc = mesNasc - 1;
        cal.set(Calendar.DAY_OF_MONTH, diaNasc);
        cal.set(Calendar.MONTH, mesNasc);
        cal.set(Calendar.YEAR, anoNasc);
        dataNascimento = cal.getTime();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getFotoKey() {
        if (fotoKey == null) {
            fotoKey = "";
        }
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<Email> getEmails() {
        if (emails == null) {
            emails = new ArrayList<>();
        }
        return emails;
    }

    public void setEmails(List<Email> emails) {
        this.emails = emails;
    }

    public List<Telefone> getTelefones() {
        if (telefones == null) {
            telefones = new ArrayList<>();
        }
        return telefones;
    }

    public void setTelefones(List<Telefone> telefones) {
        this.telefones = telefones;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public Integer getDiaNasc() {
        return diaNasc;
    }

    public void setDiaNasc(Integer diaNasc) {
        this.diaNasc = diaNasc;
    }

    public Integer getMesNasc() {
        return mesNasc;
    }

    public void setMesNasc(Integer mesNasc) {
        this.mesNasc = mesNasc;
    }

    public Integer getAnoNasc() {
        return anoNasc;
    }

    public void setAnoNasc(Integer anoNasc) {
        this.anoNasc = anoNasc;
    }
    
    public Boolean getParq() {
        return parq;
    }
    
    public void setParq(Boolean parq) {
        this.parq = parq;
    }

    public String getTelefones(boolean todos) {
        StringBuilder tels = new StringBuilder("");
        if (getTelefones().size() > 0) {
            int qtdTelefones = getTelefones().size();
            for (int i = 0; i < qtdTelefones; i++) {
                Object obj = getTelefones().get(i);
                if (obj instanceof Telefone) {
                    Telefone telefone = (Telefone) obj;
                    if (Uteis.validarTelefoneCelular(telefone.getTelefone()) || todos) {
                        if (tels.toString().isEmpty()) {
                            tels.append(telefone.getTelefone());
                        } else {
                            tels.append(";").append(telefone.getTelefone());
                        }
                    }
                }
            }
        }
        return tels.toString();
    }
    
}
