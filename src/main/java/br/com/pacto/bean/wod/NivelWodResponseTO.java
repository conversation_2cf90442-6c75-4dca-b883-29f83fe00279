package br.com.pacto.bean.wod;

import br.com.pacto.bean.nivelwod.NivelWod;
import br.com.pacto.bean.tipowod.TipoWod;
import br.com.pacto.enumerador.wod.CategoriaNivelWodEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 27/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NivelWodResponseTO {

    @ApiModelProperty(value = "Código identificador único do nível WOD", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do nível WOD", example = "Iniciante")
    private String nome;

    @ApiModelProperty(value = "Nome padrão do nível WOD definido pelo sistema", example = "Beginner")
    private String nomePadrao;

    @ApiModelProperty(value = "Categoria do nível WOD. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- Padrão\n" +
            "- Padrão (personalizado)\n" +
            "- Personalizado\n", example = "Padrão")
    private String categoria;

    public NivelWodResponseTO() {

    }

    public NivelWodResponseTO(NivelWod nivel) {
        this.id = nivel.getCodigo();
        this.nome = nivel.getNome();
        this.nomePadrao = nivel.getNomePadrao();
        this.categoria = CategoriaNivelWodEnum.getFromId(nivel.getCategoria()).getDescricao();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getNomePadrao() {
        return nomePadrao;
    }

    public void setNomePadrao(String nomePadrao) {
        this.nomePadrao = nomePadrao;
    }

    public String getCategoria() {
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }
}
