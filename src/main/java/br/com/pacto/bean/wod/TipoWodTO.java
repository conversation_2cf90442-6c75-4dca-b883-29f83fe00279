package br.com.pacto.bean.wod;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by ulisses on 27/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TipoWodTO {

    private Integer id;
    private String nome;
    private Boolean usarRanking;
    private Boolean profissionalEmPrimeiro;
    private String[] ordenamentosSelecionados;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getUsaRanking() {
        return usarRanking;
    }

    public void setUsarRanking(Boolean usarRanking) {
        this.usarRanking = usarRanking;
    }

    public Boolean getProfissionalEmPrimeiro() {
        return profissionalEmPrimeiro;
    }

    public void setProfissionalEmPrimeiro(Boolean profissionalEmPrimeiro) { this.profissionalEmPrimeiro = profissionalEmPrimeiro; }


    public String[] getOrdenamentosSelecionados() {
        return ordenamentosSelecionados;
    }

    public void setOrdenamentosSelecionados(String[] ordenamentosSelecionados) { this.ordenamentosSelecionados = ordenamentosSelecionados; }


}

