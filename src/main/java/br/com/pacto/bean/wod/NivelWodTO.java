package br.com.pacto.bean.wod;

import br.com.pacto.bean.nivelwod.NivelWod;
import br.com.pacto.enumerador.wod.CategoriaNivelWodEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by Denis
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NivelWodTO {

    private Integer id;
    private String nome;
    private String nomePadrao;

    public NivelWodTO() {

    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getNomePadrao() {
        return nomePadrao;
    }

    public void setNomePadrao(String nomePadrao) {
        this.nomePadrao = nomePadrao;
    }
}
