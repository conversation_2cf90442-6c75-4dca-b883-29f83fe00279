package br.com.pacto.bean.wod;

import br.com.pacto.bean.tipowod.TipoWod;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 27/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TipoWodResponseTO {

    private Integer id;
    private String nome;
    private Boolean usarRanking;
    private Boolean profissionalEmPrimeiro;
    private List<String> ordenamentosSelecionados = new ArrayList<>();

    public TipoWodResponseTO() {

    }

    public TipoWodResponseTO(TipoWod tipo) {
        this.id = tipo.getCodigo();
        this.nome = tipo.getNome();
        if (!StringUtils.isBlank(tipo.getCamposResultado())) {
            String[] resultados = tipo.getCamposResultado().split(",");

            if (resultados.length > 0) {
                this.usarRanking = true;
                for (String resultado : resultados) {
                    if (resultado.equals("nivelCrossfit")) {
                        this.profissionalEmPrimeiro = true;
                    } else {
                        ordenamentosSelecionados.add(resultado.toUpperCase());
                    }
                }
            }
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getUsarRanking() {
        return usarRanking;
    }

    public void setUsarRanking(Boolean usarRanking) {
        this.usarRanking = usarRanking;
    }

    public Boolean getProfissionalEmPrimeiro() {
        return profissionalEmPrimeiro;
    }

    public void setProfissionalEmPrimeiro(Boolean profissionalEmPrimeiro) {
        this.profissionalEmPrimeiro = profissionalEmPrimeiro;
    }

    public List<String> getOrdenamentosSelecionados() {
        return ordenamentosSelecionados;
    }

    public void setOrdenamentosSelecionados(List<String> ordenamentosSelecionados) {
        this.ordenamentosSelecionados = ordenamentosSelecionados;
    }
}
