/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.bi;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.serie.TreinoRealizado;
import br.com.pacto.controller.json.gestao.*;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Entidade contendo dados consolidados do dashboard de Business Intelligence, incluindo indicadores de alunos, treinos, agendamentos e avaliações.")
@Entity
public class DashboardBI implements Serializable {

    @ApiModelProperty(value = "Código identificador único do dashboard.", example = "1")
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @ApiModelProperty(value = "Mês de referência dos dados.", example = "12")
    private Integer mes;

    @ApiModelProperty(value = "Ano de referência dos dados.", example = "2023")
    private Integer ano;

    @ApiModelProperty(value = "Dia de referência dos dados.", example = "15")
    private Integer dia;

    @ApiModelProperty(value = "Dias de configuração para cálculo dos indicadores.", example = "30")
    private Integer diasConfiguracao;

    @ApiModelProperty(value = "Data e hora de início do processamento dos dados.")
    @Temporal(TemporalType.TIMESTAMP)
    private Date inicioProcessamento;

    @ApiModelProperty(value = "Data e hora de fim do processamento dos dados.")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fimProcessamento;

    @ApiModelProperty(value = "Percentual de alunos que utilizam o aplicativo móvel.", example = "67.5")
    private Double percUtilizamApp = 0.0;

    @ApiModelProperty(value = "Total de alunos que utilizam o aplicativo.", example = "85")
    private Integer utilizamApp = 0;

    @ApiModelProperty(value = "Total de alunos inativos que utilizam o aplicativo.", example = "12")
    private Integer inativosUtilizamApp = 0;

    @ApiModelProperty(value = "Total de alunos que não utilizam o aplicativo.", example = "35")
    private Integer naoUtilizamApp = 0;
    @ApiModelProperty(value = "Dados de Business Intelligence relacionados à carteira de alunos.")
    @Transient
    private BITreinoCarteiraDTO biCarteira;

    @ApiModelProperty(value = "Dados de Business Intelligence relacionados às avaliações de treino.")
    @Transient
    private BITreinoAvaliacaoTreinoDTO biTreinoAvaliacaoTreino;

    @ApiModelProperty(value = "Dados de Business Intelligence relacionados à agenda.")
    @Transient
    private BITreinoAgendaDTO biAgenda;

    @ApiModelProperty(value = "Dados de Business Intelligence relacionados aos treinamentos.")
    @Transient
    private BITreinoTreinamentoDTO biTreinamento;

    @ApiModelProperty(value = "Lista de clientes ativos que instalaram o aplicativo.")
    @Transient
    private List<ClienteSintetico> clientesAtivosInstaladosApp = new ArrayList<ClienteSintetico>();

    @ApiModelProperty(value = "Lista de clientes inativos que instalaram o aplicativo.")
    @Transient
    private List<ClienteSintetico> clientesInativosInstaladosApp = new ArrayList<ClienteSintetico>();

    @ApiModelProperty(value = "Lista de clientes que não instalaram o aplicativo.")
    @Transient
    private List<ClienteSintetico> clientesNaoInstaladosApp = new ArrayList<ClienteSintetico>();
    @ApiModelProperty(value = "Total geral de alunos.", example = "120")
    private Integer totalAlunos = 0;

    @ApiModelProperty(value = "Total de alunos com situação ativa.", example = "97")
    private Integer totalAlunosAtivos = 0;

    @ApiModelProperty(value = "Total de alunos com situação inativa.", example = "18")
    private Integer totalAlunosInativos = 0;

    @ApiModelProperty(value = "Total de alunos sem acompanhamento.", example = "12")
    private Integer totalAlunosSemAcompanhamento = 0;

    @ApiModelProperty(value = "Total de alunos visitantes.", example = "5")
    private Integer totalAlunosVisitantes = 0;

    @ApiModelProperty(value = "Total de alunos cancelados.", example = "3")
    private Integer totalAlunosCancelados = 0;

    @ApiModelProperty(value = "Total de alunos ativos sem treino ativo.", example = "12")
    private Integer totalAlunosAtivosForaTreino = 0;

    @ApiModelProperty(value = "Total de alunos com treino ativo.", example = "85")
    private Integer totalAlunosTreino = 0;

    @ApiModelProperty(value = "Total de treinos que precisam ser renovados.", example = "15")
    private Integer totalTreinosRenovar = 0;

    @ApiModelProperty(value = "Percentual de avaliações realizadas.", example = "85")
    private Integer percentualAvaliacoes = 0;

    @ApiModelProperty(value = "Percentual de renovações de contratos.", example = "75")
    private Integer percentualRenovacoes = 0;

    @ApiModelProperty(value = "Percentual de treinos em dia.", example = "78")
    private Integer percentualEmDia = 0;

    @ApiModelProperty(value = "Total de alunos com avaliações realizadas.", example = "82")
    private Integer totalAlunosAvaliacoes = 0;

    @ApiModelProperty(value = "Total de alunos sem treino ativo.", example = "12")
    private Integer totalAlunosSemTreino = 0;

    @ApiModelProperty(value = "Total de alunos sem avaliações.", example = "23")
    private Integer totalAlunosSemAvaliacoes = 0;

    @ApiModelProperty(value = "Total de alunos que não renovaram a carteira.", example = "8")
    private Integer totalNaoRenovaramCarteira = 0;

    @ApiModelProperty(value = "Total de renovações de carteira realizadas.", example = "45")
    private Integer totalRenovacoesCarteira = 0;

    @ApiModelProperty(value = "Total de alunos com contratos a vencer.", example = "18")
    private Integer totalAlunosAvencer = 0;

    @ApiModelProperty(value = "Total de novos alunos na carteira.", example = "12")
    private Integer totalNovosCarteira = 0;

    @ApiModelProperty(value = "Total de novos alunos que são realmente novos na carteira.", example = "8")
    private Integer totalNovosCarteiraNovos = 0;

    @ApiModelProperty(value = "Total de novos alunos que trocaram de professor.", example = "4")
    private Integer totalNovosCarteiraTrocaram = 0;

    @ApiModelProperty(value = "Total de alunos que trocaram de carteira.", example = "6")
    private Integer totalTrocaramCarteira = 0;

    @ApiModelProperty(value = "Tempo médio de permanência na carteira em dias.", example = "180")
    private Integer tempoMedioPermanenciaCarteira = 0;

    @ApiModelProperty(value = "Tempo médio de permanência no treino em dias.", example = "45")
    private Integer tempoMedioPermanenciaTreino = 0;

    @ApiModelProperty(value = "Total de treinos em dia.", example = "62")
    private Integer totalTreinosEmdia = 0;

    @ApiModelProperty(value = "Total de treinos vencidos.", example = "8")
    private Integer totalTreinosVencidos = 0;

    @ApiModelProperty(value = "Total de treinos a vencer.", example = "15")
    private Integer totalTreinosAvencer = 0;

    @ApiModelProperty(value = "Total de alunos que acessaram o sistema.", example = "78")
    private Integer totalAlunosAcessaram = 0;

    @ApiModelProperty(value = "Número de avaliações de treino realizadas.", example = "156")
    private Integer nrAvaliacoesTreino = 0;

    @ApiModelProperty(value = "Média do valor das avaliações de treino.", example = "4.2")
    private Double mediaValorAvaliacao = 0.0;

    @ApiModelProperty(value = "Número de avaliações com 5 estrelas.", example = "89")
    private Integer nr5estrelas = 0;

    @ApiModelProperty(value = "Número de avaliações com 4 estrelas.", example = "45")
    private Integer nr4estrelas = 0;

    @ApiModelProperty(value = "Número de avaliações com 3 estrelas.", example = "15")
    private Integer nr3estrelas = 0;

    @ApiModelProperty(value = "Número de avaliações com 2 estrelas.", example = "5")
    private Integer nr2estrelas = 0;

    @ApiModelProperty(value = "Número de avaliações com 1 estrela.", example = "2")
    private Integer nr1estrelas = 0;

    @ApiModelProperty(value = "Código do professor responsável.", example = "1")
    private Integer codigoProfessor;

    @ApiModelProperty(value = "Maior tempo de permanência registrado em dias.", example = "365")
    private Integer maiorPermanencia;

    @ApiModelProperty(value = "Nome do cliente com maior permanência.", example = "João Silva")
    private String nomeClienteMaiorPermanencia;

    @ApiModelProperty(value = "Matrícula do cliente com maior permanência.", example = "123456")
    private String matriculaClienteMaiorPermanencia;

    @ApiModelProperty(value = "Menor tempo de permanência registrado em dias.", example = "30")
    private Integer menorPermanencia;

    @ApiModelProperty(value = "Nome do cliente com menor permanência.", example = "Maria Santos")
    private String nomeClienteMenorPermanencia;

    @ApiModelProperty(value = "Matrícula do cliente com menor permanência.", example = "789012")
    private String matriculaClienteMenorPermanencia;

    @ApiModelProperty(value = "Total de agendamentos realizados.", example = "245")
    private Integer agendamentos = 0;

    @ApiModelProperty(value = "Total de agendamentos onde os alunos compareceram.", example = "198")
    private Integer compareceram = 0;

    @ApiModelProperty(value = "Total de faltas em agendamentos.", example = "32")
    private Integer faltaram = 0;

    @ApiModelProperty(value = "Total de agendamentos confirmados.", example = "220")
    private Integer confirmados = 0;

    @ApiModelProperty(value = "Total de agendamentos aguardando confirmação.", example = "25")
    private Integer aguardandoConfirmacao = 0;

    @ApiModelProperty(value = "Total de agendamentos cancelados.", example = "15")
    private Integer cancelaram = 0;

    @ApiModelProperty(value = "Total de professores ativos.", example = "12")
    private Integer professores = 0;

    @ApiModelProperty(value = "Total de horas de disponibilidade dos professores.", example = "480")
    private Integer horasDisponibilidade = 0;

    @ApiModelProperty(value = "Total de horas efetivamente atendidas.", example = "385")
    private Integer horasAtendimento = 0;

    @ApiModelProperty(value = "Percentual de ocupação das disponibilidades.", example = "80")
    private Integer ocupacao = 0;

    @ApiModelProperty(value = "Total de novos treinos criados.", example = "28")
    private Integer novosTreinos = 0;

    @ApiModelProperty(value = "Total de treinos revisados.", example = "15")
    private Integer treinosRevisados = 0;

    @ApiModelProperty(value = "Total de treinos renovados.", example = "42")
    private Integer treinosRenovados = 0;

    @ApiModelProperty(value = "Total de avaliações físicas realizadas.", example = "67")
    private Integer avaliacoesFisicas = 0;

    @ApiModelProperty(value = "Código da empresa.", example = "1")
    private Integer empresa = 0;

    @ApiModelProperty(value = "Tempo mediano de permanência na carteira em dias.", example = "165")
    private Integer tempoMedianaPermanenciaCarteira = 0;

    @ApiModelProperty(value = "Tempo mediano de permanência no treino em dias.", example = "40")
    private Integer tempoMedianaPermanenciaTreino = 0;
    @ApiModelProperty(value = "Lista de alunos sintéticos para análise.")
    @Transient
    private List<ClienteSintetico> alunos;

    @ApiModelProperty(value = "Lista de treinos realizados para análise.")
    @Transient
    private List<TreinoRealizado> treinos;

    @ApiModelProperty(value = "Maior valor encontrado para cálculos auxiliares.")
    @Transient
    private Integer maior;
    @Transient
    private Integer nrAvaliacoesAcompanhamento = 0;
    @Transient
    private Integer nr5EstrelasAcompanhamento = 0;
    @Transient
    private Integer nr4EstrelasAcompanhamento = 0;
    @Transient
    private Integer nr3EstrelasAcompanhamento = 0;
    @Transient
    private Integer nr2EstrelasAcompanhamento = 0;
    @Transient
    private Integer nr1EstrelaAcompanhamento = 0;

    public Integer getMaiorPermanencia() {
        return maiorPermanencia;
    }

    public void setMaiorPermanencia(Integer maiorPermanencia) {
        this.maiorPermanencia = maiorPermanencia;
    }

    public String getNomeClienteMaiorPermanencia() {
        return nomeClienteMaiorPermanencia;
    }

    public void setNomeClienteMaiorPermanencia(String nomeClienteMaiorPermanencia) {
        this.nomeClienteMaiorPermanencia = nomeClienteMaiorPermanencia;
    }

    public String getMatriculaClienteMaiorPermanencia() {
        return matriculaClienteMaiorPermanencia;
    }

    public void setMatriculaClienteMaiorPermanencia(String matriculaClienteMaiorPermanencia) {
        this.matriculaClienteMaiorPermanencia = matriculaClienteMaiorPermanencia;
    }

    public Integer getMenorPermanencia() {
        return menorPermanencia;
    }

    public void setMenorPermanencia(Integer menorPermanencia) {
        this.menorPermanencia = menorPermanencia;
    }

    public String getNomeClienteMenorPermanencia() {
        return nomeClienteMenorPermanencia;
    }

    public void setNomeClienteMenorPermanencia(String nomeClienteMenorPermanencia) {
        this.nomeClienteMenorPermanencia = nomeClienteMenorPermanencia;
    }

    public String getMatriculaClienteMenorPermanencia() {
        return matriculaClienteMenorPermanencia;
    }

    public void setMatriculaClienteMenorPermanencia(String matriculaClienteMenorPermanencia) {
        this.matriculaClienteMenorPermanencia = matriculaClienteMenorPermanencia;
    }

    public String getUltimaAtualizacao() {
        try {
            return Uteis.getDataAplicandoFormatacao(fimProcessamento, "dd/MM/yyyy HH:mm");
        } catch (Exception e) {
            return "";
        }
    }

    public Integer getMesPermanenciaTreino() {
        return tempoMedioPermanenciaTreino / 30;
    }

    public Integer getDiaPermanenciaTreino() {
        int dias = tempoMedioPermanenciaTreino - getMesPermanenciaTreino() * 30;
        return dias >= 0 ? dias : tempoMedioPermanenciaTreino;
    }

    public String getDescricaoMedianaPermanenciaTreino() {
        try {
            return "Mediana: " + getMesPermanenciaTreinoMediana() + " mês e "
                    + getDiaPermanenciaTreinoMediana() + " dia. Média: "
                    + getMesPermanenciaTreino() + " mês e " + getDiaPermanenciaTreino() + " dia.";
        } catch (Exception e) {
            return "";
        }
    }

    public Integer getMesPermanenciaTreinoMedia() {
        if (tempoMedioPermanenciaTreino == null) {
            tempoMedioPermanenciaTreino = 0;
        }
        return tempoMedioPermanenciaTreino / 30;
    }

    public Integer getDiaPermanenciaTreinoMedia() {
        if (tempoMedioPermanenciaTreino == null) {
            tempoMedioPermanenciaTreino = 0;
        }
        int dias = tempoMedioPermanenciaTreino - getMesPermanenciaTreinoMedia() * 30;
        return dias >= 0 ? dias : tempoMedioPermanenciaTreino;
    }

    public Integer getMesPermanenciaTreinoMediana() {
        if (tempoMedianaPermanenciaTreino == null) {
            tempoMedianaPermanenciaTreino = 0;
        }
        return tempoMedianaPermanenciaTreino / 30;
    }

    public Integer getDiaPermanenciaTreinoMediana() {
        if (tempoMedianaPermanenciaTreino == null) {
            tempoMedianaPermanenciaTreino = 0;
        }
        int dias = tempoMedianaPermanenciaTreino - getMesPermanenciaTreinoMediana() * 30;
        return dias >= 0 ? dias : tempoMedianaPermanenciaTreino;
    }

    public Integer getMesPermanenciaCarteira() {
        if(tempoMedioPermanenciaCarteira != null){
            return tempoMedioPermanenciaCarteira / 30;
        }else{
            return 0;
        }

    }

    public Integer getDiaPermanenciaCarteira() {
        if(tempoMedioPermanenciaCarteira != null){
            int dias = tempoMedioPermanenciaCarteira - getMesPermanenciaCarteira() * 30;
            return dias >= 0 ? dias : tempoMedioPermanenciaCarteira;
        }else{
            return 0;
        }

    }

    public List<String> getEstrelas() {
        List<String> stars = new ArrayList<String>();
        for (int i = 1; i <= 5; i++) {
            if (i > mediaValorAvaliacao && (i + 1) < mediaValorAvaliacao) {
                stars.add("icon-star-half-empty");
            } else if (i <= mediaValorAvaliacao) {
                stars.add("icon-star");
            } else if (i > mediaValorAvaliacao) {
                stars.add("icon-star-empty ");
            }
        }
        return stars;
    }

    public GestaoTreinoJSON toJSON(){

        if(UteisValidacao.emptyNumber(this.getCodigo())){
            return new GestaoTreinoJSON();
        }
        GestaoTreinoJSON obj = new GestaoTreinoJSON();
        obj.setAno(this.ano);
        obj.setCodigo(this.codigo);
        obj.setDia(this.dia);
        obj.setMes(this.mes);
        obj.setProfessor(this.codigoProfessor);
        obj.setEmpresa(this.empresa);
        obj.setTotalTreinosAvencer(this.totalTreinosAvencer);
        obj.setTotalTreinosVencidos(this.totalTreinosVencidos);
        obj.setTotalTreinosEmdia(this.totalTreinosEmdia);
        obj.setRenovados(this.getTotalRenovacoesCarteira());
        obj.setNaoRenovados(this.getTotalNaoRenovaramCarteira());
        obj.setMesAno((this.mes < 10 ? "0"+this.mes : this.mes) + "/"+ this.ano);
        return obj;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Date getFimProcessamento() {
        return fimProcessamento;
    }

    public Date getInicioProcessamento() {
        return inicioProcessamento;
    }

    public void setInicioProcessamento(Date inicioProcessamento) {
        this.inicioProcessamento = inicioProcessamento;
    }

    public void setFimProcessamento(Date fimProcessamento) {
        this.fimProcessamento = fimProcessamento;
    }

    public Integer getTotalAlunos() {
        return totalAlunos;
    }

    public void setTotalAlunos(Integer totalAlunos) {
        this.totalAlunos = totalAlunos;
    }

    public Integer getTotalAlunosAtivos() {
        return totalAlunosAtivos;
    }

    public void setTotalAlunosAtivos(Integer totalAlunosAtivos) {
        this.totalAlunosAtivos = totalAlunosAtivos;
    }

    public Integer getTotalAlunosInativos() {
        return totalAlunosInativos;
    }

    public void setTotalAlunosInativos(Integer totalAlunosInativos) {
        this.totalAlunosInativos = totalAlunosInativos;
    }

    public Integer getTotalAlunosSemAcompanhamento() { return totalAlunosSemAcompanhamento; }

    public void setTotalAlunosSemAcompanhamento(Integer totalAlunosSemAcompanhamento) { this.totalAlunosSemAcompanhamento = totalAlunosSemAcompanhamento; }

    public Integer getTotalAlunosCancelados() {
        return null == totalAlunosCancelados ? 0 : totalAlunosCancelados;
    }

    public void setTotalAlunosCancelados(Integer totalAlunosCancelados) {
        this.totalAlunosCancelados = totalAlunosCancelados;
    }

    public Integer getTotalAlunosAtivosForaTreino() {
        return totalAlunosAtivosForaTreino;
    }

    public void setTotalAlunosAtivosForaTreino(Integer totalAlunosAtivosForaTreino) {
        this.totalAlunosAtivosForaTreino = totalAlunosAtivosForaTreino;
    }

    public Integer getTotalAlunosTreino() {
        return totalAlunosTreino;
    }

    public void setTotalAlunosTreino(Integer totalAlunosTreino) {
        this.totalAlunosTreino = totalAlunosTreino;
    }

    public Integer getTotalTreinosRenovar() {
        return totalTreinosRenovar;
    }

    public void setTotalTreinosRenovar(Integer totalTreinosRenovar) {
        this.totalTreinosRenovar = totalTreinosRenovar;
    }

    public Integer getPercentualAvaliacoes() {
        return percentualAvaliacoes;
    }

    public void setPercentualAvaliacoes(Integer percentualAvaliacoes) {
        this.percentualAvaliacoes = percentualAvaliacoes;
    }

    public Integer getTotalAlunosAvaliacoes() {
        return totalAlunosAvaliacoes;
    }

    public void setTotalAlunosAvaliacoes(Integer totalAlunosAvaliacoes) {
        this.totalAlunosAvaliacoes = totalAlunosAvaliacoes;
    }

    public Integer getTotalAlunosSemAvaliacoes() {
        return totalAlunosSemAvaliacoes;
    }

    public void setTotalAlunosSemAvaliacoes(Integer totalAlunosSemAvaliacoes) {
        this.totalAlunosSemAvaliacoes = totalAlunosSemAvaliacoes;
    }

    public Integer getTotalNaoRenovaramCarteira() {
        return totalNaoRenovaramCarteira;
    }

    public void setTotalNaoRenovaramCarteira(Integer totalNaoRenovaramCarteira) {
        this.totalNaoRenovaramCarteira = totalNaoRenovaramCarteira;
    }

    public Integer getTotalRenovacoesCarteira() {
        return totalRenovacoesCarteira;
    }

    public void setTotalRenovacoesCarteira(Integer totalRenovacoesCarteira) {
        this.totalRenovacoesCarteira = totalRenovacoesCarteira;
    }

    public Integer getTotalAlunosAvencer() {
        return totalAlunosAvencer;
    }

    public void setTotalAlunosAvencer(Integer totalAlunosAvencer) {
        this.totalAlunosAvencer = totalAlunosAvencer;
    }

    public Integer getTotalNovosCarteira() {
        return totalNovosCarteira;
    }

    public void setTotalNovosCarteira(Integer totalNovosCarteira) {
        this.totalNovosCarteira = totalNovosCarteira;
    }

    public Integer getTotalTrocaramCarteira() {
        return totalTrocaramCarteira;
    }

    public void setTotalTrocaramCarteira(Integer totalTrocaramCarteira) {
        this.totalTrocaramCarteira = totalTrocaramCarteira;
    }

    public Integer getTempoMedioPermanenciaCarteira() {
        return tempoMedioPermanenciaCarteira;
    }

    public void setTempoMedioPermanenciaCarteira(Integer tempoMedioPermanenciaCarteira) {
        this.tempoMedioPermanenciaCarteira = tempoMedioPermanenciaCarteira;
    }

    public Integer getTempoMedioPermanenciaTreino() {
        return tempoMedioPermanenciaTreino;
    }

    public void setTempoMedioPermanenciaTreino(Integer tempoMedioPermanenciaTreino) {
        this.tempoMedioPermanenciaTreino = tempoMedioPermanenciaTreino;
    }

    public Integer getTotalTreinosEmdia() {
        return totalTreinosEmdia;
    }

    public void setTotalTreinosEmdia(Integer totalTreinosEmdia) {
        this.totalTreinosEmdia = totalTreinosEmdia;
    }

    public Integer getTotalTreinosAvencer() {
        return totalTreinosAvencer;
    }

    public void setTotalTreinosAvencer(Integer totalTreinosAvencer) {
        this.totalTreinosAvencer = totalTreinosAvencer;
    }

    public Integer getNrAvaliacoesTreino() {
        return nrAvaliacoesTreino;
    }

    public void setNrAvaliacoesTreino(Integer nrAvaliacoesTreino) {
        this.nrAvaliacoesTreino = nrAvaliacoesTreino;
    }

    public Double getMediaValorAvaliacao() {
        return mediaValorAvaliacao;
    }

    public void setMediaValorAvaliacao(Double mediaValorAvaliacao) {
        this.mediaValorAvaliacao = mediaValorAvaliacao;
    }

    public Integer getNr5estrelas() {
        return nr5estrelas;
    }

    public void setNr5estrelas(Integer nr5estrelas) {
        this.nr5estrelas = nr5estrelas;
    }

    public Integer getNr4estrelas() {
        return nr4estrelas;
    }

    public void setNr4estrelas(Integer nr4estrelas) {
        this.nr4estrelas = nr4estrelas;
    }

    public Integer getNr3estrelas() {
        return nr3estrelas;
    }

    public void setNr3estrelas(Integer nr3estrelas) {
        this.nr3estrelas = nr3estrelas;
    }

    public Integer getNr2estrelas() {
        return nr2estrelas;
    }

    public void setNr2estrelas(Integer nr2estrelas) {
        this.nr2estrelas = nr2estrelas;
    }

    public Integer getNr1estrelas() {
        return nr1estrelas;
    }

    public void setNr1estrelas(Integer nr1estrelas) {
        this.nr1estrelas = nr1estrelas;
    }

    public Integer getTotalAlunosSemTreino() {
        return totalAlunosSemTreino;
    }

    public void setTotalAlunosSemTreino(Integer totalAlunosSemTreino) {
        this.totalAlunosSemTreino = totalAlunosSemTreino;
    }

    public Integer getTotalTreinosVencidos() {
        return totalTreinosVencidos;
    }

    public void setTotalTreinosVencidos(Integer totalTreinosVencidos) {
        this.totalTreinosVencidos = totalTreinosVencidos;
    }

    public Integer getCodigoProfessor() {
        return codigoProfessor;
    }

    public void setCodigoProfessor(Integer codigoProfessor) {
        this.codigoProfessor = codigoProfessor;
    }

    public Integer getPercentualRenovacoes() {
        return percentualRenovacoes;
    }

    public void setPercentualRenovacoes(Integer percentualRenovacoes) {
        this.percentualRenovacoes = percentualRenovacoes;
    }

    public Integer getPercentualEmDia() {
        return percentualEmDia;
    }

    public void setPercentualEmDia(Integer percentualEmDia) {
        this.percentualEmDia = percentualEmDia;
    }

    public List<ClienteSintetico> getAlunos() {
        return alunos;
    }

    public void setAlunos(List<ClienteSintetico> alunos) {
        this.alunos = alunos;
    }

    public List<TreinoRealizado> getTreinos() {
        return treinos;
    }

    public void setTreinos(List<TreinoRealizado> treinos) {
        this.treinos = treinos;
    }

    public Integer getDia() {
        return dia;
    }

    public void setDia(Integer dia) {
        this.dia = dia;
    }

    public Integer getDiasConfiguracao() {
        return diasConfiguracao;
    }

    public void setDiasConfiguracao(Integer diasConfiguracao) {
        this.diasConfiguracao = diasConfiguracao;
    }

    public Integer getMaiorValorEstrela() {
        if (maior == null) {
            maior = 0;
            Integer[] totais = new Integer[]{getNr1estrelas(), getNr2estrelas(), getNr3estrelas(), getNr4estrelas(), getNr5estrelas()};
            for (Integer nr : totais) {
                maior = nr > maior ? nr : maior;
            }
        }
        return maior;
    }

    public Double getNrPercentual(Integer valor) {
        try {
            Double retorno = (valor.doubleValue() / getMaiorValorEstrela()) * 100;
            return retorno == 0.0 ? 0.1 : retorno;
        } catch (Exception e) {
            return 0.0;
        }
    }

    public String getPercentual5Estrelas() {
        return getNrPercentual(getNr5estrelas()) + "%";
    }

    public String getPercentual4Estrelas() {
        return getNrPercentual(getNr4estrelas()) + "%";
    }

    public String getPercentual3Estrelas() {
        return getNrPercentual(getNr3estrelas()) + "%";
    }

    public String getPercentual2Estrelas() {
        return getNrPercentual(getNr2estrelas()) + "%";
    }

    public String getPercentual1Estrelas() {
        return getNrPercentual(getNr1estrelas()) + "%";
    }

    public Integer getPercentualAtivosForaTreino() {
        try {
            return new Double(((getTotalAlunosAtivosForaTreino().doubleValue() / (getTotalAlunosAtivosForaTreino() + getTotalAlunosAtivos()))) * 100).intValue();
        } catch (Exception e) {
            return 0;
        }
    }

    public Integer getAgendamentos() {
        return agendamentos;
    }

    public void setAgendamentos(Integer agendamentos) {
        this.agendamentos = agendamentos;
    }

    public Integer getCompareceram() {
        return compareceram;
    }

    public void setCompareceram(Integer compareceram) {
        this.compareceram = compareceram;
    }

    public Integer getFaltaram() {
        return faltaram;
    }

    public void setFaltaram(Integer faltaram) {
        this.faltaram = faltaram;
    }

    public Integer getCancelaram() {
        return cancelaram;
    }

    public void setCancelaram(Integer cancelaram) {
        this.cancelaram = cancelaram;
    }

    public Integer getProfessores() {
        return professores;
    }

    public void setProfessores(Integer professores) {
        this.professores = professores;
    }

    public Integer getHorasDisponibilidade() {
        return horasDisponibilidade;
    }

    public void setHorasDisponibilidade(Integer horasDisponibilidade) {
        this.horasDisponibilidade = horasDisponibilidade;
    }

    public Integer getHorasAtendimento() {
        return horasAtendimento;
    }

    public void setHorasAtendimento(Integer horasAtendimento) {
        this.horasAtendimento = horasAtendimento;
    }

    public Integer getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(Integer ocupacao) {
        this.ocupacao = ocupacao;
    }

    public Integer getNovosTreinos() {
        return novosTreinos;
    }

    public void setNovosTreinos(Integer novosTreinos) {
        this.novosTreinos = novosTreinos;
    }

    public Integer getTreinosRevisados() {
        return treinosRevisados;
    }

    public void setTreinosRevisados(Integer treinosRevisados) {
        this.treinosRevisados = treinosRevisados;
    }

    public Integer getTreinosRenovados() {
        return treinosRenovados;
    }

    public void setTreinosRenovados(Integer treinosRenovados) {
        this.treinosRenovados = treinosRenovados;
    }

    public Integer getAvaliacoesFisicas() {
        return avaliacoesFisicas;
    }

    public void setAvaliacoesFisicas(Integer avaliacoesFisicas) {
        this.avaliacoesFisicas = avaliacoesFisicas;
    }

    public Integer getMaior() {
        return maior;
    }

    public void setMaior(Integer maior) {
        this.maior = maior;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getTempoMedianaPermanenciaCarteira() {
        return tempoMedianaPermanenciaCarteira;
    }

    public void setTempoMedianaPermanenciaCarteira(Integer tempoMedianaPermanenciaCarteira) {
        this.tempoMedianaPermanenciaCarteira = tempoMedianaPermanenciaCarteira;
    }

    public Integer getTempoMedianaPermanenciaTreino() {
        return tempoMedianaPermanenciaTreino;
    }

    public void setTempoMedianaPermanenciaTreino(Integer tempoMedianaPermanenciaTreino) {
        this.tempoMedianaPermanenciaTreino = tempoMedianaPermanenciaTreino;
    }

    public Integer getTotalNovosCarteiraNovos() {
        return totalNovosCarteiraNovos;
    }

    public void setTotalNovosCarteiraNovos(Integer totalNovosCarteiraNovos) {
        this.totalNovosCarteiraNovos = totalNovosCarteiraNovos;
    }

    public Integer getTotalNovosCarteiraTrocaram() {
        return totalNovosCarteiraTrocaram;
    }

    public void setTotalNovosCarteiraTrocaram(Integer totalNovosCarteiraTrocaram) {
        this.totalNovosCarteiraTrocaram = totalNovosCarteiraTrocaram;
    }

    public Double getValorIndicador(IndicadorDashboardEnum indicador) {
        Double retorno = 0.0;
        switch (indicador) {
            case ACESSOS:
                retorno = getTotalAlunosAcessaram().doubleValue();
                break;
            case TOTAL_ALUNOS:
                retorno = getTotalAlunos().doubleValue();
                break;
            case ATIVOS:
                retorno = getTotalAlunosAtivos().doubleValue();
                break;
            case INATIVOS:
                retorno = getTotalAlunosInativos().doubleValue();
                break;
            case ALUNOS_CANCELADOS:
                retorno = getTotalAlunosCancelados().doubleValue();
                break;
            case ATIVOS_COM_TREINO:
                retorno = getTotalAlunosTreino().doubleValue();
                break;
            case EM_DIA:
                retorno = getTotalTreinosEmdia().doubleValue();
                break;
            case VENCIDOS:
                retorno = getTotalTreinosVencidos().doubleValue();
                break;
            case TREINOS_A_VENCER:
                retorno = getTotalTreinosRenovar().doubleValue();
                break;
            case AGENDAMENTOS:
                retorno = getAgendamentos().doubleValue();
                break;
            case COMPARECERAM:
                retorno = getCompareceram().doubleValue();
                break;
            case FALTARAM:
                retorno = getFaltaram().doubleValue();
                break;
            case CONFIRMARAM:
                retorno = getConfirmados().doubleValue();
                break;
            case AG_CONFIRMACAO:
                retorno = getAguardandoConfirmacao().doubleValue();
                break;
            case CANCELARAM:
                retorno = getCancelaram().doubleValue();
                break;
            case RENOVARAM:
                retorno = getTotalRenovacoesCarteira().doubleValue();
                break;
            case NAO_RENOVARAM:
                retorno = getTotalNaoRenovaramCarteira().doubleValue();
                break;
            case ALUNOS_A_VENCER:
                retorno = getTotalAlunosAvencer().doubleValue();
                break;
            case NOVOS_CARTEIRA_NOVOS:
                retorno = getTotalNovosCarteiraNovos().doubleValue();
                break;
            case NOVOS_CARTEIRA_TROCARAM:
                retorno = getTotalNovosCarteiraTrocaram().doubleValue();
                break;
            case NOVOS_CARTEIRA:
                retorno = getTotalNovosCarteira().doubleValue();
                break;
            case TROCARAM_CARTEIRA:
                retorno = getTotalTrocaramCarteira().doubleValue();
                break;
            case BI_TEMPO_CARTEIRA:
                retorno = getTempoMedioPermanenciaCarteira()/30.0;
                break;
            case ATIVOS_SEM_TREINO:
                retorno = getTotalAlunosSemTreino().doubleValue();
                break;
            case PERC_TREINO_EM_DIA:
                retorno = getPercentualEmDia().doubleValue();
                break;
            case PERC_TREINO_VENCIDOS:
                retorno = ((getTotalTreinosEmdia()) + getTotalTreinosVencidos()) == 0
                        ? 0.0
                        : (getTotalTreinosVencidos() * 100.0) / (getTotalTreinosEmdia() + getTotalTreinosVencidos());
                break;
            case BI_TEMPO_PROGRAMA:
                retorno = getTempoMedioPermanenciaTreino()/30.0;
                break;
            case AVALIACOES:
                retorno = getNrAvaliacoesTreino().doubleValue();
                break;
            case ESTRELAS_1:
                retorno = getNr1estrelas().doubleValue();
                break;
            case ESTRELAS_2:
                retorno = getNr2estrelas().doubleValue();
                break;
            case ESTRELAS_3:
                retorno = getNr3estrelas().doubleValue();
                break;
            case ESTRELAS_4:
                retorno = getNr4estrelas().doubleValue();
                break;
            case ESTRELAS_5:
                retorno = getNr5estrelas().doubleValue();
                break;
            case COM_AVALIACAO_FISICA:
                retorno = getTotalAlunosAvaliacoes().doubleValue();
                break;
            case SEM_AVALIACAO:
                retorno = getTotalAlunosSemAvaliacoes().doubleValue();
                break;
            case PROFESSORES:
                retorno = getProfessores().doubleValue();
                break;
            case HRS_DISPONIBILIDADE:
                retorno = getHorasDisponibilidade().doubleValue();
                break;
            case HRS_ATENDIMENTO:
                retorno = getHorasAtendimento().doubleValue();
                break;
            case OCUPACAO:
                retorno = getOcupacao().doubleValue();
                break;
            case NOVOS_TREINOS:
                retorno = getNovosTreinos().doubleValue();
                break;
            case TREINOS_RENOVADOS:
                retorno = getTreinosRenovados().doubleValue();
                break;
            case TREINOS_REVISADOS:
                retorno = getTreinosRevisados().doubleValue();
                break;
            case AVALIACOES_FISICAS:
                retorno = getAvaliacoesFisicas().doubleValue();
                break;
            case PERCENTUAL_CRESCIMENTO_CARTEIRA:
                retorno = (getTotalAlunos() - getTotalNovosCarteira()) == 0 ?
                        0.0 :
                        (getTotalNovosCarteira()*100.0)/(getTotalAlunos() - getTotalNovosCarteira());
                break;
            case PERCENTUAL_RENOVACAO_CARTEIRA:
                retorno =  getPercentualRenovacoes().doubleValue();
                break;
            case ALUNOS_APP_INSTALADO:
                retorno = getUtilizamApp().doubleValue();
                break;
            case ALUNOS_APP_INSTALADO_ATIVOS:
                retorno = getUtilizamApp().doubleValue();
                break;
            case ALUNOS_APP_NAO_INSTALADO:
                retorno = getNaoUtilizamApp().doubleValue();
                break;
        }
        return retorno;
    }

    public Integer getTotalAlunosAcessaram() {
        return totalAlunosAcessaram;
    }

    public void setTotalAlunosAcessaram(Integer totalAlunosAcessaram) {
        this.totalAlunosAcessaram = totalAlunosAcessaram;
    }
    public String getOrdem(){
        return getAno()+"/"+getMes();
    }

    public Double getPercUtilizamApp() {
        return percUtilizamApp;
    }

    public int getPercUtilizamAppInt() {
        return percUtilizamApp == null ? 0 : percUtilizamApp.intValue();
    }

    public void setPercUtilizamApp(Double percUtilizamApp) {
        this.percUtilizamApp = percUtilizamApp;
    }

    public Integer getUtilizamApp() {
        return utilizamApp;
    }

    public void setUtilizamApp(Integer utilizamApp) {
        this.utilizamApp = utilizamApp;
    }

    public Integer getInativosUtilizamApp() {
        return inativosUtilizamApp;
    }

    public void setInativosUtilizamApp(Integer inativosUtilizamApp) {
        this.inativosUtilizamApp = inativosUtilizamApp;
    }

    public Integer getNaoUtilizamApp() {
        return naoUtilizamApp;
    }

    public void setNaoUtilizamApp(Integer naoUtilizamApp) {
        this.naoUtilizamApp = naoUtilizamApp;
    }

    public List<ClienteSintetico> getClientesAtivosInstaladosApp() {
        return clientesAtivosInstaladosApp;
    }

    public void setClientesAtivosInstaladosApp(List<ClienteSintetico> clientesAtivosInstaladosApp) {
        this.clientesAtivosInstaladosApp = clientesAtivosInstaladosApp;
    }

    public List<ClienteSintetico> getClientesInativosInstaladosApp() {
        return clientesInativosInstaladosApp;
    }

    public void setClientesInativosInstaladosApp(List<ClienteSintetico> clientesInativosInstaladosApp) {
        this.clientesInativosInstaladosApp = clientesInativosInstaladosApp;
    }

    public List<ClienteSintetico> getClientesNaoInstaladosApp() {
        return clientesNaoInstaladosApp;
    }

    public void setClientesNaoInstaladosApp(List<ClienteSintetico> clientesNaoInstaladosApp) {
        this.clientesNaoInstaladosApp = clientesNaoInstaladosApp;
    }

    public Integer getTotalAlunosVisitantes() {
        return totalAlunosVisitantes;
    }

    public void setTotalAlunosVisitantes(Integer totalAlunosVisitantes) {
        this.totalAlunosVisitantes = totalAlunosVisitantes;
    }

    public BITreinoCarteiraDTO getBiCarteira() {
        return biCarteira;
    }

    public void setBiCarteira(BITreinoCarteiraDTO biCarteira) {
        this.biCarteira = biCarteira;
    }

    public BITreinoAvaliacaoTreinoDTO getBiTreinoAvaliacaoTreino() { return biTreinoAvaliacaoTreino; }

    public void setBiTreinoAvaliacaoTreino(BITreinoAvaliacaoTreinoDTO biTreinoAvaliacaoTreinoDTO) {
        this.biTreinoAvaliacaoTreino = biTreinoAvaliacaoTreinoDTO;
    }

    public BITreinoAgendaDTO getBiAgenda() {
        return biAgenda;
    }

    public void setBiAgenda(BITreinoAgendaDTO biAgenda) {
        this.biAgenda = biAgenda;
    }

    public BITreinoTreinamentoDTO getBiTreinamento() {
        return biTreinamento;
    }

    public void setBiTreinamento(BITreinoTreinamentoDTO biTreinamento) {
        this.biTreinamento = biTreinamento;
    }

    public Integer getConfirmados() {
        if (confirmados == null) return 0;
        return confirmados;
    }

    public void setConfirmados(Integer confirmados) {
        this.confirmados = confirmados;
    }

    public Integer getAguardandoConfirmacao() {
        if (aguardandoConfirmacao == null) return 0;
        return aguardandoConfirmacao;
    }

    public void setAguardandoConfirmacao(Integer aguardandoConfirmacao) {
        this.aguardandoConfirmacao = aguardandoConfirmacao;
    }

    public Integer getNr1EstrelaAcompanhamento() {
        return nr1EstrelaAcompanhamento;
    }

    public void setNr1EstrelaAcompanhamento(Integer nr1EstrelaAcompanhamento) {
        this.nr1EstrelaAcompanhamento = nr1EstrelaAcompanhamento;
    }

    public Integer getNr2EstrelasAcompanhamento() {
        return nr2EstrelasAcompanhamento;
    }

    public void setNr2EstrelasAcompanhamento(Integer nr2EstrelasAcompanhamento) {
        this.nr2EstrelasAcompanhamento = nr2EstrelasAcompanhamento;
    }

    public Integer getNr3EstrelasAcompanhamento() {
        return nr3EstrelasAcompanhamento;
    }

    public void setNr3EstrelasAcompanhamento(Integer nr3EstrelasAcompanhamento) {
        this.nr3EstrelasAcompanhamento = nr3EstrelasAcompanhamento;
    }

    public Integer getNr4EstrelasAcompanhamento() {
        return nr4EstrelasAcompanhamento;
    }

    public void setNr4EstrelasAcompanhamento(Integer nr4EstrelasAcompanhamento) {
        this.nr4EstrelasAcompanhamento = nr4EstrelasAcompanhamento;
    }

    public Integer getNr5EstrelasAcompanhamento() {
        return nr5EstrelasAcompanhamento;
    }

    public void setNr5EstrelasAcompanhamento(Integer nr5EstrelasAcompanhamento) {
        this.nr5EstrelasAcompanhamento = nr5EstrelasAcompanhamento;
    }

    public Integer getNrAvaliacoesAcompanhamento() {
        return nrAvaliacoesAcompanhamento;
    }

    public void setNrAvaliacoesAcompanhamento(Integer nrAvaliacoesAcompanhamento) {
        this.nrAvaliacoesAcompanhamento = nrAvaliacoesAcompanhamento;
    }
}
