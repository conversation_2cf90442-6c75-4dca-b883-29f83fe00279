package br.com.pacto.bean.bi;

import br.com.pacto.bean.professor.ProfessorSintetic<PERSON>;


import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.List;

@Entity
public class ProfessorRanking implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private GeracaoRankingProfessores geracao;
    @ManyToOne
    private ProfessorSintetico professor;
    private Double pontos = 0.0;
    private Integer posicao;
    private String desempenho;
    private Integer cargaHoraria;
    @Transient
    private List<ProfessorRankingIndicador> indicadores;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ProfessorSintetico getProfessor() {
        return professor;
    }

    public void setProfessor(Professor<PERSON>int<PERSON><PERSON> professor) {
        this.professor = professor;
    }

    public Double getPontos() {
        return pontos;
    }

    public void setPontos(Double pontos) {
        this.pontos = pontos;
    }

    public Integer getPosicao() {
        return posicao;
    }

    public void setPosicao(Integer posicao) {
        this.posicao = posicao;
    }

    public String getDesempenho() {
        return desempenho;
    }

    public void setDesempenho(String desempenho) {
        this.desempenho = desempenho;
    }

    public GeracaoRankingProfessores getGeracao() {
        return geracao;
    }

    public void setGeracao(GeracaoRankingProfessores geracao) {
        this.geracao = geracao;
    }

    public List<ProfessorRankingIndicador> getIndicadores() {
        return indicadores;
    }

    public void setIndicadores(List<ProfessorRankingIndicador> indicadores) {
        this.indicadores = indicadores;
    }

    public Integer getCargaHoraria() { return cargaHoraria; }

    public void setCargaHoraria(Integer cargaHoraria) { this.cargaHoraria = cargaHoraria; }
}
