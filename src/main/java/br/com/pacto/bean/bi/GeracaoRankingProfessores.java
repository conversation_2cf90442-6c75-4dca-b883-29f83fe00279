package br.com.pacto.bean.bi;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Entity
public class GeracaoRankingProfessores implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer empresa = 0;
    @Temporal(TemporalType.TIMESTAMP)
    private Date inicio;
    @Temporal(TemporalType.TIMESTAMP)
    private Date fim;
    @Temporal(TemporalType.TIMESTAMP)
    private Date inicioProcessamento;
    @Temporal(TemporalType.TIMESTAMP)
    private Date fimProcessamento;
    private Integer responsavelLancamento_codigo;
    @Transient
    private List<ProfessorRanking> professores;

    public GeracaoRankingProfessores() {
    }

    public GeracaoRankingProfessores(Integer codigo, Date fimProcessamento, Integer empresa) {
        this.codigo = codigo;
        this.empresa = empresa;
        this.fimProcessamento = fimProcessamento;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public Date getInicioProcessamento() {
        return inicioProcessamento;
    }

    public void setInicioProcessamento(Date inicioProcessamento) {
        this.inicioProcessamento = inicioProcessamento;
    }

    public Date getFimProcessamento() {
        return fimProcessamento;
    }

    public void setFimProcessamento(Date fimProcessamento) {
        this.fimProcessamento = fimProcessamento;
    }

    public Integer getResponsavelLancamento_codigo() {
        return responsavelLancamento_codigo;
    }

    public void setResponsavelLancamento_codigo(Integer responsavelLancamento) {
        this.responsavelLancamento_codigo = responsavelLancamento;
    }

    public List<ProfessorRanking> getProfessores() {
        return professores;
    }

    public void setProfessores(List<ProfessorRanking> professores) {
        this.professores = professores;
    }
}
