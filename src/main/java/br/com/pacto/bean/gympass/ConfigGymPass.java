/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gympass;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.gympass.ConfigGymPassDTO;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 22/03/2020
 */
@Entity
public class ConfigGymPass implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private Empresa empresa;
    private String nome;
    private String codigoGymPass;
    @Column(columnDefinition = "boolean DEFAULT false")
    private boolean usarGymPassBooking;
    private Integer usuarioLancou_codigo;
    private Boolean ativo;
    private Boolean permitirWod;
    private Date dataLancamento;
    private Integer limiteDeAcessosPorDia;
    private Integer limiteDeAulasPorDia;

    public ConfigGymPass() {
    }

    public ConfigGymPass(ConfigGymPassDTO dto, Empresa empresa, Usuario usuario, Date dataLancamento) {
        this.codigo = dto.getCodigo();
        this.empresa = empresa;
        this.nome = empresa.getNome();
        this.codigoGymPass = dto.getCodigoGymPass();
        this.permitirWod = dto.isPermitirWod();
        this.usarGymPassBooking = dto.isUsarGymPassBooking();
        this.limiteDeAcessosPorDia = dto.getLimiteDeAcessosPorDia();
        this.limiteDeAulasPorDia = dto.getLimiteDeAulasPorDia();
        this.usuarioLancou_codigo = usuario.getCodigo();
        this.dataLancamento = dataLancamento;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCodigoGymPass() {
        return codigoGymPass;
    }

    public void setCodigoGymPass(String codigoGymPass) {
        this.codigoGymPass = codigoGymPass;
    }

    public boolean isUsarGymPassBooking() {
        return usarGymPassBooking;
    }

    public void setUsarGymPassBooking(boolean usarGymPassBooking) {
        this.usarGymPassBooking = usarGymPassBooking;
    }

    public Integer getLimiteDeAcessosPorDia() {
        return limiteDeAcessosPorDia;
    }

    public void setLimiteDeAcessosPorDia(Integer limiteDeAcessosPorDia) {
        this.limiteDeAcessosPorDia = limiteDeAcessosPorDia;
    }

    public Integer getLimiteDeAulasPorDia() {
        return limiteDeAulasPorDia;
    }

    public void setLimiteDeAulasPorDia(Integer limiteDeAulasPorDia) {
        this.limiteDeAulasPorDia = limiteDeAulasPorDia;
    }

    public Integer getUsuarioLancou_codigo() {
        return usuarioLancou_codigo;
    }

    public void setUsuarioLancou_codigo(Integer usuarioLancou) {
        this.usuarioLancou_codigo = usuarioLancou;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Boolean getPermitirWod() {
        if(permitirWod == null){
            permitirWod = false;
        }
        return permitirWod;
    }

    public void setPermitirWod(Boolean permitirWod) {
        this.permitirWod = permitirWod;
    }
}
