
 /*
  * To change this template, choose Too<PERSON> | Templates
  * and open the template in the editor.
  */
 package br.com.pacto.bean.cliente;

 import io.swagger.annotations.ApiModel;
 import io.swagger.annotations.ApiModelProperty;
 import br.com.pacto.bean.Agendamento;
 import br.com.pacto.bean.anamnese.RespostaCliente;
 import br.com.pacto.bean.aula.AulaAluno;
 import br.com.pacto.bean.aula.AulaDiaExcecao;
 import br.com.pacto.bean.avaliacao.*;
 import br.com.pacto.bean.empresa.Empresa;
 import br.com.pacto.bean.gestaopersonal.AlunoAulaPersonal;
 import br.com.pacto.bean.nivel.Nivel;
 import br.com.pacto.bean.notificacao.Notificacao;
 import br.com.pacto.bean.pessoa.Email;
 import br.com.pacto.bean.pessoa.Pessoa;
 import br.com.pacto.bean.pessoa.Telefone;
 import br.com.pacto.bean.professor.Professor<PERSON><PERSON><PERSON><PERSON>;
 import br.com.pacto.bean.programa.HistoricoRevisaoProgramaTreino;
 import br.com.pacto.bean.programa.ProgramaTreino;
 import br.com.pacto.bean.usuario.Usuario;
 import br.com.pacto.objeto.Calendario;
 import br.com.pacto.objeto.Uteis;
 import br.com.pacto.util.UteisValidacao;
 import org.json.JSONException;
 import org.json.JSONObject;
 import com.fasterxml.jackson.annotation.JsonIgnore;
 import java.io.Serializable;
 import java.text.ParseException;
 import java.util.ArrayList;
 import java.util.Arrays;
 import java.util.Date;
 import java.util.List;
 import java.util.logging.Level;
 import java.util.logging.Logger;
 import javax.persistence.*;

 import org.hibernate.annotations.Type;

 /**
  *
  * <AUTHOR>
  */
 @Entity
 @Table(uniqueConstraints = {
         @UniqueConstraint(name = "clientesintetico_codigopessoa_key", columnNames = {"codigoPessoa", "matricula"})})
 @ApiModel(description = "Entidade que representa um cliente/aluno do sistema, contendo informações pessoais, dados de matrícula, histórico de treinos, avaliações físicas e relacionamentos com professores e programas de treinamento.")
 public class ClienteSintetico implements Serializable {

     @Id
     @GeneratedValue(strategy = GenerationType.IDENTITY)
     @ApiModelProperty(value = "Código identificador único do cliente no banco de dados.", example = "12345")
     private Integer codigo;

     @ApiModelProperty(value = "Nome completo do cliente/aluno.", example = "João Silva Santos")
     private String nome;

     @ApiModelProperty(value = "Nome formatado para consultas e pesquisas (geralmente em maiúsculas).", example = "JOAO SILVA SANTOS")
     private String nomeConsulta;

     @ApiModelProperty(value = "Indica se o cliente está ativo no sistema.", example = "true")
     private Boolean ativo = true;

     @ApiModelProperty(value = "Sexo do cliente (M para Masculino, F para Feminino).", example = "M")
     private String sexo;

     @Temporal(TemporalType.TIMESTAMP)
     @ApiModelProperty(value = "Data e hora de cadastro do cliente no sistema.", example = "2024-01-15T10:30:00Z")
     private Date dia;

     @Temporal(TemporalType.TIMESTAMP)
     @ApiModelProperty(value = "Data e hora de término do último programa de treino do cliente.", example = "2024-06-15T18:00:00Z")
     private Date terminoUltimoPrograma;

     @ApiModelProperty(value = "Código do cliente no sistema legado ou externo.", example = "98765")
     private Integer codigoCliente;

     @ApiModelProperty(value = "Código da pessoa física associada ao cliente.", example = "54321")
     private Integer codigoPessoa;

     @OneToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE})
     @ApiModelProperty(value = "Referência aos dados pessoais completos do cliente (endereço, telefones, emails).")
     private Pessoa pessoa;

     @ApiModelProperty(value = "Número da matrícula do cliente na academia.", example = "2024001")
     private Integer matricula;

     @Temporal(TemporalType.DATE)
     @ApiModelProperty(value = "Data de nascimento do cliente.", example = "1990-05-15")
     private Date dataNascimento;

     @ApiModelProperty(value = "Idade atual do cliente em anos.", example = "34")
     private Integer idade;

     @ApiModelProperty(value = "Profissão ou ocupação do cliente.", example = "Engenheiro de Software")
     private String profissao;
     @Column(columnDefinition = "text", length = 9999)
     @ApiModelProperty(value = "Lista de colaboradores (professores/instrutores) associados ao cliente, armazenada como texto.", example = "João Silva, Maria Santos")
     private String colaboradores;

     @ApiModelProperty(value = "Código do contrato ativo do cliente.", example = "789123")
     private Integer codigoContrato;

     @ApiModelProperty(value = "Situação atual do cliente no sistema (AT=Ativo, IN=Inativo, etc.).", example = "AT")
     private String situacao;
     private Integer duracaoContratoMeses;
     private String mnemonicoDoContrato;
     private String nomeplano;
     private Integer fcRepouso;
     private Integer fcMaxima;
     private Double valorfaturadocontrato;
     private Double valorPagoContrato;
     private Double valorParcAbertoContrato;
     private Double saldoContaCorrenteCliente;
     @Temporal(TemporalType.DATE)
     private Date dataVigenciaDe;
     @Temporal(TemporalType.DATE)
     private Date dataVigenciaAte;
     @Temporal(TemporalType.DATE)
     private Date dataVigenciaAteAjustada;
     @Temporal(TemporalType.TIMESTAMP)
     private Date dataLancamentoContrato;
     @Temporal(TemporalType.DATE)
     private Date dataRenovacaoContrato;
     @Temporal(TemporalType.DATE)
     private Date dataRematriculaContrato;
     @Temporal(TemporalType.TIMESTAMP)
     private Date dataUltimoBV;
     @Temporal(TemporalType.DATE)
     private Date dataMatricula;
     @Temporal(TemporalType.DATE)
     private Date dataUltimarematricula;
     private Integer diasAssiduidadeUltRematriculaAteHoje;
     private Integer diasFaltaSemAcesso;
     @Temporal(TemporalType.TIMESTAMP)
     private Date dataUltimoacesso;
     private String faseAtualCRM;
     @Temporal(TemporalType.TIMESTAMP)
     private Date dataUltimoContatoCRM;
     private String responsavelUltimoContatoCRM;
     private Integer codigoUltimoContatoCRM;
     private String situacaoContrato;
     private String tipoPeriodoAcesso;
     @Temporal(TemporalType.DATE)
     private Date dataInicioPeriodoAcesso;
     @Temporal(TemporalType.DATE)
     private Date dataFimPeriodoAcesso;
     private Integer diasAcessoSemanaPassada;
     private Integer diasAcessoSemana2;
     private Integer diasAcessoSemana3;
     private Integer diasAcessoSemana4;
     private Integer vezesPorSemana;
     private Integer diasAcessoUltimoMes;
     private Integer diasAcessoMes2;
     private Integer diasAcessoMes3;
     private Integer diasAcessoMes4;
     private Integer mediaDiasAcesso4Meses;
     @ApiModelProperty(value = "Código identificador da empresa à qual o cliente está vinculado.", example = "1")
     private Integer empresa;
     @Column(columnDefinition = "text")
     @ApiModelProperty(value = "Endereço de email principal do cliente.", example = "<EMAIL>")
     private String email;

     @ApiModelProperty(value = "Lista de telefones do cliente concatenados em string.", example = "(11) 99999-9999, (11) 3333-4444")
     private String telefones;

     @ApiModelProperty(value = "Descrição da duração do contrato ou plano do cliente.", example = "Mensal - 12 meses")
     private String descricaoDuracao;

     @ApiModelProperty(value = "Peso de risco do cliente baseado em avaliações médicas (0-10).", example = "3")
     private Integer pesoRisco;

     @ManyToOne
     @ApiModelProperty(value = "Referência ao grupo de clientes ao qual este cliente pertence (ex: VIP, Premium, etc.).")
     private GrupoCliente grupo;

     @Transient
     @ApiModelProperty(value = "URL da foto de perfil do cliente (campo transiente para exibição).", example = "https://exemplo.com/fotos/cliente123.jpg")
     private String urlFoto;

     @Transient
     @ApiModelProperty(value = "Código do colaborador que atua como professor principal do cliente (campo transiente).", example = "456")
     private Integer codigoColaboradorProfessor;

     @ManyToOne
     @ApiModelProperty(value = "Referência ao professor principal responsável pelo cliente.")
     private ProfessorSintetico professorSintetico;

     @ManyToOne
     @ApiModelProperty(value = "Referência ao nível de treinamento atual do cliente (Iniciante, Intermediário, Avançado).")
     private Nivel nivelAluno;
     private Integer nrTreinosRealizados;
     private Integer nrTreinosPrevistos;
     private String situacaoMatriculaContrato = "";
     private Integer versao = 0;
     private String codigoAcesso;
     @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, fetch = FetchType.LAZY, mappedBy = "cliente", targetEntity = ClienteBadge.class)
     private List<ClienteBadge> badges = new ArrayList<ClienteBadge>();
     @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, fetch = FetchType.LAZY, mappedBy = "cliente", targetEntity = ClienteBadge.class)
     private List<HistoricoRevisaoProgramaTreino> revisoes = new ArrayList<HistoricoRevisaoProgramaTreino>();
     private String objetivos;
     private String dadosAvaliacao;
     @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, fetch = FetchType.LAZY, mappedBy = "cliente", targetEntity = ClienteObservacao.class)
     private List<ClienteObservacao> observacoes = new ArrayList<ClienteObservacao>();
     @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, fetch = FetchType.LAZY, mappedBy = "cliente", targetEntity = AvaliacaoFisica.class)
     private List<AvaliacaoFisica> avaliacaoFisicas = new ArrayList<AvaliacaoFisica>();
     @OneToMany(cascade = CascadeType.REMOVE, mappedBy = "cliente")
     private List<Agendamento> agendamentos;

     @OneToMany(cascade = CascadeType.REMOVE, mappedBy = "cliente")
     private List<ProgramaTreino> programaTreinos;
     @OneToMany(cascade = CascadeType.REMOVE, mappedBy = "cliente")
     private List<PesoOsseo> pesoOsseos;
     @OneToMany(cascade = CascadeType.REMOVE, mappedBy = "cliente")
     private List<ItemAvaliacaoFisica> itemAvaliacaoFisicas;
     @OneToMany(cascade = CascadeType.REMOVE, mappedBy = "cliente")
     private List<RespostaCliente> respostaClientes;
     @OneToMany(cascade = CascadeType.REMOVE, mappedBy = "cliente")
     private List<Ventilometria> ventilometrias;
     @OneToMany(cascade = CascadeType.REMOVE, mappedBy = "cliente")
     private List<ClienteAcompanhamento> clienteAcompanhamentos;
     @OneToMany(mappedBy = "cliente", cascade = CascadeType.REMOVE)
     private List<Usuario> usuarios;
     @OneToMany(mappedBy = "cliente", cascade = CascadeType.REMOVE)
     private List<AlunoAulaPersonal> alunosAulaPersonal;
     @OneToMany(mappedBy = "cliente", cascade = CascadeType.REMOVE)
     private List<AulaAluno> aulasAlunos;
     @OneToMany(mappedBy = "cliente", cascade = CascadeType.REMOVE)
     private List<AulaDiaExcecao> aulasDiasExcecoes;
     @Transient
     private String nivel;
     @Transient
     private List<Notificacao> notificacoes;
     @Transient
     private String css = "";
     @Transient
     private ProgramaTreino programaVigente = null;
     @Transient
     private Empresa empresaTreino;
     @ApiModelProperty(value = "Número de aulas experimentais realizadas pelo cliente.", example = "2")
     private Integer nrAulasExperimentais;

     @ApiModelProperty(value = "Modalidades praticadas pelo cliente na academia (códigos separados por pipe).", example = "1|3|5")
     private String modalidades;

     @ApiModelProperty(value = "Indica se o cliente foi aprovado no questionário PAR-Q (Physical Activity Readiness Questionnaire).", example = "true")
     private Boolean parq;

     @ApiModelProperty(value = "Indica se o cliente pratica modalidade CrossFit.", example = "false")
     private Boolean crossfit;

     @ApiModelProperty(value = "Saldo atual de créditos de treino do cliente.", example = "10")
     private Integer saldoCreditoTreino = 0;

     @ApiModelProperty(value = "Total de créditos de treino adquiridos pelo cliente.", example = "50")
     private Integer totalCreditoTreino = 0;

     @Lob
     @Type(type="org.hibernate.type.StringClobType")
     @Column(columnDefinition = "text", length = 9999)
     @ApiModelProperty(value = "Descrições detalhadas das modalidades praticadas pelo cliente.", example = "Musculação para hipertrofia, Pilates para flexibilidade")
     private String descricoesModalidades = "";

     @ApiModelProperty(value = "Situação da operação do contrato do cliente.", example = "ATIVO")
     private String situacaoContratoOperacao = "";
     @Transient
     private String key;
     @Transient
     private ItemAvaliacaoFisica fc;
     @Transient
     private ItemAvaliacaoFisica pressao;
     @Transient
     private ItemAvaliacaoFisica pressaoDiastolica;
     @Transient
     private ItemAvaliacaoFisica pressaoSistolica;
     @Transient
     private ItemAvaliacaoFisica ventilometria;
     @Transient
     private Double peso;
     @Transient
     private Double altura;
     @Transient
     private Double percentualGordura;
     @Transient
     private Date dataAvaliacao;
     @Transient
     private Date dataProximaAvaliacao;
     @Transient
     private ItemAvaliacaoFisica parqP;
     @Transient
     private ResultadoResistenciaEnum resistenciaBraco;
     @Transient
     private ResultadoResistenciaEnum resistenciaAbdomen;
     @Transient
     private List<String> objetivosLista;
     @Transient
     private Integer flexaoAbdomen;
     @Transient
     private Integer flexaoBraco;
     @Temporal(TemporalType.TIMESTAMP)
     private Date dataAtualizacaoFoto;
     @Transient
     private Integer atletaBioimpedancia;
     @Transient
     private Integer nivelAtividadeBioimpedancia;
     private String fotoKeyApp;

     private Integer frequenciaSemanal;
    private String codigoExterno; //utilizado quando aluno foi importado de outro sistema
     @Temporal(TemporalType.DATE)
     private Date dataCadastro;
     private Boolean existeParcVencidaContrato = false;
     private Boolean empresaUsaFreePass = false;
     @Temporal(TemporalType.DATE)
     private Date ultimaVisita;
     private String cargo;
     private Boolean freePass = false;
     private String endereco;
     private String cidade;
     private String bairro;
     private String estadoCivil;
     private String CPF;
     private String RG;
     private String UF;
     private Double pesoInicio;
     private Double pesoAtual;
     private Double percentualGorduraInicio;
     private Double percentualGorduraAtual;
     private Double massaMagraInicio;
     private Double massaMagraAtual;
     private String gympassUniqueToken;
     @Temporal(TemporalType.DATE)
     private Date freePassInicio;
     @Temporal(TemporalType.DATE)
     private Date freePassFim;
     @Enumerated(EnumType.STRING)
     private OrigemCliente origemCliente;

     public String getModalidades() {
         if (modalidades == null) {
             modalidades = "";
         }
         return modalidades;
     }

     public void setModalidades(String modalidades) {
         this.modalidades = modalidades;
     }

     public ClienteSintetico() {
     }

     public ClienteSintetico(Integer codigo, String nome) {
         this.codigo = codigo;
         this.nome = nome;
     }

     public ClienteSintetico(Integer codigo, String nome, Integer codigoPessoa, Nivel nivel, ProfessorSintetico professor) {
         this.codigo = codigo;
         this.nome = nome;
         this.codigoPessoa = codigoPessoa;
         this.nivelAluno = nivel;
         this.professorSintetico = professor;
     }

     private static Date getDate(final String data) {
         try {
             if (data == null || data.equals("null") || data.equals("")) {
                 return null;
             }
             return Calendario.getDate(data.length() <= 10 ? "yyyy-MM-dd" : "yyyy-MM-dd HH:mm:ss", data);
         } catch (ParseException ex) {
             Logger.getLogger(ClienteSintetico.class.getName()).log(Level.SEVERE, null, ex);
             return null;
         }
     }

     private String getString(final JSONObject j, final String attr) throws JSONException {
         return j.isNull(attr) || j.getJSONArray(attr).isNull(0)
                 ? null
                 : j.getJSONArray(attr).getString(0);
     }

     private int getInt(final JSONObject j, final String attr) throws JSONException {
         return j.isNull(attr) || j.getJSONArray(attr).isNull(0)
                 ? null
                 : j.getJSONArray(attr).getInt(0);
     }

     private double getDouble(final JSONObject j, final String attr) throws JSONException {
         return j.isNull(attr) || j.getJSONArray(attr).isNull(0)
                 ? null
                 : j.getJSONArray(attr).getDouble(0);
     }

     private boolean getBoolean(final JSONObject j, final String attr) throws JSONException {
         try {
             return j.isNull(attr) ? null : j.getJSONArray(attr).getBoolean(0);
         } catch (Exception e) {
             return false;
         }
     }

     public ClienteSintetico(Integer codigo) {
         this.codigo = codigo;
     }

     public ClienteSintetico(JSONObject o) throws JSONException {
         this.nome = getString(o, "nomeCliente");
         this.sexo = getString(o, "sexoCliente");
         this.dia = getDate(getString(o, "dia"));
         this.codigoCliente = getInt(o, "codigoCliente");
         this.codigoPessoa = getInt(o, "codigoPessoa");
         this.matricula = getInt(o, "matricula");
         this.dataNascimento = getDate(getString(o, "dataNascimento"));
         this.idade = getInt(o, "idade");
         this.profissao = getString(o, "profissao");
         this.colaboradores = getString(o, "colaboradores");
         this.codigoContrato = getInt(o, "codigoContrato");
         this.situacao = getString(o, "situacao");
         this.duracaoContratoMeses = getInt(o, "duracaoContratoMeses");
         this.mnemonicoDoContrato = getString(o, "mnemonicoContrato");
         this.nomeplano = getString(o, "nomePlano");
         this.valorfaturadocontrato = getDouble(o, "valorFaturadoContrato");
         this.valorPagoContrato = getDouble(o, "valorPagoContrato");
         this.valorParcAbertoContrato = getDouble(o, "valorParcAbertoContrato");
         this.saldoContaCorrenteCliente = getDouble(o, "saldoContaCorrenteCliente");
         this.dataVigenciaDe = getDate(getString(o, "dataVigenciaDe"));
         this.dataVigenciaAte = getDate(getString(o, "dataVigenciaAte"));
         this.dataVigenciaAteAjustada = getDate(getString(o, "dataVigenciaAteAjustada"));
         this.dataLancamentoContrato = getDate(getString(o, "dataLancamentoContrato"));
         this.dataRenovacaoContrato = getDate(getString(o, "dataRenovacaoContrato"));
         this.dataRematriculaContrato = getDate(getString(o, "dataRematriculaContrato"));
         this.dataUltimoBV = getDate(getString(o, "dataUltimoBV"));
         this.dataMatricula = getDate(getString(o, "dataMatricula"));
         this.dataUltimarematricula = getDate(getString(o, "dataUltimaMatricula"));
         this.diasAssiduidadeUltRematriculaAteHoje = getInt(o, "diasAssiduidadeUltRematriculaAteHoje");
         this.dataUltimoacesso = getDate(getString(o, "dataUltimoAcesso"));
         this.faseAtualCRM = getString(o, "faseAtualCRM");
         this.dataUltimoContatoCRM = getDate(getString(o, "dataUltimoContatoCRM"));
         this.responsavelUltimoContatoCRM = getString(o, "responsavelUltimoContatoCRM");
         this.codigoUltimoContatoCRM = getInt(o, "codigoUltimoContatoCRM");
         this.situacaoContrato = getString(o, "situacaoContrato");
         this.tipoPeriodoAcesso = getString(o, "tipoPeriodoAcesso");
         this.dataInicioPeriodoAcesso = getDate(getString(o, "dataInicioPeriodoAcesso"));
         this.dataFimPeriodoAcesso = getDate(getString(o, "dataFimPeriodoAcesso"));
         this.diasAcessoSemanaPassada = getInt(o, "diasAcessoSemanaPassada");
         this.diasAcessoSemana2 = getInt(o, "diasAcessoSemana2");
         this.diasAcessoSemana3 = getInt(o, "diasAcessoSemana3");
         this.diasAcessoSemana4 = getInt(o, "diasAcessoSemana4");
         this.vezesPorSemana = getInt(o, "vezesporsemana");
         this.diasAcessoUltimoMes = getInt(o, "diasAcessoUltimoMes");
         this.diasAcessoMes2 = getInt(o, "diasAcessoMes2");
         this.diasAcessoMes3 = getInt(o, "diasAcessoMes3");
         this.diasAcessoMes4 = getInt(o, "diasAcessoMes4");
         this.mediaDiasAcesso4Meses = getInt(o, "mediaDiasAcesso4Meses");
         this.empresa = getInt(o, "empresaCliente");
         this.email = getString(o, "email");
         this.telefones = getString(o, "telefonesCliente");
         this.modalidades = getString(o, "modalidades");
         this.parq = getBoolean(o, "parq");
         this.descricoesModalidades = getString(o, "descricoesModalidades");
         this.situacaoContratoOperacao = getString(o, "situacaoContratoOperacao");
         this.key = getString(o,"key");
         this.nomeConsulta  = getString(o,"nomeConsulta");
         this.dataCadastro = getDate(getString(o,"dataCadastro"));
         this.existeParcVencidaContrato = getBoolean(o,"existeparcelavencidacontrato");
         this.profissao = getString(o,"profissao");
         this.bairro = getString(o,"bairro");
         this.endereco = getString(o,"endereco");
         this.cidade = getString(o,"cidade");
         this.estadoCivil = getString(o,"estadoCivil");
         this.RG = getString(o, "RG");
         this.CPF = getString(o,"cpf");
         this.cargo = getString(o,"cargo");
         this.ultimaVisita = getDate(getString(o,"ultimaVisita"));
         this.UF = getString(o,"UF");
         this.freePassInicio = getDate(getString(o,"freePassInicio"));
         this.freePassFim = getDate(getString(o,"freePassFim"));

     }

     public Integer getCodigoColaboradorProfessor() {
         return codigoColaboradorProfessor;
     }

     public void setCodigoColaboradorProfessor(Integer codigoColaboradorProfessor) {
         this.codigoColaboradorProfessor = codigoColaboradorProfessor;
     }

     public ProfessorSintetico getProfessorSintetico() {
         return professorSintetico;
     }

     public void setProfessorSintetico(ProfessorSintetico professorSintetico) {
         this.professorSintetico = professorSintetico;
     }

     public Integer getCodigo() {
         return codigo;
     }

     public void setCodigo(Integer codigo) {
         this.codigo = codigo;
     }

     public String getNome() {
         return nome;
     }

     public String getNomeMinusculo() {
         if (nome != null) {
             return nome.toLowerCase();
         } else {
             return "";
         }
     }

     public String getNomeAbreviado() {
         if (nome != null) {
             return Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(nome);
         } else {
             return "";
         }
     }

     public String getPrimeiroNome() {
         if (nome != null) {
             return Uteis.getPrimeiroNome(nome);
         } else {
             return "";
         }
     }

     public String getNomeAbreviadoMinusculo() {
         if (nome != null) {
             return Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(nome).toLowerCase();
         } else {
             return "";
         }
     }

     public void setNome(String nome) {
         this.nome = nome;
     }

     public String getSexo() {
         return sexo;
     }

     public String getSexoFull() {
         if(this.getSexo() == null || this.getSexo().equals("")){
             return "";
         }
         if (this.getSexo().toLowerCase().equals("f")) {
             return "Feminino";
         } else {
             return "Masculino";
         }
     }

     public void setSexo(String sexo) {
         this.sexo = sexo;
     }

     public Date getDia() {
         return dia;
     }

     public void setDia(Date dia) {
         this.dia = dia;
     }

     public Integer getCodigoCliente() {
         return codigoCliente;
     }

     public void setCodigoCliente(Integer codigoCliente) {
         this.codigoCliente = codigoCliente;
     }

     public Integer getCodigoPessoa() {
         return codigoPessoa;
     }

     public void setCodigoPessoa(Integer codigoPessoa) {
         this.codigoPessoa = codigoPessoa;
     }

     public Integer getMatricula() {
         return matricula;
     }

     public void setMatricula(Integer matricula) {
         this.matricula = matricula;
     }

     public Date getDataNascimento() {
         if(dataNascimento == null && getPessoa() != null && getPessoa().getDataNascimento() != null){
             dataNascimento = getPessoa().getDataNascimento();
         }
         return dataNascimento;
     }

     public void setDataNascimento(Date dataNascimento) {
         this.dataNascimento = dataNascimento;
     }

     public Integer getIdade() {
         if(idade == null && dataNascimento != null){
             return new Long(Uteis.nrDiasEntreDatas(dataNascimento, Calendario.hoje()) / 365).intValue();
         }
         return idade;
     }

     public Integer getMesesIdade() {
         if(idade == null && dataNascimento != null){
             return new Long(Uteis.nrDiasEntreDatas(dataNascimento, Calendario.hoje()) / 30).intValue();
         }
         return idade;
     }

     public void setIdade(Integer idade) {
         this.idade = idade;
     }

     public String getProfissao() {
         return profissao;
     }

     public void setProfissao(String profissao) {
         this.profissao = profissao;
     }

     public String getColaboradores() {
         return colaboradores;
     }

     public void setColaboradores(String colaboradores) {
         this.colaboradores = colaboradores;
     }

     public Integer getCodigoContrato() {
         return codigoContrato;
     }

     public void setCodigoContrato(Integer codigoContrato) {
         this.codigoContrato = codigoContrato;
     }

     public String getSituacao() {
         if (situacao == null) {
             situacao = "";
         }
         return situacao;
     }

     public void setSituacao(String situacao) {
         this.situacao = situacao;
     }

     public Integer getDuracaoContratoMeses() {
         return duracaoContratoMeses;
     }

     public void setDuracaoContratoMeses(Integer duracaoContratoMeses) {
         this.duracaoContratoMeses = duracaoContratoMeses;
     }

     public String getMnemonicoDoContrato() {
         return mnemonicoDoContrato;
     }

     public void setMnemonicoDoContrato(String mnemonicoDoContrato) {
         this.mnemonicoDoContrato = mnemonicoDoContrato;
     }

     public String getNomeplano() {
         return nomeplano;
     }

     public void setNomeplano(String nomeplano) {
         this.nomeplano = nomeplano;
     }

     public Double getValorfaturadocontrato() {
         return valorfaturadocontrato;
     }

     public void setValorfaturadocontrato(Double valorfaturadocontrato) {
         this.valorfaturadocontrato = valorfaturadocontrato;
     }

     public Double getValorPagoContrato() {
         return valorPagoContrato;
     }

     public void setValorPagoContrato(Double valorPagoContrato) {
         this.valorPagoContrato = valorPagoContrato;
     }

     public Double getValorParcAbertoContrato() {
         return valorParcAbertoContrato;
     }

     public void setValorParcAbertoContrato(Double valorParcAbertoContrato) {
         this.valorParcAbertoContrato = valorParcAbertoContrato;
     }

     public Double getSaldoContaCorrenteCliente() {
         return saldoContaCorrenteCliente;
     }

     public void setSaldoContaCorrenteCliente(Double saldoContaCorrenteCliente) {
         this.saldoContaCorrenteCliente = saldoContaCorrenteCliente;
     }

     public Date getDataVigenciaDe() {
         return dataVigenciaDe;
     }

     public void setDataVigenciaDe(Date dataVigenciaDe) {
         this.dataVigenciaDe = dataVigenciaDe;
     }

     public Date getDataVigenciaAte() {
         return dataVigenciaAte;
     }

     public void setDataVigenciaAte(Date dataVigenciaAte) {
         this.dataVigenciaAte = dataVigenciaAte;
     }

     public Date getDataVigenciaAteAjustada() {
         return dataVigenciaAteAjustada;
     }

     public String getDataVigenciaAteAjustadaApresentar() {
         try {
             return Uteis.getData(dataVigenciaAteAjustada);
         } catch (Exception e) {
             return "";
         }
     }

     public String getDataVigenciaDeApresentar() {
         try {
             return Uteis.getData(dataVigenciaDe);
         } catch (Exception e) {
             return "";
         }
     }

     public void setDataVigenciaAteAjustada(Date dataVigenciaAteAjustada) {
         this.dataVigenciaAteAjustada = dataVigenciaAteAjustada;
     }

     public Date getDataLancamentoContrato() {
         return dataLancamentoContrato;
     }

     public void setDataLancamentoContrato(Date dataLancamentoContrato) {
         this.dataLancamentoContrato = dataLancamentoContrato;
     }

     public Date getDataRenovacaoContrato() {
         return dataRenovacaoContrato;
     }

     public void setDataRenovacaoContrato(Date dataRenovacaoContrato) {
         this.dataRenovacaoContrato = dataRenovacaoContrato;
     }

     public Date getDataRematriculaContrato() {
         return dataRematriculaContrato;
     }

     public void setDataRematriculaContrato(Date dataRematriculaContrato) {
         this.dataRematriculaContrato = dataRematriculaContrato;
     }

     public Date getDataUltimoBV() {
         return dataUltimoBV;
     }

     public void setDataUltimoBV(Date dataUltimoBV) {
         this.dataUltimoBV = dataUltimoBV;
     }

     public Date getDataMatricula() {
         return dataMatricula;
     }

     public void setDataMatricula(Date dataMatricula) {
         this.dataMatricula = dataMatricula;
     }

     public Date getDataUltimarematricula() {
         return dataUltimarematricula;
     }

     public void setDataUltimarematricula(Date dataUltimarematricula) {
         this.dataUltimarematricula = dataUltimarematricula;
     }

     public Integer getDiasAssiduidadeUltRematriculaAteHoje() {
         return diasAssiduidadeUltRematriculaAteHoje;
     }

     public void setDiasAssiduidadeUltRematriculaAteHoje(Integer diasAssiduidadeUltRematriculaAteHoje) {
         this.diasAssiduidadeUltRematriculaAteHoje = diasAssiduidadeUltRematriculaAteHoje;
     }

     public Integer getDiasFaltaSemAcesso() {
         return diasFaltaSemAcesso;
     }

     public void setDiasFaltaSemAcesso(Integer diasFaltaSemAcesso) {
         this.diasFaltaSemAcesso = diasFaltaSemAcesso;
     }

     public Date getDataUltimoacesso() {
         return dataUltimoacesso;
     }

     public void setDataUltimoacesso(Date dataUltimoacesso) {
         this.dataUltimoacesso = dataUltimoacesso;
     }

     public String getFaseAtualCRM() {
         return faseAtualCRM;
     }

     public void setFaseAtualCRM(String faseAtualCRM) {
         this.faseAtualCRM = faseAtualCRM;
     }

     public Date getDataUltimoContatoCRM() {
         return dataUltimoContatoCRM;
     }

     public void setDataUltimoContatoCRM(Date dataUltimoContatoCRM) {
         this.dataUltimoContatoCRM = dataUltimoContatoCRM;
     }

     public String getResponsavelUltimoContatoCRM() {
         return responsavelUltimoContatoCRM;
     }

     public void setResponsavelUltimoContatoCRM(String responsavelUltimoContatoCRM) {
         this.responsavelUltimoContatoCRM = responsavelUltimoContatoCRM;
     }

     public Integer getCodigoUltimoContatoCRM() {
         return codigoUltimoContatoCRM;
     }

     public void setCodigoUltimoContatoCRM(Integer codigoUltimoContatoCRM) {
         this.codigoUltimoContatoCRM = codigoUltimoContatoCRM;
     }

     public String getSituacaoContrato() {
         if (situacaoContrato == null) {
             situacaoContrato = "";
         }
         return situacaoContrato;
     }

     public void setSituacaoContrato(String situacaoContrato) {
         this.situacaoContrato = situacaoContrato;
     }

     public String getTipoPeriodoAcesso() {
         return tipoPeriodoAcesso;
     }

     public void setTipoPeriodoAcesso(String tipoPeriodoAcesso) {
         this.tipoPeriodoAcesso = tipoPeriodoAcesso;
     }

     public Date getDataInicioPeriodoAcesso() {
         return dataInicioPeriodoAcesso;
     }

     public void setDataInicioPeriodoAcesso(Date dataInicioPeriodoAcesso) {
         this.dataInicioPeriodoAcesso = dataInicioPeriodoAcesso;
     }

     public Date getDataFimPeriodoAcesso() {
         return dataFimPeriodoAcesso;
     }

     public void setDataFimPeriodoAcesso(Date dataFimPeriodoAcesso) {
         this.dataFimPeriodoAcesso = dataFimPeriodoAcesso;
     }

     public Integer getDiasAcessoSemanaPassada() {
         return diasAcessoSemanaPassada;
     }

     public void setDiasAcessoSemanaPassada(Integer diasAcessoSemanaPassada) {
         this.diasAcessoSemanaPassada = diasAcessoSemanaPassada;
     }

     public Integer getDiasAcessoSemana2() {
         return diasAcessoSemana2;
     }

     public void setDiasAcessoSemana2(Integer diasAcessoSemana2) {
         this.diasAcessoSemana2 = diasAcessoSemana2;
     }

     public Integer getDiasAcessoSemana3() {
         return diasAcessoSemana3;
     }

     public void setDiasAcessoSemana3(Integer diasAcessoSemana3) {
         this.diasAcessoSemana3 = diasAcessoSemana3;
     }

     public Integer getDiasAcessoSemana4() {
         return diasAcessoSemana4;
     }

     public void setDiasAcessoSemana4(Integer diasAcessoSemana4) {
         this.diasAcessoSemana4 = diasAcessoSemana4;
     }

     public Integer getVezesPorSemana() {
         return vezesPorSemana;
     }

     public void setVezesPorSemana(Integer vezesPorSemana) {
         this.vezesPorSemana = vezesPorSemana;
     }

     public Integer getDiasAcessoUltimoMes() {
         return diasAcessoUltimoMes;
     }

     public void setDiasAcessoUltimoMes(Integer diasAcessoUltimoMes) {
         this.diasAcessoUltimoMes = diasAcessoUltimoMes;
     }

     public Integer getDiasAcessoMes2() {
         return diasAcessoMes2;
     }

     public void setDiasAcessoMes2(Integer diasAcessoMes2) {
         this.diasAcessoMes2 = diasAcessoMes2;
     }

     public Integer getDiasAcessoMes3() {
         return diasAcessoMes3;
     }

     public void setDiasAcessoMes3(Integer diasAcessoMes3) {
         this.diasAcessoMes3 = diasAcessoMes3;
     }

     public Integer getDiasAcessoMes4() {
         return diasAcessoMes4;
     }

     public void setDiasAcessoMes4(Integer diasAcessoMes4) {
         this.diasAcessoMes4 = diasAcessoMes4;
     }

     public Integer getMediaDiasAcesso4Meses() {
         return mediaDiasAcesso4Meses;
     }

     public void setMediaDiasAcesso4Meses(Integer mediaDiasAcesso4Meses) {
         this.mediaDiasAcesso4Meses = mediaDiasAcesso4Meses;
     }

     public GrupoCliente getGrupo() {
         return grupo;
     }

     public void setGrupo(GrupoCliente grupo) {
         this.grupo = grupo;
     }

     public Integer getEmpresa() {
         return empresa;
     }

     public void setEmpresa(Integer empresa) {
         this.empresa = empresa;
     }

     public String getUrlFoto() {
         return urlFoto;
     }

     public void setUrlFoto(String urlFoto) {
         this.urlFoto = urlFoto;
     }

     public String getNivel() {
         return nivel;
     }

     public void setNivel(String nivel) {
         this.nivel = nivel;
     }

     public String getEmail() {
         if((email == null || email.isEmpty()) && getPessoa() != null && !getPessoa().getEmails().isEmpty()){
             email = "";
             for(Email e : getPessoa().getEmails()){
                 email += ";"+e.getEmail();
             }
             email = email.replaceFirst(";", "");
         }
         return email;
     }

     public void setEmail(String email) {
         this.email = email;
     }

     public String getTelefones() {
         if((telefones == null || telefones.isEmpty()) && getPessoa() != null && !getPessoa().getTelefones().isEmpty()){
             telefones = "";
             for(Telefone tel : getPessoa().getTelefones()){
                 telefones += "; "+tel.getTelefone();
             }
             telefones = telefones.replaceFirst("; ", "");
         }
         return telefones;
     }

     public void setTelefones(String telefones) {
         this.telefones = telefones;
     }

     public List<String> getListaColaboradores() {
         final String colab = getColaboradores();
         List<String> lista = new ArrayList<String>();
         if (colab != null && !colab.isEmpty() && colab.contains("/")) {
             String[] aux = colab.split("/");
             for (int i = 0; i < aux.length; i++) {
                 String nomeColab = aux[i].substring(colab.indexOf("-"));
                 lista.add(nomeColab);
             }
         }
         return lista;
     }

     public String getProfessor() {
         final String colab = getColaboradores();
         try {
             if (colab != null && !colab.isEmpty() && colab.contains("PR") && colab.length() > 4) {
                 final int from = colab.indexOf("PR-") + 3;
                 return colab.substring(from, colab.indexOf("/", from));
             }
         } catch (Exception e) {
             Uteis.logar(e, ClienteSintetico.class);
         }
         return "";
     }

     public String getConsultor() {
         final String colab = getColaboradores();
         try {
             if (colab != null && !colab.isEmpty() && colab.contains("CO")) {
                 return colab.substring(colab.indexOf("CO-") + 3, colab.indexOf("/"));
             }
         } catch (Exception e) {
             Uteis.logar(e, ClienteSintetico.class);
         }
         return "";
     }

     public Nivel getNivelAluno() {
         return nivelAluno;
     }

     public void setNivelAluno(Nivel nivelAluno) {
         this.nivelAluno = nivelAluno;
     }

     public Integer getNrTreinosRealizados() {
         return nrTreinosRealizados;
     }

     public void setNrTreinosRealizados(Integer nrTreinosRealizados) {
         this.nrTreinosRealizados = nrTreinosRealizados;
     }

     public Integer getNrTreinosPrevistos() {
         return nrTreinosPrevistos;
     }

     public void setNrTreinosPrevistos(Integer nrTreinosPrevistos) {
         this.nrTreinosPrevistos = nrTreinosPrevistos;
     }

     @JsonIgnore
     public List<Notificacao> getNotificacoes() {
         return notificacoes;
     }

     public void setNotificacoes(List<Notificacao> notificacoes) {
         this.notificacoes = notificacoes;
     }

     public String getAndamentoTreino() {
         if (getNrTreinosPrevistos() == null || getNrTreinosPrevistos().equals(0)
                 || getNrTreinosRealizados() == null || getNrTreinosRealizados().equals(0)) {
             return "0%";
         }
         Double and = getNrTreinosRealizados().doubleValue() / getNrTreinosPrevistos().doubleValue() * 100;
         return (and > 100.0 ? 100.0 : Uteis.arredondarForcando2CasasDecimais(and)) + "%";
     }

     public List<ClienteBadge> getBadges() {
         return badges;
     }

     public void setBadges(List<ClienteBadge> badges) {
         this.badges = badges;
     }

     public String getMatriculaString() {
         try {
             return getMatricula().toString();
         } catch (Exception e) {
             return "";
         }
     }

     @Override
     public String toString(){
         return getNome();
     }
     public String getNomeProfessor() {
         try {
             return getProfessorSintetico().getNomeAbreviado();
         } catch (Exception e) {
             return "";
         }
     }

     public String getCss() {
         return css;
     }

     public void setCss(String css) {
         this.css = css;
     }

     public Integer getPesoRisco() {
         return pesoRisco;
     }

     public void setPesoRisco(Integer pesoRisco) {
         this.pesoRisco = pesoRisco;
     }

     public String getNomeMobileMin(Boolean estaMobile) {
         return getNomeMobile(estaMobile).toLowerCase();
     }

     public String getNomeMobile(Boolean estaMobile) {
         if (estaMobile) {
             return this.getNomeAbreviado();
         } else {
             return this.getNome();
         }
     }

     public List<HistoricoRevisaoProgramaTreino> getRevisoes() {
         return revisoes;
     }

     public void setRevisoes(List<HistoricoRevisaoProgramaTreino> revisoes) {
         this.revisoes = revisoes;
     }

     public Integer getChaveLink() {
         return this.codigo;
     }

     public String getSituacaoMatriculaContrato() {
         return situacaoMatriculaContrato;
     }

     public void setSituacaoMatriculaContrato(String situacaoMatriculaContrato) {
         this.situacaoMatriculaContrato = situacaoMatriculaContrato;
     }

     public Date getDataPrograma() {
         try {
             return getProgramaVigente().getDataTerminoPrevisto();
         } catch (Exception e) {
             return null;
         }
     }

     public String getDataProgramaApresentar() {
         try {
             return Uteis.getData(getProgramaVigente().getDataTerminoPrevisto());
         } catch (Exception e) {
             return "";
         }
     }

     public ProgramaTreino getProgramaVigente() {
         return programaVigente;
     }

     public void setProgramaVigente(ProgramaTreino programaVigente) {
         this.programaVigente = programaVigente;
     }

     public Empresa getEmpresaTreino() {
         return empresaTreino;
     }

     public void setEmpresaTreino(Empresa empresaTreino) {
         this.empresaTreino = empresaTreino;
     }

     public Integer getVersao() {
         if (versao == null) {
             versao = 0;
         }
         return versao;
     }

     public void setVersao(Integer versao) {
         this.versao = versao;
     }

     public String getCodigoAcesso() {
         return codigoAcesso;
     }

     public void setCodigoAcesso(String codigoAcesso) {
         this.codigoAcesso = codigoAcesso;
     }

     public String getObjetivos() {
         return objetivos == null ? "" : objetivos;
     }

     public List<String> getObjetivosLista() {
         return objetivosLista == null ? Arrays.asList(new String[]{"-"}) : objetivosLista;
     }

     public void setObjetivos(String objetivos) {
         this.objetivos = objetivos;
     }

     public String getDadosAvaliacao() {
         return dadosAvaliacao;
     }

     public void setDadosAvaliacao(String dadosAvaliacao) {
         this.dadosAvaliacao = dadosAvaliacao;
     }

     @Override
     public int hashCode() {
         return this.getCodigo() != null && this.getCodigo() > 0
                 ? this.getCodigo().hashCode() : super.hashCode();
     }

     @Override
     public boolean equals(Object obj) {
         if (obj == null) {
             return false;
         }
         if (!(obj instanceof ClienteSintetico)) {
             return false;
         }
         final ClienteSintetico other = (ClienteSintetico) obj;
         if (this.getCodigo() != other.getCodigo() && (this.getCodigo() == null || !this.getCodigo().equals(other.getCodigo()))) {
             return false;
         }
         return true;
     }

     public List<String> getListaTelefones() {
         if (getTelefones() == null || getTelefones().isEmpty()) {
             return new ArrayList<String>();
         } else {
             List<String> lista = new ArrayList<String>();
             String[] tels = getTelefones().split(";");
             for (String str : tels) {
                 lista.add(str.replaceAll("\\{", "").replaceAll("\\}", ""));
             }
             return lista;
         }
     }

     public List<String> getListaEmails() {
         if (getEmail() == null || getEmail().isEmpty()) {
             return new ArrayList<String>();
         } else {
             List<String> lista = new ArrayList<String>();
             String[] emails = getEmail().split(";");
             for (String str : emails) {
                 if(str != null && !str.isEmpty()){
                     lista.add(str.replaceAll("\\{", "").replaceAll("\\}", ""));
                 }

             }
             return lista;
         }
     }

     public Integer getNrAulasExperimentais() {
         if (nrAulasExperimentais == null) {
             nrAulasExperimentais = 0;
         }
         return nrAulasExperimentais;
     }

     public void setNrAulasExperimentais(Integer nrAulasExperimentais) {
         this.nrAulasExperimentais = nrAulasExperimentais;
     }

     public List<Integer> getCodigosModalidades() {
         List<Integer> cods = new ArrayList<Integer>();
         String[] split = getModalidades().split("\\|");
         for (String ar : split) {
             if (ar != null && !ar.isEmpty() && !ar.equals("null")) {
                 cods.add(Integer.valueOf(ar));
             }
         }
         return cods;
     }

     public List<ClienteObservacao> getObservacoes() {
         return observacoes;
     }

     public void setObservacoes(List<ClienteObservacao> observacoes) {
         this.observacoes = observacoes;
     }

     public Pessoa getPessoa() {
         if(pessoa == null){
             pessoa = new Pessoa();
         }
         return pessoa;
     }

     public void setPessoa(Pessoa pessoa) {
         this.pessoa = pessoa;
     }

     public ProgramaTreino getPrograma(){
         return new ProgramaTreino();
     }

     public Boolean getParq() {
         return parq;
     }

     public void setParq(Boolean parq) {
         this.parq = parq;
     }

     public Integer getSaldoCreditoTreino() {
         return saldoCreditoTreino;
     }

     public void setSaldoCreditoTreino(Integer saldoCreditoTreino) {
         this.saldoCreditoTreino = saldoCreditoTreino;
     }

     public String getDescricaoDuracao() {
         return descricaoDuracao;
     }

     public void setDescricaoDuracao(String descricaoDuracao) {
         this.descricaoDuracao = descricaoDuracao;
     }

     public Integer getTotalCreditoTreino() {
         return totalCreditoTreino;
     }

     public void setTotalCreditoTreino(Integer totalCreditoTreino) {
         this.totalCreditoTreino = totalCreditoTreino;
     }

     public String getDescricoesModalidades() {
         return descricoesModalidades;
     }

     public void setDescricoesModalidades(String descricoesModalidades) {
         this.descricoesModalidades = descricoesModalidades;
     }

     public String getSituacaoContratoOperacao() {
         return situacaoContratoOperacao;
     }

     public void setSituacaoContratoOperacao(String situacaoContratoOperacao) {
         this.situacaoContratoOperacao = situacaoContratoOperacao;
     }
     public String getKey() {
         return key;
     }

     public void setKey(String key) {
         this.key = key;
     }

     public List<AvaliacaoFisica> getAvaliacaoFisicas() {
         return avaliacaoFisicas;
     }

     public void setAvaliacaoFisicas(List<AvaliacaoFisica> avaliacaoFisicas) {
         this.avaliacaoFisicas = avaliacaoFisicas;
     }

     public boolean isMenorIdade(){
         return getIdade() == null ? false : getIdade() < 18;
     }

     public Boolean getCrossfit() {
         return crossfit == null ? false : crossfit;
     }

     public void setCrossfit(Boolean crossfit) {
         this.crossfit = crossfit;
     }

     public boolean isSexoMasculino(){
         return UteisValidacao.emptyString(this.sexo)  ? true :  this.sexo.equalsIgnoreCase("M");
     }

     public ItemAvaliacaoFisica getPressao() {
         if(pressao == null){
             pressao = new ItemAvaliacaoFisica();
         }
         return pressao;
     }


     public void setPressao(ItemAvaliacaoFisica pressao) {
         this.pressao = pressao;
     }

     public String getPressaoApresentar(){
         return pressao == null || pressao.getResult().isEmpty() ? " - " : pressao.getResult();
     }

     public String getFcApresentar(){
         return fc == null || fc.getResult().isEmpty() ? " - " : fc.getResult();
     }

     public String getAlturaApresentar(){
         return Uteis.formatarValorNumerico(getAltura());
     }

     public String getPercentualGorduraApresentar() {
         return Uteis.formatarValorNumerico(getPercentualGordura());
     }

     public Double getPercentualGordura() {
         if (percentualGordura == null) {
             percentualGordura = 0.0;
         }
         return percentualGordura;
     }

     public void setPercentualGordura(Double percentualGordura) {
         this.percentualGordura = percentualGordura;
     }

     public String getPesoApresentar(){
         return Uteis.formatarValorNumerico(getPeso());
     }

     public Double getPeso() {
         if(peso == null){
             peso = 0.0;
         }
         return peso;
     }

     public void setPeso(Double peso) {
         this.peso = peso;
     }

     public Double getAltura() {
         if(altura == null){
             altura = 0.0;
         }
         return altura;
     }

     public void setAltura(Double altura) {
         this.altura = altura;
     }

     public ResultadoResistenciaEnum getResistenciaBraco() {
         return resistenciaBraco;
     }

     public void setResistenciaBraco(ResultadoResistenciaEnum resistenciaBraco) {
         this.resistenciaBraco = resistenciaBraco;
     }

     public ResultadoResistenciaEnum getResistenciaAbdomen() {
         return resistenciaAbdomen;
     }

     public void setResistenciaAbdomen(ResultadoResistenciaEnum resistenciaAbdomen) {
         this.resistenciaAbdomen = resistenciaAbdomen;
     }

     public ItemAvaliacaoFisica getFc() {
         if(fc == null){
             fc = new ItemAvaliacaoFisica();
         }
         return fc;
     }

     public void setFc(ItemAvaliacaoFisica fc) {
         this.fc = fc;
     }

     public ItemAvaliacaoFisica getParqP() {
         return parqP;
     }

     public void setParqP(ItemAvaliacaoFisica parqP) {
         this.parqP = parqP;
     }

     public void setObjetivosLista(List<String> objetivosLista) {
         this.objetivosLista = objetivosLista;
     }

     public ItemAvaliacaoFisica getVentilometria() {
         return ventilometria;
     }

     public void setVentilometria(ItemAvaliacaoFisica ventilometria) {
         this.ventilometria = ventilometria;
     }

     public Integer getFlexaoAbdomen() {
         return flexaoAbdomen;
     }

     public void setFlexaoAbdomen(Integer flexaoAbdomen) {
         this.flexaoAbdomen = flexaoAbdomen;
     }

     public Integer getFlexaoBraco() {
         return flexaoBraco;
     }

     public void setFlexaoBraco(Integer flexaoBraco) {
         this.flexaoBraco = flexaoBraco;
     }

     public Date getDataAtualizacaoFoto() {
         return dataAtualizacaoFoto;
     }

     public void setDataAtualizacaoFoto(Date dataAtualizacaoFoto) {
         this.dataAtualizacaoFoto = dataAtualizacaoFoto;
     }

     public Integer getFcRepouso() {
         return fcRepouso;
     }

     public void setFcRepouso(Integer fcRepouso) {
         this.fcRepouso = fcRepouso;
     }

     public Integer getFcMaxima() {
         return fcMaxima;
     }

     public void setFcMaxima(Integer fcMaxima) {
         this.fcMaxima = fcMaxima;
     }

     public Integer getFrequenciaSemanal() {
         return frequenciaSemanal;
     }

     public void setFrequenciaSemanal(Integer frequenciaSemanal) {
         this.frequenciaSemanal = frequenciaSemanal;
     }

     public String getDataUltimoacessoApresentar() {
         try {
             return Uteis.getData(dataUltimoacesso);
         } catch (Exception e) {
             return "";
         }
     }
     public String getNomeConsulta() {
         return nomeConsulta;
     }

     public void setNomeConsulta(String nomeConsultar) {
         this.nomeConsulta = nomeConsultar;
     }

    public String getCodigoExterno() {
        if (codigoExterno == null) {
            codigoExterno = "";
 }
        return codigoExterno;
    }

     public Boolean getAtivo() {
         return ativo;
     }

     public void setAtivo(Boolean ativo) {
         this.ativo = ativo;
     }

     public void setCodigoExterno(String codigoExterno) {
        this.codigoExterno = codigoExterno;
    }

     public String getFotoKeyApp() {
         return fotoKeyApp;
     }

     public void setFotoKeyApp(String fotoKeyApp) {
         this.fotoKeyApp = fotoKeyApp;
     }

     public Date getDataAvaliacao() {
         return dataAvaliacao;
     }

     public void setDataAvaliacao(Date dataAvaliacao) {
         this.dataAvaliacao = dataAvaliacao;
     }

     public Date getDataProximaAvaliacao() {
         return dataProximaAvaliacao;
     }

     public void setDataProximaAvaliacao(Date dataProximaAvaliacao) {
         this.dataProximaAvaliacao = dataProximaAvaliacao;
     }

     public ItemAvaliacaoFisica getPressaoDiastolica() {
         if (pressaoDiastolica == null) {
             pressaoDiastolica = new ItemAvaliacaoFisica();
         }
         return pressaoDiastolica;
     }

     public void setPressaoDiastolica(ItemAvaliacaoFisica pressaoDiastolica) {
         this.pressaoDiastolica = pressaoDiastolica;
     }

     public String getPressaoDiastolicaApresentar(){
         return pressaoDiastolica == null || pressaoDiastolica.getResult().isEmpty() ? " - " : pressaoDiastolica.getResult();
     }

     public ItemAvaliacaoFisica getPressaoSistolica() {
         if (pressaoSistolica == null) {
             pressaoSistolica = new ItemAvaliacaoFisica();
         }
         return pressaoSistolica;
     }

     public void setPressaoSistolica(ItemAvaliacaoFisica pressaoSistolica) {
         this.pressaoSistolica = pressaoSistolica;
     }

     public String getPressaoSistolicaApresentar(){
         return pressaoSistolica == null || pressaoSistolica.getResult().isEmpty() ? " - " : pressaoSistolica.getResult();
     }

     public Integer getAtletaBioimpedancia() {
         return atletaBioimpedancia;
     }

     public void setAtletaBioimpedancia(Integer atleta) {
         this.atletaBioimpedancia = atleta;
     }

     public Integer getNivelAtividadeBioimpedancia() {
         return nivelAtividadeBioimpedancia;
     }

     public void setNivelAtividadeBioimpedancia(Integer nivelAtividadeBioimpedancia) {
         this.nivelAtividadeBioimpedancia = nivelAtividadeBioimpedancia;
     }

     public List<Usuario> getUsuarios() {
         return usuarios;
     }

     public void setUsuarios(List<Usuario> usuarios) {
         this.usuarios = usuarios;
     }

     public Date getDataCadastro() {
         return dataCadastro;
     }

     public void setDataCadastro(Date dataCadastro) {
         this.dataCadastro = dataCadastro;
     }

     public Boolean getExisteParcVencidaContrato() {
         return existeParcVencidaContrato;
     }

     public void setExisteParcVencidaContrato(Boolean existeParcVencidaContrato) {
         this.existeParcVencidaContrato = existeParcVencidaContrato;
     }

     public Boolean getEmpresaUsaFreePass() {
         return empresaUsaFreePass;
     }

     public void setEmpresaUsaFreePass(Boolean empresaUsaFreePass) {
         this.empresaUsaFreePass = empresaUsaFreePass;
     }

     public Date getUltimaVisita() {
         return ultimaVisita;
     }

     public void setUltimaVisita(Date ultimaVisita) {
         this.ultimaVisita = ultimaVisita;
     }

     public String getCargo() {
         return cargo;
     }

     public void setCargo(String cargo) {
         this.cargo = cargo;
     }

     public Boolean isFreePass() {
         return freePass;
     }

     public void setFreePass(Boolean freePass) {
         this.freePass = freePass;
     }

     public String getEndereco() {
         return endereco;
     }

     public void setEndereco(String endereco) {
         this.endereco = endereco;
     }

     public String getCidade() {
         return cidade;
     }

     public void setCidade(String cidade) {
         this.cidade = cidade;
     }

     public String getBairro() {
         return bairro;
     }

     public void setBairro(String bairro) {
         this.bairro = bairro;
     }

     public String getEstadoCivil() {
         return estadoCivil;
     }

     public void setEstadoCivil(String estadoCivil) {
         this.estadoCivil = estadoCivil;
     }

     public String getCPF() {
         return CPF;
     }

     public void setCPF(String CPF) {
         this.CPF = CPF;
     }

     public String getRG() {
         return RG;
     }

     public void setRG(String RG) {
         this.RG = RG;
     }

     public String getUF() {
         return UF;
     }

     public void setUF(String UF) {
         this.UF = UF;
     }

     public List<ProgramaTreino> getProgramaTreinos() {
         return programaTreinos;
     }

     public void setProgramaTreinos(List<ProgramaTreino> programaTreinos) {
         this.programaTreinos = programaTreinos;
     }

     public Double getPesoInicio() {
         return pesoInicio;
     }

     public void setPesoInicio(Double pesoInicio) {
         this.pesoInicio = pesoInicio;
     }

     public Double getPesoAtual() {
         return pesoAtual;
     }

     public void setPesoAtual(Double pesoAtual) {
         this.pesoAtual = pesoAtual;
     }

     public Double getPercentualGorduraInicio() {
         return percentualGorduraInicio;
     }

     public void setPercentualGorduraInicio(Double percentualGorduraInicio) {
         this.percentualGorduraInicio = percentualGorduraInicio;
     }

     public Double getPercentualGorduraAtual() {
         return percentualGorduraAtual;
     }


     public void setPercentualGorduraAtual(Double percentualGorduraAtual) {
         this.percentualGorduraAtual = percentualGorduraAtual;
     }

     public Double getMassaMagraInicio() {
         return massaMagraInicio;
     }

     public void setMassaMagraInicio(Double massaMagraInicio) {
         this.massaMagraInicio = massaMagraInicio;
     }

     public Double getMassaMagraAtual() {
         return massaMagraAtual;
     }

     public void setMassaMagraAtual(Double massaMagraAtual) {
         this.massaMagraAtual = massaMagraAtual;
     }

     public String getGympassUniqueToken() {
         if (gympassUniqueToken == null) {
             gympassUniqueToken = "";
         }
         return gympassUniqueToken;
     }

     public void setGympassUniqueToken(String gympassUniqueToken) {
         this.gympassUniqueToken = gympassUniqueToken;
     }

     public Date getTerminoUltimoPrograma() {
         return terminoUltimoPrograma;
     }

     public void setTerminoUltimoPrograma(Date terminoUltimoPrograma) {
         this.terminoUltimoPrograma = terminoUltimoPrograma;
     }

     public Date getFreePassInicio() {
         return freePassInicio;
     }

     public void setFreePassInicio(Date freePassInicio) {
         this.freePassInicio = freePassInicio;
     }

     public Date getFreePassFim() {
         return freePassFim;
     }

     public void setFreePassFim(Date freePassFim) {
         this.freePassFim = freePassFim;
     }

     public OrigemCliente getOrigemCliente() {
         return origemCliente;
     }

     public void setOrigemCliente(OrigemCliente origemCliente) {
         this.origemCliente = origemCliente;
     }
 }
