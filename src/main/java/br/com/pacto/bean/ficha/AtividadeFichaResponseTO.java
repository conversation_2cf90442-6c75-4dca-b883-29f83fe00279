package br.com.pacto.bean.ficha;

import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.atividade.MetodoExecucaoEnum;
import br.com.pacto.bean.serie.Serie;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.impl.Ordenacao;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 08/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Detalhes da atividade da ficha")
public class AtividadeFichaResponseTO {

    @ApiModelProperty(value = "ID da atividade", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Sequencia", example = "1")
    private Integer sequencia;
    @ApiModelProperty(value = "Atividade")
    private AtividadeResponseTO atividade;
    @ApiModelProperty(value = "Esforço", example = "1")
    private Integer esforco;
    @ApiModelProperty(value = "Método de execução", example = "PIRAMIDE_DECRESCENTE")
    private MetodoExecucaoEnum metodoExecucao;
    @ApiModelProperty(value = "Séries")
    private List<SerieResponseTO> series = new ArrayList<SerieResponseTO>();
    @ApiModelProperty(value = "ID da ficha", example = "1")
    private Integer fichaId;
    @ApiModelProperty(value = "Atividades")
    private List<SetAtividadeFichaResponseDTO> setAtividades = new ArrayList<>();
    @ApiModelProperty(value = "Nome da ficha", example = "Ficha 1")
    private String nomeNaFicha;
    @ApiModelProperty(value = "Nome da ficha", example = "Ficha 1")
    private String complementoNomeAtividade;

    public AtividadeFichaResponseTO() {
    }

    public AtividadeFichaResponseTO(AtividadeFicha af) {
        this.id = af.getCodigo();
        this.sequencia = af.getOrdem();
        this.nomeNaFicha = af.getAtividade() != null ? af.getAtividade().getNome() : af.getNome();
        this.atividade = new AtividadeResponseTO(af.getAtividade());
        this.esforco = af.getIntensidade();
        this.metodoExecucao = af.getMetodoExecucao() == null ? MetodoExecucaoEnum.NAO_ATRIBUIDO : af.getMetodoExecucao();
        this.complementoNomeAtividade = af.getComplementoNomeAtividade();
        if (af.getSeries() != null) {
            List<Serie> seriesOrdenadas = Ordenacao.ordenarLista(af.getSeries(), "ordem");
            for (Serie serie : seriesOrdenadas) {
                this.series.add(new SerieResponseTO(serie));
            }
        }
        this.fichaId = af.getFicha().getCodigo();
        List<SetAtividadeFichaResponseDTO> setAtividades = new ArrayList<>();
        if (null != af.getSetId() && !af.getSetId().equals("null") && af.getMetodoExecucao() != null && StringUtils.isNotBlank(af.getSetId())
                && (af.getMetodoExecucao().equals(MetodoExecucaoEnum.BI_SET) || af.getMetodoExecucao().equals(MetodoExecucaoEnum.TRI_SET))) {
            String setIds[] = af.getSetId().split("\\|");
            for (String atividadeSetId : setIds) {
                try {
                    if (!af.getCodigo().equals(Integer.parseInt(atividadeSetId))) {
                        SetAtividadeFichaResponseDTO setAtividade = new SetAtividadeFichaResponseDTO();
                        setAtividade.setId(Integer.parseInt(atividadeSetId));
                        setAtividades.add(setAtividade);
                    }
                }catch (Exception e){
                    Uteis.logar(e, AtividadeFichaResponseTO.class);
                }

            }
        }
        this.setAtividades.addAll(setAtividades);
    }

    public AtividadeFichaResponseTO(AtividadeFicha af, List<AtividadeFicha> setAtividades) throws Exception {
        this.id = af.getCodigo();
        this.sequencia = af.getOrdem();
        this.atividade = new AtividadeResponseTO(af.getAtividade());
        this.esforco = af.getIntensidade();
        this.metodoExecucao = af.getMetodoExecucao() == null ? MetodoExecucaoEnum.NAO_ATRIBUIDO : af.getMetodoExecucao();
        if (af.getSeries() != null) {
            List<Serie> serieOrdenada = Ordenacao.ordenarLista(af.getSeries(), "ordem");
            for (Serie serie : serieOrdenada) {
                this.series.add(new SerieResponseTO(serie));
            }
        }
        this.fichaId = af.getFicha().getCodigo();

        if(!UteisValidacao.emptyList(setAtividades)) {
            for (AtividadeFicha setAtividadeFicha : setAtividades) {
                if (setAtividadeFicha != null && !UteisValidacao.emptyNumber(setAtividadeFicha.getCodigo())) {
                    this.setAtividades.add(new SetAtividadeFichaResponseDTO(setAtividadeFicha));
                }
            }
        }
        if (af.getComplementoNomeAtividade() != null && !af.getComplementoNomeAtividade().equals("")) {
            this.complementoNomeAtividade = af.getComplementoNomeAtividade();
        }
    }

    public Integer getId() { return id; }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getSequencia() {
        return sequencia;
    }

    public void setSequencia(Integer sequencia) {
        this.sequencia = sequencia;
    }

    public AtividadeResponseTO getAtividade() {
        return atividade;
    }

    public void setAtividade(AtividadeResponseTO atividade) {
        this.atividade = atividade;
    }

    public Integer getEsforco() {
        return esforco;
    }

    public void setEsforco(Integer esforco) {
        this.esforco = esforco;
    }

    public List<SerieResponseTO> getSeries() {
        return series;
    }

    public void setSeries(List<SerieResponseTO> series) {
        this.series = series;
    }

    public MetodoExecucaoEnum getMetodoExecucao() {
        return metodoExecucao;
    }

    public void setMetodoExecucao(MetodoExecucaoEnum metodoExecucao) {
        this.metodoExecucao = metodoExecucao;
    }

    public Integer getFichaId() {
        return fichaId;
    }

    public void setFichaId(Integer fichaId) {
        this.fichaId = fichaId;
    }

    public List<SetAtividadeFichaResponseDTO> getSetAtividades() {
        return setAtividades;
    }

    public void setSetAtividades(List<SetAtividadeFichaResponseDTO> setAtividades) {
        this.setAtividades = setAtividades;
    }

    public String getNomeNaFicha() {
        return nomeNaFicha;
    }

    public void setNomeNaFicha(String nomeNaFicha) {
        this.nomeNaFicha = nomeNaFicha;
    }

    public String getComplementoNomeAtividade() {
        return complementoNomeAtividade;
    }

    public void setComplementoNomeAtividade(String complementoNomeAtividade) {
        this.complementoNomeAtividade = complementoNomeAtividade;
    }
}
