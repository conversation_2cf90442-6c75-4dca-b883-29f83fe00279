package br.com.pacto.bean.ficha;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Detalhes da categoria de ficha")
public class CategoriaFichaResponseTO {

    @ApiModelProperty(value = "Código único identificador da categoria de ficha", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome da categoria de ficha", example = "Musculação")
    private String nome;

    public CategoriaFichaResponseTO() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public CategoriaFichaResponseTO(CategoriaFicha cf) {
        if (cf == null) {
            return;
        }
        this.id = cf.getCodigo();
        this.nome = cf.getNome();
    }
}
