package br.com.pacto.bean.ficha;

import br.com.pacto.bean.atividade.AtividadeFicha;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR> 11/02/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Detalhes da atividade da ficha")
public class SetAtividadeFichaResponseDTO {

    @ApiModelProperty(value = "ID da atividade ficha", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome da atividade ficha", example = "Atividade ficha 1")
    private String nome;

    public SetAtividadeFichaResponseDTO() {
    }

    public SetAtividadeFichaResponseDTO(AtividadeFicha atv) {
        this.id = atv.getCodigo();
        this.nome = atv.getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
