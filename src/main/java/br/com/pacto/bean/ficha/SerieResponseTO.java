package br.com.pacto.bean.ficha;

import br.com.pacto.bean.serie.Serie;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Detalhes da série")
public class SerieResponseTO {

    @ApiModelProperty(value = "ID único da série", example = "1")
    private Integer id;

    @ApiModelProperty(value = "ID da atividade da ficha à qual esta série pertence", example = "1")
    private Integer atividadeFichaId;

    @ApiModelProperty(value = "Sequência/ordem da série na atividade", example = "1")
    private Integer sequencia;

    @ApiModelProperty(value = "Número de repetições da série (valor numérico)", example = "10")
    private Integer repeticaoComp;

    @ApiModelProperty(value = "Repetições da série (pode conter faixas como '8-12')", example = "10")
    private String repeticoes;

    @ApiModelProperty(value = "Carga utilizada na série (formato texto)", example = "60kg")
    private String carga;

    @ApiModelProperty(value = "Carga utilizada na série (valor numérico)", example = "60.0")
    private Double cargaComp;

    @ApiModelProperty(value = "Cadência de execução do movimento (tempo de cada fase)", example = "2-1-2-1")
    private String cadencia;

    @ApiModelProperty(value = "Tempo de descanso após a série em segundos", example = "90")
    private Integer descanso;

    @ApiModelProperty(value = "Observações complementares sobre a execução da série", example = "Manter postura ereta durante todo o movimento")
    private String complemento;

    @ApiModelProperty(value = "Velocidade para atividades cardiovasculares (km/h)", example = "8.5")
    private Double velocidade;

    @ApiModelProperty(value = "Duração da série em minutos (para atividades cardiovasculares)", example = "30")
    private Integer duracao;

    @ApiModelProperty(value = "Distância a ser percorrida na série em metros", example = "5000")
    private Integer distancia;

    @ApiModelProperty(value = "Carga formatada para exibição no aplicativo", example = "60kg")
    private String cargaApp;

    @ApiModelProperty(value = "Repetições formatadas para exibição no aplicativo", example = "10x")
    private String repeticaoApp;

    @ApiModelProperty(value = "Indica se a série foi realizada pelo aluno", example = "false")
    private Boolean serieRealizada;

    public SerieResponseTO() {
    }

    public SerieResponseTO(Serie serie) {
        serie.obterCompValores();
        this.id = serie.getCodigo();
        this.atividadeFichaId = serie.getAtividadeFicha().getCodigo();
        this.sequencia = serie.getOrdem();
        this.repeticoes = serie.getRepeticaoComp();
        this.carga = serie.getCargaComp();
        this.cadencia = serie.getCadencia();
        this.descanso = serie.getDescanso();
        this.complemento = serie.getComplemento();
        this.velocidade = serie.getVelocidade();
        this.duracao = serie.getDuracao();
        this.distancia = serie.getDistancia();
        this.repeticaoComp = serie.getRepeticao();
        this.cargaComp = serie.getCarga();
        this.serieRealizada = serie.getSerieRealizada();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getAtividadeFichaId() {
        return atividadeFichaId;
    }

    public void setAtividadeFichaId(Integer atividadeFichaId) {
        this.atividadeFichaId = atividadeFichaId;
    }

    public Integer getSequencia() {
        return sequencia;
    }

    public void setSequencia(Integer sequencia) {
        this.sequencia = sequencia;
    }

    public String getRepeticoes() {
        return repeticoes;
    }

    public void setRepeticoes(String repeticoes) {
        this.repeticoes = repeticoes;
    }

    public String getCarga() {
        return carga;
    }

    public void setCarga(String carga) {
        this.carga = carga;
    }

    public String getCadencia() {
        return cadencia;
    }

    public void setCadencia(String cadencia) {
        this.cadencia = cadencia;
    }

    public Integer getDescanso() {
        return descanso;
    }

    public void setDescanso(Integer descanso) {
        this.descanso = descanso;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public Double getVelocidade() {
        return velocidade;
    }

    public void setVelocidade(Double velocidade) {
        this.velocidade = velocidade;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getDistancia() {
        return distancia;
    }

    public void setDistancia(Integer distancia) {
        this.distancia = distancia;
    }

    public String getCargaApp() {
        return cargaApp;
    }

    public void setCargaApp(String cargaApp) {
        this.cargaApp = cargaApp;
    }

    public String getRepeticaoApp() {
        return repeticaoApp;
    }

    public void setRepeticaoApp(String repeticaoApp) {
        this.repeticaoApp = repeticaoApp;
    }

    public Integer getRepeticaoComp() { return repeticaoComp; }

    public void setRepeticaoComp(Integer repeticaoComp) { this.repeticaoComp = repeticaoComp; }

    public Double getCargaComp() { return cargaComp; }

    public void setCargaComp(Double cargaComp) { this.cargaComp = cargaComp; }

    public Boolean getSerieRealizada() {
        if (serieRealizada == null){
            serieRealizada = false;
        }
        return serieRealizada;
    }

    public void setSerieRealizada(Boolean serieRealizada) { this.serieRealizada = serieRealizada; }
}
