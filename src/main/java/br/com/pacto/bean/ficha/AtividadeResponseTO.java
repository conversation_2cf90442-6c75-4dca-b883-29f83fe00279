package br.com.pacto.bean.ficha;

import br.com.pacto.bean.atividade.*;
import br.com.pacto.bean.musculo.GrupoMuscularResponseTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 08/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Detalhes da atividade")
public class AtividadeResponseTO {

    @ApiModelProperty(value = "ID da atividade", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome da atividade", example = "Atividade 1")
    private String nome;
    @ApiModelProperty(value = "Atividade ativa")
    private Boolean ativa;
    @ApiModelProperty(value = "Série apenas duração")
    private Boolean serieApenasDuracao;
    @ApiModelProperty(value = "Tipo de atividade", example = "Cardiovascular")
    private TipoAtividadeEndpointEnum tipo;
    @ApiModelProperty(value = "Descrição")
    private String descricao;
    @ApiModelProperty(value = "URI do vídeo")
    private String videoUri;
    @ApiModelProperty(value = "Imagens das atividades")
    private List<AtividadeImagemResponseTO> images = new ArrayList<>();
    @ApiModelProperty(value = "Empresas das atividades")
    private List<AtividadeEmpresaResponseTO> empresas = new ArrayList<>();
    @ApiModelProperty(value = "Vídeos das atividades")
    private List<AtividadeVideoTO> urlLinkVideos;
    @ApiModelProperty(value = "Grupos musculares")
    private List<GrupoMuscularResponseTO> gruposMusculares = new ArrayList<>();

    public AtividadeResponseTO() {
    }

    public AtividadeResponseTO(Atividade atividade) {
        this.id = atividade.getCodigo();
        this.nome = atividade.getNome();
        this.ativa = atividade.isAtivo();
        this.serieApenasDuracao = atividade.getSeriesApenasDuracao();
        this.tipo = atividade.getTipo() == TipoAtividadeEnum.ANAEROBICO ? TipoAtividadeEndpointEnum.NEUROMUSCULAR : TipoAtividadeEndpointEnum.CARDIOVASCULAR;
        this.descricao = atividade.getDescricao();
        this.videoUri = atividade.getLinkVideo();
        if (atividade.getAnimacoes() != null && atividade.getAnimacoes().size() > 0) {
            for (AtividadeAnimacao aa : atividade.getAnimacoes()) {
                this.images.add(new AtividadeImagemResponseTO(aa));
            }
        }
        if (atividade.getEmpresasHabilitadas() != null && atividade.getEmpresasHabilitadas().size() > 0) {
            for (AtividadeEmpresa ae : atividade.getEmpresasHabilitadas()) {
                this.empresas.add(new AtividadeEmpresaResponseTO(ae));
            }
        }
        if (atividade.getGruposMusculares() != null && atividade.getGruposMusculares().size() > 0) {
            for (AtividadeGrupoMuscular agm : atividade.getGruposMusculares()) {
                this.gruposMusculares.add(new GrupoMuscularResponseTO(agm.getGrupoMuscular(), true));
            }
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getAtiva() {
        return ativa;
    }

    public void setAtiva(Boolean ativa) {
        this.ativa = ativa;
    }

    public Boolean getSerieApenasDuracao() {
        return serieApenasDuracao;
    }

    public void setSerieApenasDuracao(Boolean serieApenasDuracao) {
        this.serieApenasDuracao = serieApenasDuracao;
    }

    public TipoAtividadeEndpointEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoAtividadeEndpointEnum tipo) {
        this.tipo = tipo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getVideoUri() {
        return videoUri;
    }

    public void setVideoUri(String videoUri) {
        this.videoUri = videoUri;
    }

    public List<AtividadeImagemResponseTO> getImages() {
        return images;
    }

    public void setImages(List<AtividadeImagemResponseTO> images) {
        this.images = images;
    }

    public List<AtividadeEmpresaResponseTO> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<AtividadeEmpresaResponseTO> empresas) {
        this.empresas = empresas;
    }

    public List<AtividadeVideoTO> getUrlLinkVideos() {
        return urlLinkVideos;
    }

    public void setUrlLinkVideos(List<AtividadeVideoTO> urlLinkVideos) {
        this.urlLinkVideos = urlLinkVideos;
    }

    public List<GrupoMuscularResponseTO> getGruposMusculares() {
        return gruposMusculares;
    }

    public void setGruposMusculares(List<GrupoMuscularResponseTO> gruposMusculares) {
        this.gruposMusculares = gruposMusculares;
    }

}
