package br.com.pacto.bean.ficha;

import br.com.pacto.bean.atividade.*;
import br.com.pacto.bean.musculo.GrupoMuscularResponseTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 08/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Detalhes da atividade")
public class AtividadeResponseTO {

    @ApiModelProperty(value = "ID único da atividade física", example = "15")
    private Integer id;

    @ApiModelProperty(value = "Nome da atividade física", example = "Supino Reto com Barra")
    private String nome;

    @ApiModelProperty(value = "Indica se a atividade está ativa no sistema", example = "true")
    private Boolean ativa;

    @ApiModelProperty(value = "Indica se a série é configurada apenas por duração (para atividades cardiovasculares)", example = "false")
    private Boolean serieApenasDuracao;

    @ApiModelProperty(value = "Tipo da atividade física. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- NEUROMUSCULAR (Neuromuscular)\n" +
            "- CARDIOVASCULAR (Cardiovascular)", example = "NEUROMUSCULAR")
    private TipoAtividadeEndpointEnum tipo;

    @ApiModelProperty(value = "Descrição detalhada da atividade e sua execução", example = "Exercício para desenvolvimento do peitoral maior, deltoides anterior e tríceps")
    private String descricao;

    @ApiModelProperty(value = "URL do vídeo demonstrativo da atividade", example = "https://exemplo.com/video/supino-reto")
    private String videoUri;

    @ApiModelProperty(value = "Lista de imagens demonstrativas da atividade")
    private List<AtividadeImagemResponseTO> images = new ArrayList<>();

    @ApiModelProperty(value = "Lista de empresas habilitadas para utilizar esta atividade")
    private List<AtividadeEmpresaResponseTO> empresas = new ArrayList<>();

    @ApiModelProperty(value = "Lista de vídeos adicionais da atividade")
    private List<AtividadeVideoTO> urlLinkVideos;

    @ApiModelProperty(value = "Lista de grupos musculares trabalhados pela atividade")
    private List<GrupoMuscularResponseTO> gruposMusculares = new ArrayList<>();

    public AtividadeResponseTO() {
    }

    public AtividadeResponseTO(Atividade atividade) {
        this.id = atividade.getCodigo();
        this.nome = atividade.getNome();
        this.ativa = atividade.isAtivo();
        this.serieApenasDuracao = atividade.getSeriesApenasDuracao();
        this.tipo = atividade.getTipo() == TipoAtividadeEnum.ANAEROBICO ? TipoAtividadeEndpointEnum.NEUROMUSCULAR : TipoAtividadeEndpointEnum.CARDIOVASCULAR;
        this.descricao = atividade.getDescricao();
        this.videoUri = atividade.getLinkVideo();
        if (atividade.getAnimacoes() != null && atividade.getAnimacoes().size() > 0) {
            for (AtividadeAnimacao aa : atividade.getAnimacoes()) {
                this.images.add(new AtividadeImagemResponseTO(aa));
            }
        }
        if (atividade.getEmpresasHabilitadas() != null && atividade.getEmpresasHabilitadas().size() > 0) {
            for (AtividadeEmpresa ae : atividade.getEmpresasHabilitadas()) {
                this.empresas.add(new AtividadeEmpresaResponseTO(ae));
            }
        }
        if (atividade.getGruposMusculares() != null && atividade.getGruposMusculares().size() > 0) {
            for (AtividadeGrupoMuscular agm : atividade.getGruposMusculares()) {
                this.gruposMusculares.add(new GrupoMuscularResponseTO(agm.getGrupoMuscular(), true));
            }
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getAtiva() {
        return ativa;
    }

    public void setAtiva(Boolean ativa) {
        this.ativa = ativa;
    }

    public Boolean getSerieApenasDuracao() {
        return serieApenasDuracao;
    }

    public void setSerieApenasDuracao(Boolean serieApenasDuracao) {
        this.serieApenasDuracao = serieApenasDuracao;
    }

    public TipoAtividadeEndpointEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoAtividadeEndpointEnum tipo) {
        this.tipo = tipo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getVideoUri() {
        return videoUri;
    }

    public void setVideoUri(String videoUri) {
        this.videoUri = videoUri;
    }

    public List<AtividadeImagemResponseTO> getImages() {
        return images;
    }

    public void setImages(List<AtividadeImagemResponseTO> images) {
        this.images = images;
    }

    public List<AtividadeEmpresaResponseTO> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<AtividadeEmpresaResponseTO> empresas) {
        this.empresas = empresas;
    }

    public List<AtividadeVideoTO> getUrlLinkVideos() {
        return urlLinkVideos;
    }

    public void setUrlLinkVideos(List<AtividadeVideoTO> urlLinkVideos) {
        this.urlLinkVideos = urlLinkVideos;
    }

    public List<GrupoMuscularResponseTO> getGruposMusculares() {
        return gruposMusculares;
    }

    public void setGruposMusculares(List<GrupoMuscularResponseTO> gruposMusculares) {
        this.gruposMusculares = gruposMusculares;
    }

}
