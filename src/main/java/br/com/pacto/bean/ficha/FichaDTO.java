package br.com.pacto.bean.ficha;

import br.com.pacto.bean.programa.TipoExecucaoEnum;
import br.com.pacto.objeto.to.AtividadeFichaDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Created by paulo on 21/01/2020.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados para criação ou atualização de ficha de treino")
public class FichaDTO {

    @ApiModelProperty(value = "ID único da ficha", example = "1")
    private Integer id;

    @ApiModelProperty(value = "ID do programa de treino ao qual a ficha pertence", example = "10")
    private Integer programaId;

    @ApiModelProperty(value = "Nome da ficha de treino", example = "Ficha Iniciante - Membros Superiores")
    private String nome;

    @ApiModelProperty(value = "ID da categoria da ficha", example = "1")
    private Integer categoriaId;

    @ApiModelProperty(value = "Tipo de execução da ficha. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- ALTERNADO (Execução alternada)\n" +
            "- DIAS_SEMANA (Execução por dias da semana)\n", example = "ALTERNADO")
    private TipoExecucaoEnum tipo_execucao;

    @ApiModelProperty(value = "Lista dos dias da semana para execução da ficha", example = "[\"SEG\", \"QUA\", \"SEX\"]")
    private List<String> dias_semana;

    @ApiModelProperty(value = "Mensagem orientativa para o aluno", example = "Foque na execução correta dos movimentos")
    private String mensagem;

    @ApiModelProperty(value = "Lista das atividades que compõem a ficha")
    private List<AtividadeFichaDTO> atividades;

    @ApiModelProperty(value = "Indica se a ficha está ativa no sistema", example = "true")
    private Boolean ativo;

    @ApiModelProperty(value = "Indica se a ficha é pré-definida (modelo)", example = "false")
    private Boolean predefinida = Boolean.FALSE;
    private Integer ordem;
    private String nomePrograma;
    private String ultimaExecucao;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getProgramaId() {
        return programaId;
    }

    public void setProgramaId(Integer programaId) {
        this.programaId = programaId;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCategoriaId() {
        return categoriaId;
    }

    public void setCategoriaId(Integer categoriaId) {
        this.categoriaId = categoriaId;
    }

    public TipoExecucaoEnum getTipo_execucao() {
        return tipo_execucao;
    }

    public void setTipo_execucao(TipoExecucaoEnum tipo_execucao) {
        this.tipo_execucao = tipo_execucao;
    }

    public List<String> getDias_semana() {
        return dias_semana;
    }

    public void setDias_semana(List<String> dias_semana) {
        this.dias_semana = dias_semana;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public List<AtividadeFichaDTO> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<AtividadeFichaDTO> atividades) {
        this.atividades = atividades;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Boolean getPredefinida() {
        return predefinida;
    }

    public void setPredefinida(Boolean predefinida) {
        this.predefinida = predefinida;
    }

    public String getNomePrograma() {
        return nomePrograma;
    }

    public void setNomePrograma(String nomePrograma) {
        this.nomePrograma = nomePrograma;
    }

    public String getUltimaExecucao() {
        return ultimaExecucao;
    }

    public void setUltimaExecucao(String ultimaExecucao) {
        this.ultimaExecucao = ultimaExecucao;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }
}
