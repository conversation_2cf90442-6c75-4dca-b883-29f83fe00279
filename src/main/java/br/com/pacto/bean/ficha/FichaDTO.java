package br.com.pacto.bean.ficha;

import br.com.pacto.bean.programa.TipoExecucaoEnum;
import br.com.pacto.objeto.to.AtividadeFichaDTO;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

/**
 * Created by paulo on 21/01/2020.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FichaDTO {

    private Integer id;
    private Integer programaId;
    private String nome;
    private Integer categoriaId;
    private TipoExecucaoEnum tipo_execucao;
    private List<String> dias_semana;
    private String mensagem;
    private List<AtividadeFichaDTO> atividades;
    private Boolean ativo;
    private Boolean predefinida = Boolean.FALSE;
    private String nomePrograma;
    private String ultimaExecucao;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getProgramaId() {
        return programaId;
    }

    public void setProgramaId(Integer programaId) {
        this.programaId = programaId;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCategoriaId() {
        return categoriaId;
    }

    public void setCategoriaId(Integer categoriaId) {
        this.categoriaId = categoriaId;
    }

    public TipoExecucaoEnum getTipo_execucao() {
        return tipo_execucao;
    }

    public void setTipo_execucao(TipoExecucaoEnum tipo_execucao) {
        this.tipo_execucao = tipo_execucao;
    }

    public List<String> getDias_semana() {
        return dias_semana;
    }

    public void setDias_semana(List<String> dias_semana) {
        this.dias_semana = dias_semana;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public List<AtividadeFichaDTO> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<AtividadeFichaDTO> atividades) {
        this.atividades = atividades;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Boolean getPredefinida() {
        return predefinida;
    }

    public void setPredefinida(Boolean predefinida) {
        this.predefinida = predefinida;
    }

    public String getNomePrograma() {
        return nomePrograma;
    }

    public void setNomePrograma(String nomePrograma) {
        this.nomePrograma = nomePrograma;
    }

    public String getUltimaExecucao() {
        return ultimaExecucao;
    }

    public void setUltimaExecucao(String ultimaExecucao) {
        this.ultimaExecucao = ultimaExecucao;
    }
}
