package br.com.pacto.bean.aula;

import br.com.pacto.bean.nivel.NivelTO;
import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.controller.json.ambiente.AmbienteResponseTO;
import br.com.pacto.controller.json.aulaDia.AulaDTO;
import br.com.pacto.controller.json.aulaDia.AulaResponseDTO;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.impl.UtilReflection;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.Date;

@ApiModel(description = "Informações da aula alterada temporariamente")
public class EdicaoAulaTemporaria {
    @ApiModelProperty(value = "Código identificador da aula", example = "1")
    private Integer codigo;
    @ApiModelProperty(value = "Código identificador do professor responsável pela aula", example = "1")
    private Integer professor;
    @ApiModelProperty(value = "Nome do professor responsável pela aula", example = "Renato Cariri Alves")
    private String professorNome;
    @ApiModelProperty(value = "Código do ambiente que será utilizado para realização da aula", example = "1")
    private Integer ambiente;
    @ApiModelProperty(value = "Nome do ambiente que a aula será realizada", example = "Spinning Room")
    private String ambienteNome;
    @ApiModelProperty(value = "Nível da aula", example = "INTERMEDIÁRIO 1")
    private String nivel;
    @ApiModelProperty(value = "Hora de início da aula", example = "07:00")
    private String horarioInicial;
    @ApiModelProperty(value = "Hora de término da aula", example = "08:00")
    private String horarioFinal;
    @ApiModelProperty(value = "Dia da aula", example = "20250610")
    private Date diaAula;
    @ApiModelProperty(value = "Capacidade de alunos para a aula", example = "30")
    private Integer capacidade;
    @ApiModelProperty(value = "Idade mínima necessária para participação da aula", example = "18")
    private Integer idadeMinimaAnos;
    @ApiModelProperty(value = "Idade mínima em meses necessária para participação da aula", example = "216")
    private Integer idadeMinimaMeses;
    @ApiModelProperty(value = "Idade máxima em anos para participação da aula", example = "70")
    private Integer idadeMaximaAnos;
    @ApiModelProperty(value = "Idade máxima em meses para participação da aula", example = "840")
    private Integer idadeMaximaMeses;
    @ApiModelProperty(value = "Tempo de tolerância de checkin para a aula em minutos", example = "10")
    private Integer toleranciaCheckinMinutos;
    @ApiModelProperty(value = "Código do horário de turma da aula", example = "23")
    private Integer horarioTurma;
    @ApiModelProperty(value = "Tolerância do checkin." +
            "\n\n <strong>Valores disponíveis</strong>" +
            "- 1 (Após início)" +
            "- 2 (Antes do início)",
            example = "1", allowableValues = "1,2")
    private String toleranciaCheckinPeriodo;
    @ApiModelProperty(value = "Mensagem da aula", example = "Aula de Spinning.")
    private String mensagem;
    @ApiModelProperty(value = "Indica se é uma aula GymPass", example = "false")
    private boolean aulaGymPass;
    @ApiModelProperty(value = "Indica se é uma aula TotalPass", example = "false")
    private boolean aulaTotalPass;
    @ApiModelProperty(value = "Indica se deve validar a modalidade do contrato quando o aluno for realizar checkin", example = "false")
    private boolean validarModalidadeContratoCheckin;
    @ApiModelProperty(value = "Indica se deve controlar o checkin dos alunos", example = "true")
    private boolean controlarCheckin;
    @ApiModelProperty(value = "Tipo de alteração realizada", example = "ATUALIZACAO")
    private String tipoAlteracao;
    @ApiModelProperty(value = "Dia da semana que a aula será realizada", example = "Sat Apr 05 14:03:22 BRT 2025")
    private String diaSemana;
    @ApiModelProperty(value = "Data limite para a aula", example = "2025-04-05 13:47:22")
    private Date limite;
    private String nome;
    @ApiModelProperty(value = "Limite de vagas agregados", example = "30")
    private Integer limiteVagasAgregados;

    public EdicaoAulaTemporaria(AulaDTO aula) {
        this.professor = aula.getProfessorId() != null ? Integer.valueOf(aula.getProfessorId()) : null;
        this.ambiente = aula.getAmbienteId() != null ? Integer.valueOf(aula.getAmbienteId()) : null;
        StringBuilder niveis = new StringBuilder();
        if (aula.getNiveis() != null && !aula.getNiveis().isEmpty()) {
            for (NivelTO nivel : aula.getNiveis()) {
                niveis.append(",").append(nivel.getId());
            }
            this.nivel = niveis.substring(1);
        }
        this.horarioInicial = aula.getHorarioInicial();
        this.horarioFinal = aula.getHorarioFinal();
        this.capacidade = aula.getCapacidade() != null ? Integer.valueOf(aula.getCapacidade()) : null;
        this.idadeMinimaAnos = aula.getIdadeMinimaAnos();
        this.idadeMinimaMeses = aula.getIdadeMinimaMeses();
        this.idadeMaximaAnos = aula.getIdadeMaximaAnos();
        this.idadeMaximaMeses = aula.getIdadeMaximaMeses();
        this.toleranciaCheckinMinutos = Integer.valueOf(aula.getToleranciaMin());
        this.toleranciaCheckinPeriodo = aula.getTipoTolerancia();
        this.mensagem = aula.getMensagem();
        this.aulaGymPass = aula.isVisualizarProdutosGympass();
        this.aulaTotalPass = aula.isVisualizarProdutosTotalpass();
        this.validarModalidadeContratoCheckin = !aula.getNaoValidarModalidadeContrato();
        this.controlarCheckin = aula.getValidarRestricoesMarcacao() != null ? aula.getValidarRestricoesMarcacao() : false;
        this.tipoAlteracao = aula.getTipoEscolhaEdicao();
        this.diaSemana = aula.getDiaSemana();
        this.nome = aula.getNome();
        this.limiteVagasAgregados = aula.getLimiteVagasAgregados();

    }

    public AulaResponseDTO toAulaResponseDTO() {
        AulaResponseDTO aulaResponseDTO = new AulaResponseDTO();
        aulaResponseDTO.setProfessor(new ProfessorResponseTO(this.professor, null));
        aulaResponseDTO.setAmbiente(new AmbienteResponseTO(this.ambiente, null));
        aulaResponseDTO.setNiveis(new ArrayList<>());
        if (!UteisValidacao.emptyString(this.nivel)) {
            String[] splitNivel = this.nivel.split(",");
            for (String nivel : splitNivel) {
                NivelTO nivelTO = new NivelTO();
                nivelTO.setId(Integer.valueOf(nivel));
                aulaResponseDTO.getNiveis().add(nivelTO);
            }
        }
        aulaResponseDTO.setCapacidade(capacidade);
        aulaResponseDTO.setIdadeMinimaAnos(idadeMinimaAnos);
        aulaResponseDTO.setIdadeMinimaMeses(idadeMinimaMeses);
        aulaResponseDTO.setIdadeMaximaAnos(idadeMaximaAnos);
        aulaResponseDTO.setIdadeMaximaMeses(idadeMaximaMeses);
        aulaResponseDTO.setToleranciaMin(toleranciaCheckinMinutos);
        aulaResponseDTO.setTipoTolerancia(Integer.valueOf(toleranciaCheckinPeriodo));
        aulaResponseDTO.setMensagem(mensagem);
        aulaResponseDTO.setVisualizarProdutosGympass(aulaGymPass);
        aulaResponseDTO.setVisualizarProdutosTotalpass(aulaTotalPass);
        aulaResponseDTO.setNaoValidarModalidadeContrato(!validarModalidadeContratoCheckin);
        aulaResponseDTO.setValidarRestricoesMarcacao(controlarCheckin);
        aulaResponseDTO.setNome(nome);
        aulaResponseDTO.setLimiteVagasAgregados(limiteVagasAgregados);
        return aulaResponseDTO;
    }

    public EdicaoAulaTemporaria() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getProfessor() {
        return professor;
    }

    public void setProfessor(Integer professor) {
        this.professor = professor;
    }

    public Integer getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Integer ambiente) {
        this.ambiente = ambiente;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public Date getDiaAula() {
        return diaAula;
    }

    public void setDiaAula(Date diaAula) {
        this.diaAula = diaAula;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public Integer getLimiteVagasAgregados() {
        return limiteVagasAgregados;
    }

    public void setLimiteVagasAgregados(Integer limiteVagasAgregados) {
        this.limiteVagasAgregados = limiteVagasAgregados;
    }

    public Integer getIdadeMinimaAnos() {
        return idadeMinimaAnos;
    }

    public void setIdadeMinimaAnos(Integer idadeMinimaAnos) {
        this.idadeMinimaAnos = idadeMinimaAnos;
    }

    public Integer getIdadeMinimaMeses() {
        return idadeMinimaMeses;
    }

    public void setIdadeMinimaMeses(Integer idadeMinimaMeses) {
        this.idadeMinimaMeses = idadeMinimaMeses;
    }

    public Integer getIdadeMaximaAnos() {
        return idadeMaximaAnos;
    }

    public void setIdadeMaximaAnos(Integer idadeMaximaAnos) {
        this.idadeMaximaAnos = idadeMaximaAnos;
    }

    public Integer getIdadeMaximaMeses() {
        return idadeMaximaMeses;
    }

    public void setIdadeMaximaMeses(Integer idadeMaximaMeses) {
        this.idadeMaximaMeses = idadeMaximaMeses;
    }

    public Integer getToleranciaCheckinMinutos() {
        return toleranciaCheckinMinutos;
    }

    public void setToleranciaCheckinMinutos(Integer toleranciaCheckinMinutos) {
        this.toleranciaCheckinMinutos = toleranciaCheckinMinutos;
    }

    public String getToleranciaCheckinPeriodo() {
        return toleranciaCheckinPeriodo;
    }

    public void setToleranciaCheckinPeriodo(String toleranciaCheckinPeriodo) {
        this.toleranciaCheckinPeriodo = toleranciaCheckinPeriodo;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public boolean isAulaGymPass() {
        return aulaGymPass;
    }

    public void setAulaGymPass(boolean aulaGymPass) {
        this.aulaGymPass = aulaGymPass;
    }

    public boolean isAulaTotalPass() {
        return aulaTotalPass;
    }

    public void setAulaTotalPass(boolean aulaTotalPass) {
        this.aulaTotalPass = aulaTotalPass;
    }

    public boolean isValidarModalidadeContratoCheckin() {
        return validarModalidadeContratoCheckin;
    }

    public void setValidarModalidadeContratoCheckin(boolean validarModalidadeContratoCheckin) {
        this.validarModalidadeContratoCheckin = validarModalidadeContratoCheckin;
    }

    public boolean isControlarCheckin() {
        return controlarCheckin;
    }

    public void setControlarCheckin(boolean controlarCheckin) {
        this.controlarCheckin = controlarCheckin;
    }

    public String getTipoAlteracao() {
        return tipoAlteracao;
    }

    public void setTipoAlteracao(String tipoAlteracao) {
        this.tipoAlteracao = tipoAlteracao;
    }

    public Date getLimite() {
        return limite;
    }

    public void setLimite(Date limite) {
        this.limite = limite;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public String getProfessorNome() {
        return professorNome;
    }

    public void setProfessorNome(String professorNome) {
        this.professorNome = professorNome;
    }

    public Integer getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(Integer horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public String getAmbienteNome() {
        return ambienteNome;
    }

    public void setAmbienteNome(String ambienteNome) {
        this.ambienteNome = ambienteNome;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }


    @Override
    public String toString() {
        return "[codigo:" + codigo + "]<br/>" +
               "[professor:" + professor + "]<br/>" +
               "[ambiente:" + ambiente + "]<br/>" +
               "[nivel:'" + nivel + "']<br/>" +
               "[horarioInicial:'" + horarioInicial + "']<br/>" +
               "[horarioFinal:'" + horarioFinal + "']<br/>" +
               "[diaAula:" + diaAula + "]<br/>" +
               "[capacidade:" + capacidade + "]<br/>" +
               "[limiteVagasAgregados:" + limiteVagasAgregados + "]<br/>" +
               "[idadeMinimaAnos:" + idadeMinimaAnos + "]<br/>" +
               "[idadeMinimaMeses:" + idadeMinimaMeses + "]<br/>" +
               "[idadeMaximaAnos:" + idadeMaximaAnos + "]<br/>" +
               "[idadeMaximaMeses:" + idadeMaximaMeses + "]<br/>" +
               "[toleranciaCheckinMinutos:" + toleranciaCheckinMinutos + "]<br/>" +
               "[horarioTurma:" + horarioTurma + "]<br/>" +
               "[toleranciaCheckinPeriodo:'" + toleranciaCheckinPeriodo + "']<br/>" +
               "[mensagem:'" + mensagem + "']<br/>" +
               "[aulaGymPass:" + aulaGymPass + "]<br/>" +
               "[aulaTotalPass:" + aulaTotalPass + "]<br/>" +
               "[validarModalidadeContratoCheckin:" + validarModalidadeContratoCheckin + "]<br/>" +
               "[controlarCheckin:" + controlarCheckin + "]<br/>" +
               "[diaSemana:'" + diaSemana + "']<br/>";
    }

    public String getDescricaoParaLog(EdicaoAulaTemporaria v2) {
        try {
            StringBuilder log = new StringBuilder();
            log.append(UtilReflection.difference(this, v2));
            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }
}
