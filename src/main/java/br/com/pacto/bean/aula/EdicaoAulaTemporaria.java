package br.com.pacto.bean.aula;

import br.com.pacto.bean.nivel.NivelTO;
import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.controller.json.ambiente.AmbienteResponseTO;
import br.com.pacto.controller.json.aulaDia.AulaDTO;
import br.com.pacto.controller.json.aulaDia.AulaResponseDTO;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.impl.UtilReflection;

import java.util.ArrayList;
import java.util.Date;

public class EdicaoAulaTemporaria {

    private Integer codigo;
    private Integer professor;
    private String professorNome;
    private Integer ambiente;
    private String ambienteNome;
    private String nivel;
    private String horarioInicial;
    private String horarioFinal;
    private Date diaAula;
    private Integer capacidade;
    private Integer idadeMinimaAnos;
    private Integer idadeMinimaMeses;
    private Integer idadeMaximaAnos;
    private Integer idadeMaximaMeses;
    private Integer toleranciaCheckinMinutos;
    private Integer horarioTurma;
    private String toleranciaCheckinPeriodo;
    private String mensagem;
    private boolean aulaGymPass;
    private boolean aulaTotalPass;
    private boolean validarModalidadeContratoCheckin;
    private boolean controlarCheckin;
    private String tipoAlteracao;
    private String diaSemana;
    private Date limite;
    private String nome;

    public EdicaoAulaTemporaria(AulaDTO aula) {
        this.professor = Integer.valueOf(aula.getProfessorId());
        this.ambiente = Integer.valueOf(aula.getAmbienteId());
        StringBuilder niveis = new StringBuilder();
        if (!aula.getNiveis().isEmpty()) {
            for (NivelTO nivel : aula.getNiveis()) {
                niveis.append(",").append(nivel.getId());
            }
            this.nivel = niveis.substring(1);
        }
        this.horarioInicial = aula.getHorarioInicial();
        this.horarioFinal = aula.getHorarioFinal();
        this.capacidade = Integer.valueOf(aula.getCapacidade());
        this.idadeMinimaAnos = aula.getIdadeMinimaAnos();
        this.idadeMinimaMeses = aula.getIdadeMinimaMeses();
        this.idadeMaximaAnos = aula.getIdadeMaximaAnos();
        this.idadeMaximaMeses = aula.getIdadeMaximaMeses();
        this.toleranciaCheckinMinutos = Integer.valueOf(aula.getToleranciaMin());
        this.toleranciaCheckinPeriodo = aula.getTipoTolerancia();
        this.mensagem = aula.getMensagem();
        this.aulaGymPass = aula.isVisualizarProdutosGympass();
        this.aulaTotalPass = aula.isVisualizarProdutosTotalpass();
        this.validarModalidadeContratoCheckin = !aula.getNaoValidarModalidadeContrato();
        this.controlarCheckin = aula.getValidarRestricoesMarcacao();
        this.tipoAlteracao = aula.getTipoEscolhaEdicao();
        this.diaSemana = aula.getDiaSemana();
        this.nome = aula.getNome();

    }

    public AulaResponseDTO toAulaResponseDTO(){
        AulaResponseDTO aulaResponseDTO = new AulaResponseDTO();
        aulaResponseDTO.setProfessor(new ProfessorResponseTO(this.professor, null));
        aulaResponseDTO.setAmbiente(new AmbienteResponseTO(this.ambiente, null));
        aulaResponseDTO.setNiveis(new ArrayList<>());
        if(!UteisValidacao.emptyString(this.nivel)){
            String[] splitNivel = this.nivel.split(",");
            for (String nivel : splitNivel) {
                NivelTO nivelTO = new NivelTO();
                nivelTO.setId(Integer.valueOf(nivel));
                aulaResponseDTO.getNiveis().add(nivelTO);
            }
        }
        aulaResponseDTO.setCapacidade(capacidade);
        aulaResponseDTO.setIdadeMinimaAnos(idadeMinimaAnos);
        aulaResponseDTO.setIdadeMinimaMeses(idadeMinimaMeses);
        aulaResponseDTO.setIdadeMaximaAnos(idadeMaximaAnos);
        aulaResponseDTO.setIdadeMaximaMeses(idadeMaximaMeses);
        aulaResponseDTO.setToleranciaMin(toleranciaCheckinMinutos);
        aulaResponseDTO.setTipoTolerancia(Integer.valueOf(toleranciaCheckinPeriodo));
        aulaResponseDTO.setMensagem(mensagem);
        aulaResponseDTO.setVisualizarProdutosGympass(aulaGymPass);
        aulaResponseDTO.setVisualizarProdutosTotalpass(aulaTotalPass);
        aulaResponseDTO.setNaoValidarModalidadeContrato(!validarModalidadeContratoCheckin);
        aulaResponseDTO.setValidarRestricoesMarcacao(controlarCheckin);
        aulaResponseDTO.setNome(nome);
        return aulaResponseDTO;
    }

    public EdicaoAulaTemporaria() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getProfessor() {
        return professor;
    }

    public void setProfessor(Integer professor) {
        this.professor = professor;
    }

    public Integer getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Integer ambiente) {
        this.ambiente = ambiente;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public Date getDiaAula() {
        return diaAula;
    }

    public void setDiaAula(Date diaAula) {
        this.diaAula = diaAula;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public Integer getIdadeMinimaAnos() {
        return idadeMinimaAnos;
    }

    public void setIdadeMinimaAnos(Integer idadeMinimaAnos) {
        this.idadeMinimaAnos = idadeMinimaAnos;
    }

    public Integer getIdadeMinimaMeses() {
        return idadeMinimaMeses;
    }

    public void setIdadeMinimaMeses(Integer idadeMinimaMeses) {
        this.idadeMinimaMeses = idadeMinimaMeses;
    }

    public Integer getIdadeMaximaAnos() {
        return idadeMaximaAnos;
    }

    public void setIdadeMaximaAnos(Integer idadeMaximaAnos) {
        this.idadeMaximaAnos = idadeMaximaAnos;
    }

    public Integer getIdadeMaximaMeses() {
        return idadeMaximaMeses;
    }

    public void setIdadeMaximaMeses(Integer idadeMaximaMeses) {
        this.idadeMaximaMeses = idadeMaximaMeses;
    }

    public Integer getToleranciaCheckinMinutos() {
        return toleranciaCheckinMinutos;
    }

    public void setToleranciaCheckinMinutos(Integer toleranciaCheckinMinutos) {
        this.toleranciaCheckinMinutos = toleranciaCheckinMinutos;
    }

    public String getToleranciaCheckinPeriodo() {
        return toleranciaCheckinPeriodo;
    }

    public void setToleranciaCheckinPeriodo(String toleranciaCheckinPeriodo) {
        this.toleranciaCheckinPeriodo = toleranciaCheckinPeriodo;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public boolean isAulaGymPass() {
        return aulaGymPass;
    }

    public void setAulaGymPass(boolean aulaGymPass) {
        this.aulaGymPass = aulaGymPass;
    }

    public boolean isAulaTotalPass() {
        return aulaTotalPass;
    }

    public void setAulaTotalPass(boolean aulaTotalPass) {
        this.aulaTotalPass = aulaTotalPass;
    }

    public boolean isValidarModalidadeContratoCheckin() {
        return validarModalidadeContratoCheckin;
    }

    public void setValidarModalidadeContratoCheckin(boolean validarModalidadeContratoCheckin) {
        this.validarModalidadeContratoCheckin = validarModalidadeContratoCheckin;
    }

    public boolean isControlarCheckin() {
        return controlarCheckin;
    }

    public void setControlarCheckin(boolean controlarCheckin) {
        this.controlarCheckin = controlarCheckin;
    }

    public String getTipoAlteracao() {
        return tipoAlteracao;
    }

    public void setTipoAlteracao(String tipoAlteracao) {
        this.tipoAlteracao = tipoAlteracao;
    }

    public Date getLimite() {
        return limite;
    }

    public void setLimite(Date limite) {
        this.limite = limite;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public String getProfessorNome() {
        return professorNome;
    }

    public void setProfessorNome(String professorNome) {
        this.professorNome = professorNome;
    }

    public Integer getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(Integer horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public String getAmbienteNome() {
        return ambienteNome;
    }

    public void setAmbienteNome(String ambienteNome) {
        this.ambienteNome = ambienteNome;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }


    @Override
    public String toString() {
        return "[codigo:" + codigo + "]<br/>" +
               "[nome:" + nome + "]<br/>" +
               "[professor:" + professor + "]<br/>" +
               "[ambiente:" + ambiente + "]<br/>" +
               "[nivel:'" + nivel + "']<br/>" +
               "[horarioInicial:'" + horarioInicial + "']<br/>" +
               "[horarioFinal:'" + horarioFinal + "']<br/>" +
               "[diaAula:" + diaAula + "]<br/>" +
               "[capacidade:" + capacidade + "]<br/>" +
               "[idadeMinimaAnos:" + idadeMinimaAnos + "]<br/>" +
               "[idadeMinimaMeses:" + idadeMinimaMeses + "]<br/>" +
               "[idadeMaximaAnos:" + idadeMaximaAnos + "]<br/>" +
               "[idadeMaximaMeses:" + idadeMaximaMeses + "]<br/>" +
               "[toleranciaCheckinMinutos:" + toleranciaCheckinMinutos + "]<br/>" +
               "[horarioTurma:" + horarioTurma + "]<br/>" +
               "[toleranciaCheckinPeriodo:'" + toleranciaCheckinPeriodo + "']<br/>" +
               "[mensagem:'" + mensagem + "']<br/>" +
               "[aulaGymPass:" + aulaGymPass + "]<br/>" +
               "[aulaTotalPass:" + aulaTotalPass + "]<br/>" +
               "[validarModalidadeContratoCheckin:" + validarModalidadeContratoCheckin + "]<br/>" +
               "[controlarCheckin:" + controlarCheckin + "]<br/>" +
               "[diaSemana:'" + diaSemana + "']<br/>";
    }

    public String getDescricaoParaLog(EdicaoAulaTemporaria v2) {
        try {
            StringBuilder log = new StringBuilder();
            log.append(UtilReflection.difference(this, v2));
            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }
}
