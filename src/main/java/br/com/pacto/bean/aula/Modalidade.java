/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aula;

import br.com.pacto.bean.animacao.Animacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.enumeradores.IconesAulaCheia;
import br.com.pacto.util.enumeradores.PaletaCoresEnum;
import java.io.Serializable;
import javax.persistence.*;


/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class Modalidade implements Serializable{
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer codigoZW;
    private String nome;
    @Enumerated(EnumType.ORDINAL)
    private PaletaCoresEnum cor;
    @Transient
    private Boolean escolhido = Boolean.TRUE;
    @ManyToOne(fetch = FetchType.LAZY)
    private Animacao animacao;
    @Enumerated(EnumType.ORDINAL)
    private IconesAulaCheia icone;

    @Column(name="ativo", columnDefinition = "boolean default true")
    private Boolean ativo = Boolean.TRUE;

    public Modalidade(Integer codigoZW, String nome, PaletaCoresEnum cor) {
        this.codigoZW = codigoZW;
        this.nome = nome;
        this.cor = cor;
    }

    public Modalidade(Integer codigoZW, String nome) {
        this.codigoZW = codigoZW;
        this.nome = nome;
    }
    public Modalidade() {
    }

    public Modalidade(Integer codigo) {
        this.codigo = codigo;
    }
    
    public Modalidade(String nome) {
        this.nome = nome;
    }

    public Modalidade(String nome, PaletaCoresEnum cor) {
        this.nome = nome;
        this.cor = cor;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoZW() {
        return codigoZW;
    }

    public void setCodigoZW(Integer codigoZW) {
        this.codigoZW = codigoZW;
    }

    public String getNome() {
        if(nome == null){
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public PaletaCoresEnum getCor() {
        return cor;
    }

    public void setCor(PaletaCoresEnum cor) {
        this.cor = cor;
    }
    
    public String getNomeCss() {
        return "classeCssModalidade"+codigo;
    }

    public Boolean getEscolhido() {
        return escolhido;
    }

    public void setEscolhido(Boolean escolhido) {
        this.escolhido = escolhido;
    }

    public Animacao getAnimacao() {
        return animacao;
    }

    public void setAnimacao(Animacao animacao) {
        this.animacao = animacao;
    }

    public IconesAulaCheia getIcone() {
        return icone;
    }

    public void setIcone(IconesAulaCheia icone) {
        this.icone = icone;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }
}
