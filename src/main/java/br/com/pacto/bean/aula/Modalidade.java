/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aula;

import br.com.pacto.bean.animacao.Animacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.enumeradores.IconesAulaCheia;
import br.com.pacto.util.enumeradores.PaletaCoresEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.persistence.*;


/**
 *
 * <AUTHOR>
 */
@Entity
@Table
@ApiModel(description = "Entidade que representa uma modalidade de atividade física")
public class Modalidade implements Serializable{
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "Código único identificador da modalidade", example = "1")
    private Integer codigo;

    @ApiModelProperty(value = "Código identificador da modalidade no sistema ZW", example = "101")
    private Integer codigoZW;

    @ApiModelProperty(value = "Nome da modalidade", example = "Cardio")
    private String nome;

    @Enumerated(EnumType.ORDINAL)
    @ApiModelProperty(value = "Cor da modalidade. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- AZUL_A (0)\n" +
            "- AZUL_B (1)\n" +
            "- AZUL_C (2)\n" +
            "- AZUL_D (3)\n" +
            "- AZUL_E (4)\n" +
            "- AZUL_F (5)\n" +
            "- AZUL_G (6)\n" +
            "- AZUL_H (7)\n" +
            "- AZUL_I (8)\n" +
            "- AZUL_J (9)\n" +
            "- AZUL_ESCURO_A (10)\n" +
            "- AZUL_ESCURO_B (11)\n" +
            "- AZUL_ESCURO_C (12)\n" +
            "- AZUL_ESCURO_D (13)\n" +
            "- AZUL_ESCURO_E (14)\n" +
            "- ROXO_A (15)\n" +
            "- ROXO_B (16)\n" +
            "- ROXO_C (17)\n" +
            "- ROXO_D (18)\n" +
            "- ROXO_E (19)\n" +
            "- ROXO_F (20)\n" +
            "- ROXO_G (21)\n" +
            "- ROXO_H (22)\n" +
            "- ROXO_I (23)\n" +
            "- ROXO_J (24)\n" +
            "- VERMELHO_A (25)\n" +
            "- VERMELHO_B (26)\n" +
            "- VERMELHO_C (27)\n" +
            "- VERMELHO_D (28)\n" +
            "- VERMELHO_E (29)\n" +
            "- VERMELHO_F (30)\n" +
            "- VERMELHO_G (31)\n" +
            "- VERMELHO_H (32)\n" +
            "- VERMELHO_I (33)\n" +
            "- VERMELHO_J (34)\n" +
            "- LARANJA_A (35)\n" +
            "- LARANJA_B (36)\n" +
            "- LARANJA_C (37)\n" +
            "- LARANJA_D (38)\n" +
            "- LARANJA_E (39)\n" +
            "- LARANJA_F (40)\n" +
            "- LARANJA_G (41)\n" +
            "- LARANJA_H (42)\n" +
            "- LARANJA_I (43)\n" +
            "- LARANJA_J (44)\n" +
            "- MARROM_A (45)\n" +
            "- MARROM_B (46)\n" +
            "- MARROM_C (47)\n" +
            "- MARROM_D (48)\n" +
            "- MARROM_E (49)\n" +
            "- MARROM_F (50)\n" +
            "- MARROM_G (51)\n" +
            "- MARROM_H (52)\n" +
            "- MARROM_I (53)\n" +
            "- MARROM_J (54)\n" +
            "- VERDE_A (55)\n" +
            "- VERDE_B (56)\n" +
            "- VERDE_C (57)\n" +
            "- VERDE_D (58)\n" +
            "- VERDE_E (59)\n" +
            "- VERDE_F (60)\n" +
            "- VERDE_G (61)\n" +
            "- VERDE_H (62)\n" +
            "- VERDE_I (63)\n" +
            "- VERDE_J (64)\n" +
            "- VERDE_LIMAO_A (65)\n" +
            "- VERDE_LIMAO_B (66)\n" +
            "- VERDE_LIMAO_C (67)\n" +
            "- VERDE_LIMAO_D (68)\n" +
            "- VERDE_LIMAO_E (69)\n" +
            "- AMARELO_A (70)\n" +
            "- AMARELO_B (71)\n" +
            "- AMARELO_C (72)\n" +
            "- AMARELO_D (73)\n" +
            "- AMARELO_E (74)\n", example = "AZUL_E")
    private PaletaCoresEnum cor;

    @Transient
    @ApiModelProperty(value = "Indica se a modalidade está escolhida/selecionada", example = "true")
    private Boolean escolhido = Boolean.TRUE;

    @ManyToOne(fetch = FetchType.LAZY)
    @ApiModelProperty(value = "Referência à animação associada à modalidade")
    private Animacao animacao;

    @Enumerated(EnumType.ORDINAL)
    @ApiModelProperty(value = "Ícone da modalidade para aulas cheias. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- ICONE_1 (basket32.png)\n" +
            "- ICONE_2 (bodyparts70.png)\n" +
            "- ICONE_3 (bodyparts71.png)\n" +
            "- ICONE_4 (dumbbell.png)\n" +
            "- ICONE_5 (dumbbell1.png)\n" +
            "- ICONE_6 (dumbbell14.png)\n" +
            "- ICONE_7 (dumbbell16.png)\n" +
            "- ICONE_8 (dumbbell19.png)\n" +
            "- ICONE_9 (dumbbells10.png)\n" +
            "- ICONE_10 (dumbbells7.png)\n" +
            "- ICONE_11 (exercise1.png)\n" +
            "- ICONE_12 (exercise2.png)\n" +
            "- ICONE_13 (exercise29.png)\n" +
            "- ICONE_14 (exercise35.png)\n" +
            "- ICONE_15 (exercise45.png)\n" +
            "- ICONE_16 (exercise5.png)\n" +
            "- ICONE_17 (exercise54.png)\n" +
            "- ICONE_18 (exercise55.png)\n" +
            "- ICONE_19 (exercise6.png)\n" +
            "- ICONE_20 (exercise65.png)\n" +
            "- ICONE_21 (exercise7.png)\n" +
            "- ICONE_22 (exercise8.png)\n" +
            "- ICONE_23 (female107.png)\n" +
            "- ICONE_24 (flexions.png)\n" +
            "- ICONE_25 (gym11.png)\n" +
            "- ICONE_26 (gym20.png)\n" +
            "- E mais ícones até ICONE_63 (yoga230.png)", example = "ICONE_4")
    private IconesAulaCheia icone;

    @Column(name="ativo", columnDefinition = "boolean default true")
    @ApiModelProperty(value = "Indica se a modalidade está ativa no sistema", example = "true")
    private Boolean ativo = Boolean.TRUE;

    public Modalidade(Integer codigoZW, String nome, PaletaCoresEnum cor) {
        this.codigoZW = codigoZW;
        this.nome = nome;
        this.cor = cor;
    }

    public Modalidade(Integer codigoZW, String nome) {
        this.codigoZW = codigoZW;
        this.nome = nome;
    }
    public Modalidade() {
    }

    public Modalidade(Integer codigo) {
        this.codigo = codigo;
    }
    
    public Modalidade(String nome) {
        this.nome = nome;
    }

    public Modalidade(String nome, PaletaCoresEnum cor) {
        this.nome = nome;
        this.cor = cor;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoZW() {
        return codigoZW;
    }

    public void setCodigoZW(Integer codigoZW) {
        this.codigoZW = codigoZW;
    }

    public String getNome() {
        if(nome == null){
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public PaletaCoresEnum getCor() {
        return cor;
    }

    public void setCor(PaletaCoresEnum cor) {
        this.cor = cor;
    }
    
    public String getNomeCss() {
        return "classeCssModalidade"+codigo;
    }

    public Boolean getEscolhido() {
        return escolhido;
    }

    public void setEscolhido(Boolean escolhido) {
        this.escolhido = escolhido;
    }

    public Animacao getAnimacao() {
        return animacao;
    }

    public void setAnimacao(Animacao animacao) {
        this.animacao = animacao;
    }

    public IconesAulaCheia getIcone() {
        return icone;
    }

    public void setIcone(IconesAulaCheia icone) {
        this.icone = icone;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }
}
