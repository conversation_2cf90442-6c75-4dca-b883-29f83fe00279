package br.com.pacto.bean.programa;

import br.com.pacto.objeto.Calendario;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.sql.Timestamp;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;

/**
 * Created by ulisses on 13/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Objeto de transferência para criação e alteração de programas de treino")
public class ProgramaTreinoTO {

    @ApiModelProperty(value = "ID único do programa de treino", example = "123")
    private Integer id;

    @ApiModelProperty(value = "ID do aluno associado ao programa", example = "456")
    private Integer alunoId;

    @ApiModelProperty(value = "ID do colaborador responsável pelo programa", example = "789")
    private Integer colaboradorId;

    @ApiModelProperty(value = "Nome do programa de treino", example = "Programa Hipertrofia - João Silva")
    private String nome;

    @ApiModelProperty(value = "ID do professor que montou o programa", example = "101")
    private Integer professorId;

    @ApiModelProperty(value = "Data de início do programa em timestamp", example = "1640995200000")
    private Long inicio;

    @ApiModelProperty(value = "Data de término do programa em timestamp", example = "1648771200000")
    private Long termino;

    @ApiModelProperty(value = "Total de treinos previstos no programa", example = "36")
    private Integer totalTreinos;

    @ApiModelProperty(value = "Quantidade de dias por semana que o programa deve ser executado", example = "3")
    private Integer qtdDiasSemana;

    @ApiModelProperty(value = "Gênero do programa de treino. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- M (Masculino)\n" +
            "- F (Feminino)\n", example = "M")
    private String genero;

    @ApiModelProperty(value = "Indica se o programa é pré-definido", example = "false")
    private Boolean predefinido;

    @ApiModelProperty(value = "Indica se o programa está em revisão pelo professor", example = "false")
    private Boolean emRevisaoProfessor = false;

    @ApiModelProperty(value = "Indica se o programa foi gerado por Inteligência Artificial", example = "false")
    private Boolean isGeradoPorIA = false;
    private Integer professorMontou;

    public Integer getId() { return id; }

    public void setId(Integer id) { this.id = id; }

    public Integer getAlunoId() {
        return alunoId;
    }

    public void setAlunoId(Integer alunoId) {
        this.alunoId = alunoId;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getProfessorId() {
        return professorId;
    }

    public void setProfessorId(Integer professorId) {
        this.professorId = professorId;
    }

    public Long getInicio() {
        return inicio;
    }

    public Date getInicioComoData() throws ParseException {
        return new Date(this.inicio);
    }

    public void setInicio(Long inicio) {
        this.inicio = inicio;
    }

    public Long getTermino() {
        return termino;
    }

    public Date getTerminoComoData() throws ParseException {
        return new Date(this.termino);
    }

    public void setTermino(Long termino) {
        this.termino = termino;
    }

    public Integer getTotalTreinos() {
        return totalTreinos;
    }

    public void setTotalTreinos(Integer totalTreinos) {
        this.totalTreinos = totalTreinos;
    }

    public Integer getQtdDiasSemana() {
        return qtdDiasSemana;
    }

    public void setQtdDiasSemana(Integer qtdDiasSemana) {
        this.qtdDiasSemana = qtdDiasSemana;
    }

    public String getGenero() {
        return genero;
    }

    public void setGenero(String genero) {
        this.genero = genero;
    }

    public Boolean getPredefinido() {
        return predefinido;
    }

    public void setPredefinido(Boolean predefinido) {
        this.predefinido = predefinido;
    }

    public Integer getColaboradorId() {
        return colaboradorId;
    }

    public void setColaboradorId(Integer colaboradorId) {
        this.colaboradorId = colaboradorId;
    }

    public Boolean getEmRevisaoProfessor() {
        return emRevisaoProfessor;
    }

    public Boolean getGeradoPorIA() {
        return isGeradoPorIA;
    }

    public void setGeradoPorIA(Boolean geradoPorIA) {
        isGeradoPorIA = geradoPorIA;
    }

    public void setEmRevisaoProfessor(Boolean emRevisaoProfessor) {
        this.emRevisaoProfessor = emRevisaoProfessor;
    }

    public Integer getProfessorMontou() { return professorMontou; }

    public void setProfessorMontou(Integer professorMontou) { this.professorMontou = professorMontou; }
}
