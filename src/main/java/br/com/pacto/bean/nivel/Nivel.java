/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.nivel;

import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.atividade.AtividadeNivel;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.serie.Serie;
import br.com.pacto.util.impl.UtilReflection;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import static java.util.Objects.isNull;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(uniqueConstraints =
        @UniqueConstraint(columnNames = {"nome"}))
public class Nivel implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    private Integer ordem;
    @Column(columnDefinition = "boolean DEFAULT true")
    private Boolean ativo;
    @OneToMany(targetEntity = AtividadeNivel.class, cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, orphanRemoval = true, fetch = FetchType.LAZY, mappedBy = "nivel")
    private List<AtividadeNivel> niveis = new ArrayList<AtividadeNivel>();

    public Nivel() {
    }

    public Nivel(Integer nivel) {
        this.codigo = nivel;
    }

    public Nivel(String nome, Integer ordem) {
        this.nome = nome;
        this.ordem = ordem;
    }

    public String getDescricaoOrdem() {
        try {
            return ordem.toString();
        } catch (Exception e) {
            return "";
        }

    }

    public List<AtividadeNivel> getNiveis() {
        return niveis;
    }

    public void setNiveis(List<AtividadeNivel> niveis) {
        this.niveis = niveis;
    }

    public Integer getOrdem() {
        if (ordem == null) {
            ordem = 0;
        }
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public Nivel(final String nome) {
        this.nome = nome;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public String getNomeMinusculo() {
        try {
            return nome.toLowerCase();
        }catch (Exception e){
            return "";
        }

    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public String getDescricaoParaLog(Nivel v2) {
        try {
            StringBuilder log = new StringBuilder();
            log.append(UtilReflection.difference(this, v2));
            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }
}


