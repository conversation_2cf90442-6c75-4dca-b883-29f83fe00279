package br.com.pacto.bean.usuario;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.bean.pessoa.Pessoa;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuarioEmail.UsuarioEmail;
import br.com.pacto.bean.wod.ScoreTreino;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONException;
import org.json.JSONObject;
import servicos.integracao.adm.client.EmpresaWS;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Entity(name = "Usuario")
@Table(uniqueConstraints = {
    @UniqueConstraint(name = "usuario_professor_codigo_key", columnNames = {"professor_codigo", "cliente_codigo"})})
public class Usuario implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;
    @Id
    @Column(name = "codigo")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Column(name = "username")
    private String userName;
    @Column(name = "senha")
    private String senha;
    @Enumerated(EnumType.ORDINAL)
    private StatusEnum status;
    private String nome;
    @Transient
    private String chave;
    @Column(name = "tipo")
    @Enumerated(EnumType.ORDINAL)
    private TipoUsuarioEnum tipo;
    @ManyToOne(fetch = FetchType.LAZY)
    private ClienteSintetico cliente;
    @ManyToOne(fetch = FetchType.EAGER)
    private ProfessorSintetico professor;
    @OneToOne(mappedBy = "usuario", fetch = FetchType.LAZY,cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE})
    private UsuarioEmail usuarioEmail;
    @OneToMany(mappedBy = "usuario", cascade = CascadeType.REMOVE)
    private List<ScoreTreino> scoresTreinos;
    @Transient
    private String avatar;
    @Transient
    private String avatarEmpresa;
    @Transient
    private String avatarEmpresaApp;
    @Transient
    private String token;
    private String codigoExterno;
    @Transient
    private String modulos;
    private Integer usuarioZW;
    private Integer empresaZW;
    @Transient
    private transient List<EmpresaWS> empresasZW;
    @Transient
    private transient EmpresaWS empresaLogada;
    @Transient
    private transient String pwdForCookie;
    @Transient
    private transient String sessionId;
    @Transient
    private String timeZone = "America/Sao_Paulo";
    @Transient
    private String locale = "pt_BR";
    @ManyToOne
    private Perfil perfil;
    @Transient
    private String senhaAlterar;
    @Transient
    private Integer tipoCodigo;
    @Transient
    private Integer perfilCodigo;
    @Transient
    private String nomeEmpresa;
    @Transient
    private String urlSite;
    @Transient
    private String urlHomeBackground640x551 = "";
    @Transient
    private String urlHomeBackground320x276 = "";
    @Transient
    private Boolean usaSalaCheia = Boolean.FALSE;
    private String fotoKeyApp; //IMAGEM PARA O APP
    private Integer versaoFotoApp;
    private String cpf;
    @Transient
    private String msgWhatsapp;
    private String usuarioGeral;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataRegistroUsoApp;
    private String versaoDoApp;
    private String idClienteApp;
    private String appUtilizado;

    @Column(name = "email", unique = true)
    private String email;

    private String celular;

    public String getSenhaAlterar() {
        return senhaAlterar;
    }

    public void setSenhaAlterar(String senhaAlterar) {
        this.senhaAlterar = senhaAlterar;
    }

    public String getNomeAbreviado() {
        return Uteis.getNomeAbreviado(getNome());
    }

    public String getNomeProfessor() {
        try {
            return getProfessor().getNome();
        } catch (Exception e) {
            return "";
        }
    }

    public Usuario() {

    }
    public Usuario(String userName, Integer codigoCliente, String situacao) {
        this.userName = userName;
        this.cliente = new ClienteSintetico();
        this.cliente.setCodigo(codigoCliente);
        this.cliente.setSituacao(situacao);
    }

    public Usuario(Integer codigo, String userName, Integer professorCodigo, String pessoaNome) {
        this.codigo = codigo;
        this.userName = userName;
        this.professor = new ProfessorSintetico(professorCodigo, new Pessoa());
        this.professor.getPessoa().setNome(pessoaNome);
    }

    public String getNomeProfessorAbreviado() {
        try {
            return getProfessor().getNomeAbreviado();
        } catch (Exception e) {
            return "";
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getUserName() {
        return userName;
    }

    public String getUserNameApresentar() {
        try {
            return userName.contains("@") ? userName + ";" : "";
        } catch (Exception e) {
            return "";
        }

    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public StatusEnum getStatus() {
        return status;
    }

    public void setStatus(StatusEnum status) {
        this.status = status;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public TipoUsuarioEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoUsuarioEnum tipo) {
        this.tipo = tipo;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public ProfessorSintetico getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorSintetico professor) {
        this.professor = professor;
    }

    public Integer getIdPessoa() {
        if (cliente != null) {
            return cliente.getCodigoPessoa();
        } else if (professor != null) {
            return professor.getCodigoPessoa();
        } else {
            return 0;
        }
    }

    public String getSuperNome() {
        if (getCliente() != null) {
            return getCliente().getNome();
        } else if (getProfessor() != null) {
            return getProfessor().getNome();
        } else {
            return getNome();
        }
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getCodigoExterno() {
        return codigoExterno;
    }

    public void setCodigoExterno(String codigoExterno) {
        this.codigoExterno = codigoExterno;
    }

    public String getModulos() {
        return modulos;
    }

    public void setModulos(String modulos) {
        this.modulos = modulos;
    }

    public Integer getUsuarioZW() {
        return usuarioZW;
    }

    public void setUsuarioZW(Integer usuarioZW) {
        this.usuarioZW = usuarioZW;
    }

    public Integer getEmpresaZW() {
        return empresaZW;
    }

    public void setEmpresaZW(Integer empresaZW) {
        this.empresaZW = empresaZW;
    }

    public List<EmpresaWS> getEmpresasZW() {
        return empresasZW;
    }

    public void setEmpresasZW(List<EmpresaWS> empresasZW) {
        this.empresasZW = empresasZW;
    }

    public EmpresaWS getEmpresaLogada() {
        return empresaLogada;
    }

    public void setEmpresaLogada(EmpresaWS empresaLogada) {
        this.empresaLogada = empresaLogada;
    }

    public String getPwdForCookie() {
        return pwdForCookie;
    }

    public void setPwdForCookie(String pwdForCookie) {
        this.pwdForCookie = pwdForCookie;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getAvatarEmpresa() {
        return avatarEmpresa;
    }

    public void setAvatarEmpresa(String avatarEmpresa) {
        this.avatarEmpresa = avatarEmpresa;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public Perfil getPerfil() {
        return perfil;
    }

    public void setPerfil(Perfil perfil) {
        this.perfil = perfil;
    }

    public boolean isItemHabilitado(final String name, final String tipoPermissao) {
        if (tipo == TipoUsuarioEnum.ROOT) {
            return true;
        } else {
            return perfil.isItemHabilitado(name, tipoPermissao);
        }
    }

    public boolean isFuncionalidadeHabilitado(final String name) {
        try {
            if (tipo == TipoUsuarioEnum.ROOT) {
                return true;
            } else {
                return perfil.isItemHabilitado(name, TipoPermissaoEnum.TOTAL.name());
            }
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isCadastroHabilitado() {
        if (tipo == TipoUsuarioEnum.ROOT) {
            return true;
        } else {
            return perfil.isItemHabilitado(RecursoEnum.MUSCULOS.toString(), TipoPermissaoEnum.CONSULTAR.toString())
                    || perfil.isItemHabilitado(RecursoEnum.GRUPOS_MUSCULARES.toString(), TipoPermissaoEnum.CONSULTAR.toString())
                    || perfil.isItemHabilitado(RecursoEnum.APARELHOS.toString(), TipoPermissaoEnum.CONSULTAR.toString())
                    || perfil.isItemHabilitado(RecursoEnum.ATIVIDADES.toString(), TipoPermissaoEnum.CONSULTAR.toString())
                    || perfil.isItemHabilitado(RecursoEnum.FICHAS_PRE_DEFINIDAS.toString(), TipoPermissaoEnum.CONSULTAR.toString())
                    || perfil.isItemHabilitado(RecursoEnum.CATEGORIA_ATIVIDADE.toString(), TipoPermissaoEnum.CONSULTAR.toString())
                    || perfil.isItemHabilitado(RecursoEnum.CATEGORIA_FICHAS.toString(), TipoPermissaoEnum.CONSULTAR.toString())
                    || perfil.isItemHabilitado(RecursoEnum.NIVEIS.toString(), TipoPermissaoEnum.CONSULTAR.toString())
                    || perfil.isItemHabilitado(RecursoEnum.OBJETIVOS.toString(), TipoPermissaoEnum.CONSULTAR.toString())
                    || perfil.isItemHabilitado(RecursoEnum.IMAGENS.toString(), TipoPermissaoEnum.CONSULTAR.toString());
        }
    }

    public boolean isCadastroHabilitadoModuloAula() {
        if (tipo == TipoUsuarioEnum.ROOT) {
            return true;
        } else {
            return perfil.isItemHabilitado(RecursoEnum.CADASTRO_AULAS.toString(), TipoPermissaoEnum.CONSULTAR.name())
                    || perfil.isItemHabilitado(RecursoEnum.INSERIR_ALUNO.toString(), TipoPermissaoEnum.CONSULTAR.name())
                    || perfil.isItemHabilitado(RecursoEnum.EXCLUIR_ALUNO.toString(), TipoPermissaoEnum.CONSULTAR.name());
        }
    }

    public boolean isPermiteLoginWeb() {
        return this.getTipo() == TipoUsuarioEnum.COORDENADOR
                || this.getTipo() == TipoUsuarioEnum.PROFESSOR
                || this.getTipo() == TipoUsuarioEnum.ROOT
                || this.getTipo() == TipoUsuarioEnum.CONSULTOR;
    }

    public EmpresaWS getEmpresaDefault() {
        if (empresasZW != null && !empresasZW.isEmpty()) {
            for (EmpresaWS emp : empresasZW) {
                if (emp.getCodigo().intValue() == this.empresaZW) {
                    return emp;
                }
            }
        }
        return UteisValidacao.emptyList(empresasZW) ? null : empresasZW.get(0);
    }

    public String getNomeApresentar() {
        if (this.getCliente() != null) {
            return this.getCliente().getNome();
        } else if (this.getProfessor() != null) {
            return this.professor.getNome();
        } else {
            return this.getNome();
        }
    }

    public String getNomeApresentarLower() {
        try {
            if (this.getCliente() != null) {
                return this.getCliente().getNome().toLowerCase();
            } else if (this.getProfessor() != null) {
                return this.getProfessor().getNome().toLowerCase();
            } else {
                return this.getNome().toLowerCase();
            }
        }catch (Exception e){
            return this.getNome().toLowerCase();
        }
    }

    public String toJSON() throws JSONException {
        JSONObject json = new JSONObject();
        json.put("id", this.codigo);
        json.put("k", this.chave);
        json.put("un", this.userName);
        json.put("pwd", this.pwdForCookie);
        json.put("sessionid", this.getSessionId());
        return json.toString();
    }
    public String toJSONSimples() throws JSONException {
        JSONObject json = new JSONObject();
        json.put("k", this.chave);
        json.put("un", this.userName);
        json.put("pwd", this.pwdForCookie);
        return json.toString();
    }

    public Integer getTipoCodigo() {
        return tipoCodigo;
    }

    public void setTipoCodigo(Integer tipoCodigo) {
        this.tipoCodigo = tipoCodigo;
    }

    public Integer getPerfilCodigo() {
        return perfilCodigo;
    }

    public void setPerfilCodigo(Integer perfilCodigo) {
        this.perfilCodigo = perfilCodigo;
    }

    public String getNomeEmpresa() {
        if (nomeEmpresa == null) {
            nomeEmpresa = "";
        }
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getAvatarEmpresaApp() {
        return avatarEmpresaApp;
    }

    public void setAvatarEmpresaApp(String avatarEmpresaApp) {
        this.avatarEmpresaApp = avatarEmpresaApp;
    }

    public String getUrlHomeBackground640x551() {
        return urlHomeBackground640x551;
    }

    public void setUrlHomeBackground640x551(String urlHomeBackground640x551) {
        this.urlHomeBackground640x551 = urlHomeBackground640x551;
    }

    public String getUrlHomeBackground320x276() {
        return urlHomeBackground320x276;
    }

    public void setUrlHomeBackground320x276(String urlHomeBackground320x276) {
        this.urlHomeBackground320x276 = urlHomeBackground320x276;
    }

    public Boolean getUsaSalaCheia() {
        return usaSalaCheia;
    }

    public void setUsaSalaCheia(Boolean usaSalaCheia) {
        this.usaSalaCheia = usaSalaCheia;
    }

    public UsuarioEmail getUsuarioEmail() {
        return usuarioEmail;
    }

    public void setUsuarioEmail(UsuarioEmail usuarioEmail) {
        this.usuarioEmail = usuarioEmail;
    }

    public String getUrlSite() {
        return urlSite;
}

    public void setUrlSite(String urlSite) {
        this.urlSite = urlSite;
    }

    public String getFotoKeyApp() {
        if(fotoKeyApp == null){
            fotoKeyApp = "";
        }
        if(!fotoKeyApp.contains("http") && !fotoKeyApp.isEmpty()){
            Calendar calendar = Calendar.getInstance();
            fotoKeyApp = Aplicacao.obterUrlFotoDaNuvem(fotoKeyApp) +"?"+calendar.getTimeInMillis();
        }
        return fotoKeyApp;
    }

    public void setFotoKeyApp(String fotoKeyApp) {
        this.fotoKeyApp = fotoKeyApp;
    }

    public Integer getVersaoFotoApp() {
        if (versaoFotoApp == null) {
            versaoFotoApp = 0;
        }
        return versaoFotoApp;
    }

    public void setVersaoFotoApp(Integer versaoFotoApp) {
        this.versaoFotoApp = versaoFotoApp;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getMsgWhatsapp() {
        if (msgWhatsapp == null) {
            msgWhatsapp = "";
        }
        return msgWhatsapp;
    }

    public void setMsgWhatsapp(String msgWhatsapp) {
        this.msgWhatsapp = msgWhatsapp;
    }

    public String getUsuarioGeral() {
        return usuarioGeral;
    }

    public void setUsuarioGeral(String usuarioGeral) {
        this.usuarioGeral = usuarioGeral;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCelular() {
        return celular;
    }

    public void setCelular(String celular) {
        this.celular = celular;
    }

    public Date getDataRegistroUsoApp() {
        return dataRegistroUsoApp;
    }

    public void setDataRegistroUsoApp(Date dataRegistroUsoApp) {
        this.dataRegistroUsoApp = dataRegistroUsoApp;
    }

    public String getVersaoDoApp() {
        return versaoDoApp;
    }

    public void setVersaoDoApp(String versaoDoApp) {
        this.versaoDoApp = versaoDoApp;
    }

    public String getIdClienteApp() {
        return idClienteApp;
    }

    public void setIdClienteApp(String idClienteApp) {
        this.idClienteApp = idClienteApp;
    }

    public String getAppUtilizado() {
        return appUtilizado;
    }

    public void setAppUtilizado(String appUtilizado) {
        this.appUtilizado = appUtilizado;
    }
}
