/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.programa;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.impl.EntityManagerService;
import br.com.pacto.base.jpa.service.impl.PovoadorAtividadeServiceImpl;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.base.util.Propagador;
import br.com.pacto.bean.agenda.TipoLembreteEnum;
import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeAlternativa;
import br.com.pacto.bean.atividade.AtividadeAnimacao;
import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.atividade.AtividadeFichaAjuste;
import br.com.pacto.bean.atividade.AtividadeGrupoMuscular;
import br.com.pacto.bean.atividade.AtividadeImagemUploadTO;
import br.com.pacto.bean.atividade.AtividadeVideo;
import br.com.pacto.bean.atividade.AtividadeVideoTO;
import br.com.pacto.bean.atividade.MetodoExecucaoEnum;
import br.com.pacto.bean.atividade.TipoAtividadeEnum;
import br.com.pacto.bean.avaliacao.AnamneseTreinoPorIADTO;
import br.com.pacto.bean.avaliacao.evolucao.EvolucaoFisicaDTO;
import br.com.pacto.bean.avaliacao.evolucao.EvolucaoGrupoTrabalhadoDTO;
import br.com.pacto.bean.avaliacao.evolucao.GrupoTrabalhadoItemDTO;
import br.com.pacto.bean.badge.Badge;
import br.com.pacto.bean.cliente.AcaoAlunoEnum;
import br.com.pacto.bean.cliente.ClienteAcompanhamento;
import br.com.pacto.bean.cliente.ClienteAcompanhamentoResponseTO;
import br.com.pacto.bean.cliente.ClienteBadge;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.configuracoes.TimeZoneEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.ficha.AtividadeFichaResponseTO;
import br.com.pacto.bean.ficha.CategoriaFicha;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.ficha.FichaDTO;
import br.com.pacto.bean.ficha.FichaResponseTO;
import br.com.pacto.bean.ficha.SerieResponseTO;
import br.com.pacto.bean.musculo.GrupoMuscular;
import br.com.pacto.bean.musculo.PontosMuscularEnum;
import br.com.pacto.bean.notificacao.Notificacao;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.bean.pessoa.Pessoa;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.Compromisso;
import br.com.pacto.bean.programa.HistoricoRevisaoProgramaTreino;
import br.com.pacto.bean.programa.ObjetivoPredefinido;
import br.com.pacto.bean.programa.ObjetivoPrograma;
import br.com.pacto.bean.programa.OrigemExecucaoEnum;
import br.com.pacto.bean.programa.ProgramaSituacaoEnum;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoAndamento;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.bean.programa.ProgramaTreinoResponseTO;
import br.com.pacto.bean.programa.ProgramaTreinoResumo;
import br.com.pacto.bean.programa.ProgramaTreinoTO;
import br.com.pacto.bean.programa.RestricoesEnum;
import br.com.pacto.bean.programa.TipoExecucaoEnum;
import br.com.pacto.bean.serie.Serie;
import br.com.pacto.bean.serie.SerieEndpointTO;
import br.com.pacto.bean.serie.SerieRealizada;
import br.com.pacto.bean.serie.SerieTO;
import br.com.pacto.bean.serie.TreinoRealizado;
import br.com.pacto.bean.sincronizacao.HistoricoRevisao;
import br.com.pacto.bean.sincronizacao.TipoClassSincronizarEnum;
import br.com.pacto.bean.sincronizacao.TipoObjetoSincronizarEnum;
import br.com.pacto.bean.sincronizacao.TipoRevisaoEnum;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.aluno.AlunoResponseTO;
import br.com.pacto.controller.json.aluno.DetalheTreinoAlunoDTO;
import br.com.pacto.controller.json.aluno.DistribuicaoMusculoDTO;
import br.com.pacto.controller.json.aluno.FichaExecutada;
import br.com.pacto.controller.json.aluno.PorcentagemDiaSemana;
import br.com.pacto.controller.json.aluno.ProgramaTreinoAlunoResponseDTO;
import br.com.pacto.controller.json.atividade.AtividadeAlternativaDTO;
import br.com.pacto.controller.json.atividade.AtividadeGeradaPorIADTO;
import br.com.pacto.controller.json.atividade.AtividadeTreinoGeradoPorIADTO;
import br.com.pacto.controller.json.aulaDia.AulaAlunoDTO;
import br.com.pacto.controller.json.aulaDia.HistoricoTreinosVO;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.ficha.FichaDeTreinoGeradaPorIADTO;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.controller.json.notificacao.PushMobileRunnable;
import br.com.pacto.controller.json.programa.*;
import br.com.pacto.controller.json.programa.read.ObjetivoProgramaJSON;
import br.com.pacto.controller.json.programa.read.OrigemEnum;
import br.com.pacto.controller.json.programa.read.ProgramaTreinoJSON;
import br.com.pacto.controller.json.programa.read.ProgramaVersaoJSON;
import br.com.pacto.controller.json.programa.write.ProgramaWriteAppJSON;
import br.com.pacto.controller.json.programa.write.ProgramaWriteJSON;
import br.com.pacto.dao.intf.atividade.*;
import br.com.pacto.dao.intf.cliente.ClienteBadgeDao;
import br.com.pacto.dao.intf.ficha.CategoriaFichaDao;
import br.com.pacto.dao.intf.ficha.FichaDao;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.dao.intf.musculo.GrupoMuscularDao;
import br.com.pacto.dao.intf.notificacao.NotificacaoDao;
import br.com.pacto.dao.intf.professor.ProfessorSinteticoDao;
import br.com.pacto.dao.intf.programa.CompromissoDao;
import br.com.pacto.dao.intf.programa.HistoricoRevisaoProgramaTreinoDao;
import br.com.pacto.dao.intf.programa.ObjetivoPredefinidoDao;
import br.com.pacto.dao.intf.programa.ObjetivoProgramaDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoAndamentoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoFichaDao;
import br.com.pacto.dao.intf.serie.SerieDao;
import br.com.pacto.dao.intf.serie.SerieRealizadaDao;
import br.com.pacto.dao.intf.serie.TreinoRealizadoDao;
import br.com.pacto.dao.intf.sincronizacao.HistoricoRevisaoDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.ColecaoUtils;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.objeto.to.AtividadeFichaDTO;
import br.com.pacto.objeto.to.AtividadeFichaEndpointTO;
import br.com.pacto.objeto.to.AtividadeFichaTO;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.agenda.AgendaModoBDServiceImpl;
import br.com.pacto.service.impl.atividade.SerieGeradaPorIADTO;
import br.com.pacto.service.impl.cliente.ClienteSinteticoServiceImpl;
import br.com.pacto.service.impl.cliente.perfil.HorariosQueTreinouProgramaAtual;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.AlunoExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.AtividadeFichaExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.ProgramaTreinoExcecoes;
import br.com.pacto.service.intf.atividade.AtividadeService;
import br.com.pacto.service.intf.badge.BadgeService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.cliente.HistoricoPresencasVO;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.ficha.FichaService;
import br.com.pacto.service.intf.ficha.FilaImpressaoService;
import br.com.pacto.service.intf.ficha.SerieService;
import br.com.pacto.service.intf.logErros.LogErrosService;
import br.com.pacto.service.intf.memcached.CachedManagerInterfaceFacade;
import br.com.pacto.service.intf.notificacao.NotificacaoService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.programa.ObjetivoPredefinidoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.service.intf.usuario.FotoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.bean.GenericoTO;
import br.com.pacto.util.enumeradores.DiasSemana;
import br.com.pacto.util.impl.AuditUtilities;
import br.com.pacto.util.impl.JSFUtilities;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.impl.UtilReflection;
import br.com.pacto.validacao.intf.ficha.FichaValidacaoService;
import br.com.pacto.validacao.intf.programa.ProgramaTreinoValidacao;
import com.google.gson.Gson;
import org.apache.commons.collections.Predicate;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.hibernate.Hibernate;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.ContextConfiguration;
import org.apache.http.HttpStatus;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.Normalizer;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static br.com.pacto.objeto.Uteis.incluirLog;
import static java.util.Objects.isNull;

/**
 * <AUTHOR>
 */
@Service
@ContextConfiguration(locations = {"/applicationContext.xml"})
public class ProgramaTreinoServiceImpl implements ProgramaTreinoService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private ProgramaTreinoValidacao validacao;
    @Autowired
    private ProgramaTreinoDao programatreinoDao;
    @Autowired
    private CompromissoDao compromissoDao;
    @Autowired
    private ObjetivoPredefinidoDao objetivoPredefinidoDao;
    @Autowired
    private HistoricoRevisaoProgramaTreinoDao historicoRevisaoDao;
    @Autowired
    private CategoriaFichaDao categoriaFichaDao;
    @Autowired
    private AtividadeDao atividadeDao;
    @Autowired
    private AtividadeAlternativaDao atividadeAlternativaDao;
    @Autowired
    private TreinoRealizadoDao treinoRealizadoDao;
    @Autowired
    private SerieDao serieDao;
    @Autowired
    private SerieRealizadaDao serieRealizadaDao;
    @Autowired
    private AtividadeFichaDao atividadeFichaDao;
    @Autowired
    private ProgramaTreinoFichaDao programaTreinoFichaDao;
    @Autowired
    private NotificacaoService notfService;
    @Autowired
    private NotificacaoDao notDao;
    @Autowired
    private ProgramaTreinoAndamentoDao programaTreinoAndamentoDao;
    @Autowired
    private ClienteBadgeDao clienteBadge;
    @Autowired
    private ClienteSinteticoService cs;
    @Autowired
    private BadgeService bs;
    @Autowired
    private ProgramaTreinoFichaDao programatreinofichaDao;
    @Autowired
    private ProfessorSinteticoService professorService;
    @Autowired
    private ObjetivoPredefinidoService objetivoService;
    @Autowired
    private ObjetivoProgramaDao objetivoProgramaDao;
    @Autowired
    private FichaService fichaService;
    @Autowired
    private SerieService serieService;
    @Autowired
    private FichaValidacaoService fichaValidacaoService;
    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;
    @Autowired
    private FichaDao fichaDao;
    @Autowired
    private HistoricoRevisaoDao historicoRevisaoSincronizacaoDao;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private FilaImpressaoService filaImpressaoService;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private ProfessorSinteticoDao professorSinteticoDao;
    @Autowired
    private FotoService fotoService;
    @Autowired
    private LogDao logDao;
    @Autowired
    private LogErrosService logErrosService;
    @Autowired
    private ConexaoZWService conexaoZWService;
    @Autowired
    private AtividadeService atividadeService;
    @Autowired
    private AtividadeVideoDao atividadeVideoDao;
    @Autowired
    private GrupoMuscularDao grupoMuscularDao;
    @Autowired
    private AtividadeGrupoMuscularDao atividadeGrupoMuscularDao;
    @Autowired
    AtividadeAnimacaoDao atividadeAnimacaoDao;
    @Autowired
    private CachedManagerInterfaceFacade memcached;
    @Autowired
    private ProfessorSinteticoService professorSinteticoService;

    private static final int MAXIMO_ATIVIDADES_LISTAR = 50;
    private static final String treinoIaUsername = "TreinoIA";
    private static final String SEM_REDE_FRANQUEADORA = "SEM REDE FRANQUEADORA";

    public ProgramaTreinoFichaDao getProgramatreinofichaDao() {
        return programatreinofichaDao;
    }

    public void setProgramatreinofichaDao(ProgramaTreinoFichaDao programatreinofichaDao) {
        this.programatreinofichaDao = programatreinofichaDao;
    }

    public NotificacaoService getNotfService() {
        return notfService;
    }

    public void setNotfService(NotificacaoService notfService) {
        this.notfService = notfService;
    }

    public ProgramaTreinoAndamentoDao getProgramaTreinoAndamentoDao() {
        return programaTreinoAndamentoDao;
    }

    public void setProgramaTreinoAndamentoDao(ProgramaTreinoAndamentoDao programaTreinoAndamentoDao) {
        this.programaTreinoAndamentoDao = programaTreinoAndamentoDao;
    }

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public ProgramaTreinoValidacao getValidacao() {
        return validacao;
    }

    public void setValidacao(ProgramaTreinoValidacao validacao) {
        this.validacao = validacao;
    }

    public ProgramaTreinoDao getProgramaTreinoDao() {
        return this.programatreinoDao;
    }

    public ProgramaTreinoDao getProgramatreinoDao() {
        return programatreinoDao;
    }

    public CompromissoDao getCompromissoDao() {
        return compromissoDao;
    }

    public ProgramaTreinoFichaDao getProgramaTreinoFichaDao() {
        return programaTreinoFichaDao;
    }

    public void setProgramaTreinoFichaDao(ProgramaTreinoFichaDao programaTreinoFichaDao) {
        this.programaTreinoFichaDao = programaTreinoFichaDao;
    }

    public ObjetivoPredefinidoDao getObjetivoPredefinidoDao() {
        return objetivoPredefinidoDao;
    }

    public CategoriaFichaDao getCategoriaFichaDao() {
        return categoriaFichaDao;
    }

    public AtividadeDao getAtividadeDao() {
        return atividadeDao;
    }

    public AtividadeAlternativaDao getAtividadeAlternativaDao() {
        return atividadeAlternativaDao;
    }

    public TreinoRealizadoDao getTreinoRealizadoDao() {
        return treinoRealizadoDao;
    }

    public SerieRealizadaDao getSerieRealizadaDao() {
        return serieRealizadaDao;
    }

    public SerieDao getSerieDao() {
        return serieDao;
    }

    public ClienteBadgeDao getClienteBadge() {
        return clienteBadge;
    }

    public GrupoMuscularDao getGrupoMuscularDao() {
        return grupoMuscularDao;
    }

    public Compromisso addCompromisso(ProgramaTreino programa, int diaSemana, final String horario) {
        Compromisso compromisso = new Compromisso(diaSemana, horario, programa);
        programa.getCompromissos().add(compromisso);
        return compromisso;
    }

    public List<Compromisso> addCompromisso(ProgramaTreino programa, int[] diaSemana, final String horario) {
        List<Compromisso> l = new ArrayList<Compromisso>();
        for (int i = 0; i < diaSemana.length; i++) {
            int j = diaSemana[i];
            Compromisso compromisso = new Compromisso(j, horario, programa);
            l.add(compromisso);
        }
        return l;
    }

    public boolean remove(Compromisso compromisso, final ProgramaTreino programa) {
        return programa.getCompromissos().remove(compromisso);
    }

    public ObjetivoPrograma addObjetivo(final String ctx, ProgramaTreino programa, final String nome) throws ServiceException {
        ObjetivoPrograma objetivoPrograma;
        try {
            objetivoPrograma = new ObjetivoPrograma(getObjetivoPredefinidoDao().insertOrGetObjectForName(ctx, nome), programa);
            programa.getObjetivos().add(objetivoPrograma);
            return objetivoPrograma;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ProgramaTreinoFicha addProgramaFicha(final String ctx, ProgramaTreino programa,
                                                Ficha ficha, TipoExecucaoEnum tipoExecucao, String versao, List<String> diaSemana) throws ServiceException {
        try {
            ProgramaTreinoFicha programaTreinoFicha = new ProgramaTreinoFicha(programa, ficha, tipoExecucao, diaSemana);
            programaTreinoFicha.setVersao(Integer.valueOf(versao));
            programa.getProgramaFichas().add(programaTreinoFicha);
            return programaTreinoFicha;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public AtividadeFicha addAtividadeFicha(final String ctx, ProgramaTreinoFicha programaTreinoFicha, final String atividade,
                                            Integer ordem) throws ServiceException {
        try {
            AtividadeFicha atividadeFicha = new AtividadeFicha(atividade,
                    getAtividadeDao().insertOrGetObjectForName(ctx, atividade),
                    programaTreinoFicha.getFicha(), ordem, null, new ArrayList<AtividadeFichaAjuste>(), false);
            programaTreinoFicha.getFicha().getAtividades().add(atividadeFicha);
            return atividadeFicha;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public CategoriaFicha addCategoriaFicha(final String ctx, Ficha ficha, final String nome) throws ServiceException {
        try {
            CategoriaFicha categoriaFicha = getCategoriaFichaDao().insertOrGetObjectForName(ctx, nome);
            ficha.setCategoria(categoriaFicha);
            return categoriaFicha;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ProgramaTreino alterar(final String ctx, ProgramaTreino object, final ProfessorSintetico professorAlterou) throws ServiceException {
        return alterar(ctx, object, professorAlterou, true);
    }

    public ProgramaTreino alterar(final String ctx, ProgramaTreino object, final ProfessorSintetico professorAlterou, Boolean verificarConfiguracaoSistema) throws ServiceException, ValidacaoException {
        try {
            addProfessores(ctx, object);
            prepararPersistirRestricoes(ctx, object);
            boolean alterouObjetivos = prepararPersistirObjetivos(ctx, object);
            getValidacao().validarInsercao(object, ctx, verificarConfiguracaoSistema);
            if (Calendario.menorOuIgual(object.getDataInicio(), Calendario.hoje())
                    && object.getCliente() != null
                    && Calendario.maiorOuIgual(object.getDataTerminoPrevisto(), Calendario.hoje())) {

                object.getCliente().setNrTreinosPrevistos(object.getTotalAulasPrevistas());
                object.setCliente(cs.alterar(ctx, object.getCliente()));
            }
            verificarAlteracoes(ctx, object, alterouObjetivos, professorAlterou);
            ProgramaTreino update = getProgramaTreinoDao().update(ctx, object);
            update.manterAntesAlteracao();
            object.setObjetivos(obterObjetivosPrograma(ctx, object));
            return update;
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ProgramaTreino alterar(final String ctx, ProgramaTreino object) throws ServiceException, ValidacaoException {
        try {
            if (Calendario.maior(object.getDataInicio(), object.getDataTerminoPrevisto())) {
                throw new ValidacaoException("datainicio.maior.datafim");
            }
            if (Calendario.maiorOuIgual(new Date(), object.getDataInicio())
                    && Calendario.maiorOuIgual(object.getDataTerminoPrevisto(), new Date())) {
                cs.alterarAlgunsCampos(
                        ctx,
                        object.getCliente(),
                        new String[]{"nrTreinosPrevistos"},
                        new Object[]{object.getTotalAulasPrevistas()});
            }
            return getProgramaTreinoDao().update(ctx, object);
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, ProgramaTreino object, final String username, boolean registrarLog) throws ServiceException {
        try {
            object = obterPorId(ctx, object.getCodigo());
            ProgramaTreino ptAntesExclusao = UtilReflection.copy(object);
            acao(object, TipoRevisaoEnum.DELETE);
            limparRelacoes(ctx, object);
            getProgramaTreinoDao().deleteV2(ctx, object);
            if (!BooleanUtils.isTrue(object.getPreDefinido())) {
                notificarOuvintes(ctx, object);
            }

            if (registrarLog && !UteisValidacao.emptyString(username) && ptAntesExclusao != null && ptAntesExclusao.getCodigo() != null) {
                Integer clienteId = 0;
                boolean programaPreDefinido = object.getPreDefinido() != null && object.getPreDefinido();
                if (!programaPreDefinido) {
                    clienteId = object.getCliente().getCodigo();
                }

                incluirLog(ctx, ptAntesExclusao.getCodigo().toString(), clienteId.toString(),
                        ptAntesExclusao.getDescricaoParaLog(null), "", "EXCLUSÃO",
                        "EXCLUSÃO PROGRAMA", EntidadeLogEnum.PROGRAMA, "Programa", username, logDao,
                        null, null);
            }

        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void limparRelacoes(String ctx, ProgramaTreino object) throws Exception {
        limparRelacaoProgramaNotificacao(ctx, object);
        limparRelacaoProgramaObjetivo(ctx, object);
        limparRelacaoProgramaCompromisso(ctx, object);
        limparRelacaoProgramaFicha(ctx, object);

        carregarHistoricoRevisoes(ctx, object);
        if (object.getHistoricoRevicoes() != null) {
            for (HistoricoRevisaoProgramaTreino hrpt : object.getHistoricoRevicoes()) {
                historicoRevisaoDao.deleteV2(ctx, hrpt);
            }
        }

        excluirHistoricoRevisaoSincronizacao(ctx, object);
    }

    public void limparRelacaoProgramaNotificacao(String ctx, ProgramaTreino object) throws Exception {
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("programa_codigo", object.getCodigo());

        List<Notificacao> notificacoes = notDao.findByParam(ctx, "select obj from Notificacao obj where obj.programa.codigo = :programa_codigo", param);
        if (notificacoes != null && !UteisValidacao.emptyList(notificacoes)) {
            for (Notificacao notificacao : notificacoes) {
                notDao.delete(ctx, notificacao);
            }
        }
    }

    public void limparRelacaoProgramaObjetivo(String ctx, ProgramaTreino object) throws Exception {
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("programa_codigo", object.getCodigo());

        List<ObjetivoPrograma> objetivoProgramas = objetivoProgramaDao.findByParam(ctx, "select obj from ObjetivoPrograma obj where obj.programa.codigo = :programa_codigo", param);
        if (objetivoProgramas != null && !UteisValidacao.emptyList(objetivoProgramas)) {
            for (ObjetivoPrograma objetivoPrograma : objetivoProgramas) {
                objetivoProgramaDao.delete(ctx, objetivoPrograma);
            }
        }
    }

    public void limparRelacaoProgramaCompromisso(String ctx, ProgramaTreino object) throws Exception {
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("programa_codigo", object.getCodigo());

        List<Compromisso> compromissos = compromissoDao.findByParam(ctx, "select obj from Compromisso obj where obj.programa.codigo = :programa_codigo", param);
        if (compromissos != null && !UteisValidacao.emptyList(compromissos)) {
            for (Compromisso compromisso : compromissos) {
                compromissoDao.delete(ctx, compromisso);
            }
        }
    }

    public void limparRelacaoProgramaFicha(String ctx, ProgramaTreino object) throws Exception {
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("programa_codigo", object.getCodigo());

        List<ProgramaTreinoFicha> programaTreinoFichas = programaTreinoFichaDao.findByParam(ctx, "select obj from ProgramaTreinoFicha obj where obj.programa.codigo = :programa_codigo", param);
        if (programaTreinoFichas != null && !UteisValidacao.emptyList(programaTreinoFichas)) {
            for (ProgramaTreinoFicha programaTreinoFicha : programaTreinoFichas) {
                programaTreinoFichaDao.delete(ctx, programaTreinoFicha);
            }
        }
    }

    private void addProfessores(final String ctx, ProgramaTreino obj) throws ServiceException {
        if (obj.getCliente() != null && obj.getCliente().getProfessorSintetico() != null && obj.getCliente().getProfessorSintetico().getCodigo() != null) {
            obj.setProfessorCarteira(professorService.obterPorId(ctx, obj.getCliente().getProfessorSintetico().getCodigo()));
        }

    }


    public void validarSituacao(final String ctx, ClienteSintetico cliente) throws ServiceException {
        ConfiguracaoSistema cfgAlunosAtivos = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_APENAS_ALUNOS_ATIVOS);
        if ((cliente.getSituacao().equals("VI")
                || cliente.getSituacaoContrato().equals("DE")
                || cliente.getSituacaoContrato().equals("CA")) && cfgAlunosAtivos.getValorAsBoolean()) {
            throw new ServiceException("obrigatorio.alunoSemSituacaoCompativel");
        }
    }

    public ProgramaTreino inserir(final String ctx, ProgramaTreino object) throws ServiceException {
        return inserir(ctx, object, true);
    }

    public ProgramaTreino inserir(final String ctx, ProgramaTreino object, Boolean verificarConfiguracaoSistema) throws ServiceException, ValidacaoException {
        try {
            acao(object, TipoRevisaoEnum.INSERT);
            boolean predefinido = object.getPreDefinido() != null && object.getPreDefinido() ? true : false;
            if (!predefinido) {
                addProfessores(ctx, object);
            }
            prepararPersistirObjetivos(ctx, object);
            prepararPersistirRestricoes(ctx, object);
            getValidacao().validarInsercao(object, ctx, verificarConfiguracaoSistema);
            if (!predefinido
                    && object.getCliente() != null
                    && Calendario.menorOuIgual(object.getDataInicio(), Calendario.hoje())
                    && Calendario.maiorOuIgual(object.getDataTerminoPrevisto(), Calendario.hoje())) {
                object.getCliente().setNrTreinosRealizados(0);
                object.getCliente().setNrTreinosPrevistos(object.getTotalAulasPrevistas());
                object.setCliente(cs.alterarAlgunsCampos(
                        ctx, object.getCliente(),
                        new String[]{"nrTreinosRealizados", "nrTreinosPrevistos"},
                        new Object[]{0, object.getTotalAulasPrevistas()}));
            }
            if (object.getVersao().equals(0)) {
                object.setVersao(1);
            }
            return getProgramaTreinoDao().insert(ctx, object);
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        } finally {
            leaveAcao();
        }
    }

    public ProgramaTreino obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getProgramaTreinoDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProgramaTreino obterProgramaVigente(final String ctx, final ClienteSintetico cliente)
            throws ServiceException {
        try {
            StringBuilder query = new StringBuilder("select obj from ProgramaTreino obj where cliente.codigo = :cliente \n");
            query.append("and ('").append(Calendario.getData(Calendario.hoje(), "yyyy-MM-dd HH:mm:ss")).append("'");
            query.append(" between ").append("dataInicio and dataTerminoPrevisto");
            query.append(" or cast(dataTerminoPrevisto as date) = '").append(Uteis.getDataFormatoBD(Calendario.getDataComHoraZerada(Calendario.hoje()))).append("') ");
            HashMap<String, Object> p = new HashMap<String, Object>();
            p.put("cliente", cliente.getCodigo());
            return getProgramaTreinoDao().findObjectByParam(ctx, query.toString(), p);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Integer obterCodigoProgramaVigente(final String ctx, final Integer codCliente)
            throws ServiceException {
        try {
            StringBuilder query = new StringBuilder("select codigo from ProgramaTreino obj where cliente_codigo = ").
                    append(codCliente).append("\n");
            query.append("and ('").append(Calendario.getData(Calendario.hoje(), "yyyy-MM-dd HH:mm:ss")).append("'");
            query.append(" between ").append("dataInicio and dataTerminoPrevisto");
            query.append(" or cast(dataTerminoPrevisto as date) = '").append(
                    Calendario.getData(Calendario.getDataComHoraZerada(Calendario.hoje()), "yyyy-MM-dd")).append("') ");
            try (ResultSet rs = getProgramaTreinoDao().createStatement(ctx, query.toString())) {
                if (rs.next()) {
                    return rs.getInt("codigo");
                }
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
        return null;
    }

    @Override
    public ProgramaTreino obterUltimoProgramaVigente(final String ctx, final ClienteSintetico cliente)
            throws ServiceException {
        ProgramaTreino vigente = null;
        try {
            vigente = obterProgramaVigente(ctx, cliente);
            if (vigente == null) {
                StringBuilder sql = new StringBuilder();
                Map<String, Object> params = new HashMap<String, Object>();
                sql.append("SELECT obj FROM ProgramaTreinoAndamento obj ");
                sql.append(" INNER JOIN obj.programa prog ");
                sql.append(" WITH prog.cliente.codigo = :_codcliente ");
                sql.append(" AND ultimoTreino IS NOT NULL ");
                sql.append(" order by ultimoTreino desc ");
                params.put("_codcliente", cliente.getCodigo());
                List<ProgramaTreinoAndamento> listaAndamento = getProgramaTreinoAndamentoDao().findByParam(ctx, sql.toString(), params, 1, 0);
                if (listaAndamento != null && !listaAndamento.isEmpty()) {
                    vigente = obterPorId(ctx, listaAndamento.get(0).getPrograma().getCodigo());
                }
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
        return vigente;
    }

    @Override
    public ProgramaTreino obterUltimoProgramaVigenteComOuSemTreinoRealizado(final String ctx, final Integer cliente)
            throws ServiceException {
        ProgramaTreino vigente = null;
        try {
            StringBuilder sql = new StringBuilder();
            Map<String, Object> params = new HashMap<String, Object>();
            sql.append("SELECT obj FROM ProgramaTreino obj ");
            sql.append(" WHERE obj.cliente.codigo = :_codcliente ");
            sql.append(" order by obj.dataTerminoPrevisto desc ");
            params.put("_codcliente", cliente);
            List<ProgramaTreino> lista = getProgramaTreinoDao().findByParam(ctx, sql.toString(), params, 1, 0);
            if (lista != null && !lista.isEmpty()) {
                return lista.get(0);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
        return vigente;
    }

    @Override
    public ProgramaVersaoJSON obterVersaoUltimoProgramaVigente(final String ctx,
                                                               final Integer codCliente, final Date diaAtual) throws ServiceException {

        try {
            Integer codigo = obterCodigoProgramaVigente(ctx, codCliente);
            String dataAtual = Calendario.getData(diaAtual, Calendario.MASC_DATAHORA);
            if (codigo == null) {
                StringBuilder query = new StringBuilder();
                query.append("  select prog.codigo, prog.versao, prog.dataterminoprevisto from programatreino  prog \n");
                query.append("   where prog.cliente_codigo = ").append(codCliente).append(" \n");
                query.append(" and dataterminoprevisto <= '" + dataAtual + "' ").append(" \n");
                query.append("   order by  prog.dataterminoprevisto desc limit 1 \n");

                try (ResultSet rs = getProgramaTreinoAndamentoDao().createStatement(ctx,
                        query.toString())) {
                    while (rs.next()) {
                        ProgramaVersaoJSON pvJson = new ProgramaVersaoJSON(rs.getInt("codigo"), rs.getInt("versao"));
                        return pvJson;
                    }
                }
            } else {
                StringBuilder query = new StringBuilder();
                query.append("select prog.codigo,prog.versao from programatreino  prog ");
                query.append(" where prog.codigo = ").append(codigo);
                //
                try (ResultSet rs = getProgramaTreinoAndamentoDao().createStatement(ctx,
                        query.toString())) {
                    if (rs.next()) {
                        ProgramaVersaoJSON pvJson = new ProgramaVersaoJSON(rs.getInt("codigo"), rs.getInt("versao"));
                        return pvJson;
                    }
                }
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
        return null;

    }

    public List<AtividadeFicha> obterAtividadesFichaPorIds(final String ctx, List<Integer> atividadeFichaIds) throws ServiceException {

        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM AtividadeFicha obj WHERE codigo IN (:ids) ");

        Map<String, Object> params = new HashMap<String, Object>();
        params.put("ids", atividadeFichaIds);

        try {
            return atividadeFichaDao.findByParam(ctx, hql.toString(), params, 0);
        } catch (Exception e) {
            throw new ServiceException(AtividadeFichaExcecoes.ERRO_BUSCAR_ATIVIDADES_FICHA);
        }
    }

    @Override
    public ProgramaTreino obterPorId(final String ctx, Integer id) throws ServiceException {

        return programatreinoDao.obterPorId(ctx, id);
    }

    @Override
    public List<ProgramaTreino> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getProgramaTreinoDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ProgramaTreino> obterPorParam(final String ctx, String query,
                                              Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getProgramaTreinoDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ProgramaTreino> obterTodos(final String ctx) throws ServiceException {
        try {
            return getProgramaTreinoDao().findAll(ctx);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProgramaTreinoFicha obterProgramaTreinoFicha(final String ctx, final Integer programa, final Integer ficha) throws ServiceException {
        try {
            return getProgramaTreinoFichaDao().findObjectByAttributes(ctx,
                    new String[]{"programa.codigo", "ficha.codigo"},
                    new Object[]{programa, ficha}, "ficha.nome");
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<Number> obterCodigoProgramaTreinoFicha(final String ctx, final Integer programa, final Integer ficha) throws ServiceException {
        try {
            StringBuilder where = new StringBuilder(" WHERE programa_codigo ").
                    append(" = ").append(programa).append(" AND ficha_codigo = ").
                    append(ficha);
            return getSerieDao().listNumberWithParam(ctx, "SELECT codigo FROM ProgramaTreinoFicha ", where, null);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Serie obterSerie(final String ctx, final Integer idSerie) throws ServiceException {
        try {
            return getSerieDao().findObjectByAttributes(ctx, new String[]{"codigo"},
                    new Object[]{idSerie}, null);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<ProgramaTreinoFicha> obterFichaPorProgramaTreino(final String ctx, final Integer programa) throws ServiceException {
        try {
            return getProgramaTreinoFichaDao().obterPorProgramaTreino(ctx, programa);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public SerieRealizada obterSerieRealizadaHoje(final String ctx, final Integer idSerie) throws ServiceException {
        try {
            final String sql = "select obj from SerieRealizada obj where serie.codigo = :serie and cast(dataFim as date) = :dataFim order by dataFim DESC LIMIT 1";
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("serie", idSerie);
            params.put("dataFim", Calendario.hoje());
            List<SerieRealizada> list = getSerieRealizadaDao().findByParam(ctx, sql, params);
            return list == null || list.isEmpty() ? null : list.get(0);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<TreinoRealizado> obterUltimosTreinosRealizados(final String ctx, final Integer idPrograma,
                                                               final int maxResults) throws ServiceException {
        try {
            return getTreinoRealizadoDao().findListByAttributes(ctx, new String[]{"programaTreinoFicha.programa.codigo"}, new Object[]{idPrograma},
                    "dataInicio DESC", maxResults);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<TreinoRealizado> obterUltimosTreinosRealizadosCliente(final String ctx, final Integer matricula,
                                                                      final int maxResults) throws ServiceException {
        try {
            return getTreinoRealizadoDao().findListByAttributes(ctx,
                    new String[]{"programaTreinoFicha.programa.cliente.matricula"}, new Object[]{matricula},
                    "dataInicio DESC", maxResults);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<SerieRealizada> obterSeriesDoTreino(final String ctx, final Integer idTreinoRealizado) throws ServiceException {
        try {
            return getSerieRealizadaDao().findListByAttributes(ctx, new String[]{"treinoRealizado.codigo"}, new Object[]{idTreinoRealizado},
                    "codigo", 0);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<SerieRealizada> obterSeriesDaFicha(final String ctx, final Integer idFicha) throws ServiceException {
        try {
            return getSerieRealizadaDao().findListByAttributes(ctx, new String[]{"atividadeFicha.ficha.codigo"}, new Object[]{idFicha},
                    "codigo", 0);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<Date> obterDatasSeriesAgrupadasDaFicha(final String ctx, final Integer idFicha, final int maxResults) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder().append("select distinct cast(sr.datainicio as date)");
            sql.append(" from serierealizada sr ");
            sql.append(" inner join atividadeficha af on af.codigo = sr.atividadeficha_codigo");
            sql.append(" inner join ficha f on f.codigo = af.ficha_codigo");
            sql.append(" where f.codigo = ").append(idFicha);
            sql.append(" order by cast(sr.datainicio as date) desc");
            sql.append(" limit ").append(maxResults);
            return getSerieRealizadaDao().listOfObjects(ctx, sql.toString());
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<SerieRealizada> obterSeriesDaFichaPorData(final String ctx, final Integer idFicha, final Date data) throws ServiceException {
        try {
            StringBuilder jpql = new StringBuilder("select o from ").append(SerieRealizada.class.getSimpleName());
            jpql.append(" o where atividadeFicha.ficha.codigo = :ficha and cast(dataInicio as date) = :dataInicio ");
            jpql.append("order by dataInicio desc");
            //
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("ficha", idFicha);
            params.put("dataInicio", data);
            return getSerieRealizadaDao().findByParam(ctx, jpql.toString(), params);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public ProgramaTreinoResumo obterResumo(final String ctx, final ClienteSintetico cliente) throws ServiceException {
        try {
            ProgramaTreinoResumo resumo = new ProgramaTreinoResumo();
            //
            Long nAtividades = (Long) getSerieRealizadaDao().count(ctx, "codigo", new String[]{"treinoRealizado.cliente.codigo"},
                    new Object[]{cliente.getCodigo()});
            Long nProgramas = (Long) getProgramaTreinoDao().count(ctx, "codigo", new String[]{"cliente.codigo"},
                    new Object[]{cliente.getCodigo()});
            Long nDiasTodosTreinamentos = (Long) getTreinoRealizadoDao().count(ctx, "codigo", new String[]{"cliente.codigo"},
                    new Object[]{cliente.getCodigo()});
            Long nTreinosSemana = (Long) getTreinoRealizadoDao().count(ctx, "codigo", new String[]{"cliente.codigo", "dataInicio between"},
                    new Object[]{cliente.getCodigo(), Calendario.inicioSemana(Calendario.hoje()), Calendario.fimSemana(Calendario.hoje())});
            //
            resumo.setProgramas(nProgramas.intValue());
            resumo.setAtividades(nAtividades.intValue());
            resumo.setDiasPresencaSemana(nTreinosSemana.intValue());
            resumo.setDiasTreinamento(nDiasTodosTreinamentos.intValue());
            //
            ProgramaTreino p = obterUltimoProgramaVigente(ctx, cliente);
            if (p != null) {
                Long nDiasTreinamentoProgramaAtual = (Long) getTreinoRealizadoDao().count(ctx, "codigo",
                        new String[]{"cliente.codigo", "programaTreinoFicha.programa.codigo"},
                        new Object[]{cliente.getCodigo(), p.getCodigo()});
                resumo.setDiasProgramaAtual(nDiasTreinamentoProgramaAtual.intValue());
                resumo.setDiasProgramaAtualTotal(p.getTotalAulasPrevistas());
                resumo.setExpecativaSemana(p.getDiasPorSemana());
            }
            return resumo;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public TreinoRealizado obterTreinoEmAndamento(final String ctx, final Integer idCliente,
                                                  final int idProgramaFicha,
                                                  final String inicio) throws ServiceException {
        try {
            Date dataInicio = Calendario.getDate(Calendario.MASC_DATAHORA, inicio);
            final String sql = "select obj from TreinoRealizado obj where " +
                    (idCliente == null ? "" : "cliente.codigo = :cliente and ") +
                    " programaTreinoFicha.codigo = :programaTreinoFicha and cast(dataInicio as date) = :dataInicio";
            Map<String, Object> params = new HashMap<String, Object>();
            if (idCliente != null) {
                params.put("cliente", idCliente);
            }
            params.put("programaTreinoFicha", idProgramaFicha);
            params.put("dataInicio", dataInicio);
            return getTreinoRealizadoDao().findObjectByParam(ctx, sql, params);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public SerieRealizada inserirTreinoRealizado(final String ctx, final String username,
                                                 ProgramaTreino programa, final String idFicha, final String inicio,
                                                 final String fim, final String idAtividade, Serie serie,
                                                 AtividadeFicha atividadeFicha, OrigemExecucaoEnum origem,
                                                 ProgramaTreinoFicha programaTreinoFicha)
            throws ServiceException {
        try {
            //desconsidera execuções de uma mesma série mais de uma vez no mesmo dia
            if (serie.getDataUltimaExecucao() != null
                    && Calendario.igual(Calendario.hoje(), serie.getDataUltimaExecucao())) {
                return null;
            }
            Date dataInicio = Calendario.getDate(Calendario.MASC_DATAHORA, inicio);
            Date dataFim = Calendario.getDate(Calendario.MASC_DATAHORA, fim);
            if (programa != null) {
                Map<String, Object> p = new HashMap<String, Object>();
                p.put("serie", serie.getCodigo());
                p.put("dataInicio", dataInicio);

                if (programaTreinoFicha == null) {
                    throw new ValidacaoException(getViewUtils().getMensagem("mobile.programaFichaNaoEncontrado"));
                }
                TreinoRealizado treino = obterTreinoEmAndamento(ctx, programa.getCliente().getCodigo(),
                        programaTreinoFicha.getCodigo(), inicio);
                boolean novoDia = false;
                if (treino == null || treino.getCodigo() == null) {
                    treino = new TreinoRealizado();
                    treino.setCliente(programa.getCliente());
                    treino.setDataInicio(dataInicio);
                    treino.setProfessor(programa.getProfessorCarteira());
                    treino.setProgramaTreinoFicha(programaTreinoFicha);
                    treino.setOrigem(origem);
                    novoDia = true;
                } else if (treino.getProfessorAcompanhamento() == null) {
                    ClienteAcompanhamento acompanhamento = cs.obterAcompanhamento(ctx, programa.getCliente());
                    if (acompanhamento != null) {
                        treino.setProfessorAcompanhamento(acompanhamento.getProfessor());
                        treinoRealizadoDao.updateAlgunsCampos(ctx,
                                new String[]{"professoracompanhamento_codigo"},
                                new Object[]{acompanhamento.getProfessor().getCodigo()},
                                new String[]{"codigo"}, new Object[]{treino.getCodigo()});
                    }
                }
                SerieRealizada serieRealizada = new SerieRealizada();
                serieRealizada.setTreinoRealizado(treino);
                serieRealizada.setDuracao(serie.getDuracao());
                serieRealizada.setAtividadeFicha(atividadeFicha);
                if (origem != null && OrigemExecucaoEnum.SMARTPHONE == origem) {
                    serieRealizada.ajustarDadosPorTipo(
                            serie.getValor1(atividadeFicha.getAtividade().getTipo()),
                            serie.getValor2(atividadeFicha.getAtividade().getTipo()));
                } else {
                    serieRealizada.setRepeticao(serie.getRepeticao());
                    serieRealizada.setCarga(serie.getCarga());
                    serieRealizada.setVelocidade(serie.getVelocidade());
                    serieRealizada.setDistancia(serie.getDistancia());
                }
                serieRealizada.setDataInicio(dataInicio);
                serieRealizada.setDataFim(dataFim);
                serieRealizada.setTreinoRealizado(treino);
                serieRealizada.setSerie(serie);
                this.inserirSerieRealizada(ctx, serieRealizada);
                if (novoDia) {
                    atualizarNrTreinosRealizados(ctx, programa.getCodigo(),
                            (programa.getNrTreinosRealizados() == null ? 0 : programa.getNrTreinosRealizados()) + 1);
                    cs.atualizarNrTreinosRealizados(ctx, programa.getCliente().getCodigo(),
                            (programa.getCliente().getNrTreinosRealizados() == null ? 0 : programa.getCliente().getNrTreinosRealizados()) + 1);
                    fichaDao.updateAlgunsCampos(ctx, new String[]{"ultimaExecucao"},
                            new Object[]{Calendario.hoje()}, new String[]{"codigo"},
                            new Object[]{Integer.valueOf(idFicha)});
                    serieRealizada = this.obterSerieRealizadaHoje(ctx, serie.getCodigo());
                    if (configuracaoSistemaService.notificacaoConfigurada(ctx, TipoNotificacaoEnum.INICIOU_TREINO)) {
                        notfService.notificarSerieRealizada(ctx, serieRealizada, TipoNotificacaoEnum.INICIOU_TREINO);
                    }

                }
                return serieRealizada;
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.programanaoencontrado"));
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void resetarCampoSerieRealizadaDaSerie(final String ctx, final Integer idFicha) throws ServiceException {
        try {
            String sql =
                    "select s.codigo from serie s \n" +
                            "inner join atividadeficha a on a.codigo = s.atividadeficha_codigo \n" +
                            "where a.ficha_codigo = " + idFicha;
            try (ResultSet rs = getSerieDao().createStatement(ctx, sql)) {
                while (rs.next()) {
                    Integer codSerie = rs.getInt("codigo");
                    if (!UteisValidacao.emptyNumber(codSerie)) {
                        programatreinoDao.executeNativeSQL(ctx, "update serie p set serierealizada = false where p.codigo = " + codSerie);
                    }
                }
            }
        } catch (Exception ex) {
            Uteis.logar(ex, ProgramaTreinoServiceImpl.class);
        }
    }

    @Override
    public TreinoRealizado inserirTreinoRealizadoSemSerie(final String ctx, ProgramaTreino programa,
                                                          final String idFicha, final String inicio,
                                                          final String fim, OrigemExecucaoEnum origem,
                                                          ProgramaTreinoFicha programaTreinoFicha,
                                                          final String chaveExecucao,
                                                          final String unidadeExecucao, HttpServletRequest request)
            throws ServiceException {
        try {
            Date dataInicio = Calendario.getDate(Calendario.MASC_DATAHORA, inicio);
            Date dataFim = Calendario.getDate(Calendario.MASC_DATAHORA, fim);
            if (programa != null) {
                if (programaTreinoFicha == null) {
                    throw new ValidacaoException(getViewUtils().getMensagem("mobile.programaFichaNaoEncontrado"));
                }
                TreinoRealizado treino = obterTreinoEmAndamento(ctx,
                        programa.getCliente() == null ? null : programa.getCliente().getCodigo(),
                        programaTreinoFicha.getCodigo(), inicio);

                // desconsidera execuções de treino mais de uma vez no mesmo dia
                if (treino != null
                        && idFicha.equals(treino.getProgramaTreinoFicha().getFicha().getCodigo().toString())
                        && Calendario.igual(Calendario.hoje(), treino.getDataInicio())) {
                    return null;
                }

                boolean novoDia = false;
                if (treino == null || treino.getCodigo() == null) {
                    treino = new TreinoRealizado();
                    treino.setCliente(programa.getCliente());
                    treino.setDataInicio(dataInicio);
                    treino.setProfessor(programa.getProfessorCarteira());
                    treino.setProgramaTreinoFicha(programaTreinoFicha);
                    treino.setOrigem(origem);
                    treino.setChaveExecucao(chaveExecucao);
                    treino.setUnidadeExecucao(unidadeExecucao);
                    treino = treinoRealizadoDao.insert(ctx, treino);
                    novoDia = true;
                } else if (treino.getProfessorAcompanhamento() == null && programa.getCliente() != null) {
                    ClienteAcompanhamento acompanhamento = cs.obterAcompanhamento(ctx, programa.getCliente());
                    if (acompanhamento != null) {
                        treino.setProfessorAcompanhamento(acompanhamento.getProfessor());
                        treinoRealizadoDao.updateAlgunsCampos(ctx,
                                new String[]{"professoracompanhamento_codigo"},
                                new Object[]{acompanhamento.getProfessor().getCodigo()},
                                new String[]{"codigo"}, new Object[]{treino.getCodigo()});
                    }
                }
                if (novoDia) {
                    atualizarNrTreinosRealizados(ctx, programa.getCodigo(),
                            (programa.getNrTreinosRealizados() == null ? 0 : programa.getNrTreinosRealizados()) + 1);
                    if (programa.getCliente() != null) {
                        cs.atualizarNrTreinosRealizados(ctx, programa.getCliente().getCodigo(),
                                (programa.getCliente().getNrTreinosRealizados() == null ? 0 : programa.getCliente().getNrTreinosRealizados()) + 1);
                    }
                    fichaDao.updateAlgunsCampos(ctx, new String[]{"ultimaExecucao"},
                            new Object[]{Calendario.hoje()}, new String[]{"codigo"},
                            new Object[]{Integer.valueOf(idFicha)});

                }
                return treino;
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.programanaoencontrado"));
            }
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        } finally {
            notificarOuvinteAposExecutarFicha(ctx, programa, request);
        }
    }

    private void notificarOuvinteAposExecutarFicha(final String ctx, ProgramaTreino programa, HttpServletRequest request) {
        try {
            Integer codigoCliente = programa != null && programa.getCliente() != null ? programa.getCliente().getCodigo() : null;
            Integer codigoColaborador = programa != null ? programa.getCodigoColaborador() : null;
            if (request != null) {
                notificarOuvintes(ctx, codigoCliente, codigoColaborador, request);
            } else {
                Usuario usuario = codigoCliente == null ?
                        usuarioService.obterPorAtributo(ctx, "professor.codigoColaborador", codigoColaborador) :
                        usuarioService.obterPorAtributo(ctx, "cliente.codigo", codigoCliente);
                filaImpressaoService.enfileirar(ctx, TipoObjetoSincronizarEnum.PROGRAMA, usuario.getUserName());
            }
        } catch (Exception ex) {
            Uteis.logar(ex, ProgramaTreinoServiceImpl.class);
        }
    }

    @Override
    public void atualizarSerieComBaseNaSerieRealizada(final String ctx, SerieRealizada serieRealizada) {
        SerieTO to = serieRealizada.getSerieAtualizar();
        if (to != null) {
            if ((!UteisValidacao.emptyNumber(to.getRepeticao())
                    && !UteisValidacao.emptyNumber(to.getCarga()))) {
                try {
                    Uteis.logar(null, String.format("===========ATUALIZAR CARGA/REPETICAO SERIE DA FICHA %s (%s) ===========",
                            ctx, to.getCodigo()));
                    getSerieDao().updateAlgunsCampos(ctx, new String[]{
                                    "carga", "repeticao"},
                            new Object[]{
                                    to.getCarga(),
                                    to.getRepeticao()
                            }, new String[]{"codigo"}, new Object[]{to.getCodigo()});
                    Uteis.logar(null, String.format("===========FIM - ATUALIZAR CARGA/REPETICAO SERIE DA FICHA %s (%s) ===========",
                            ctx, to.getCodigo()));
                } catch (Exception ex) {
                    Logger.getLogger(ProgramaTreinoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
            if (!UteisValidacao.emptyNumber(to.getDuracao())
                    || !UteisValidacao.emptyNumber(to.getDistancia())) {
                try {
                    Uteis.logar(null, String.format("===========ATUALIZAR DURACAO/VELOCIDADE SERIE DA FICHA %s (%s) ===========",
                            ctx, to.getCodigo()));
                    getSerieDao().updateAlgunsCampos(ctx, new String[]{
                                    "duracao", "velocidade"},
                            new Object[]{
                                    to.getDuracao(),
                                    to.getVelocidade()
                            }, new String[]{"codigo"}, new Object[]{to.getCodigo()});
                    Uteis.logar(null, String.format("===========FIM - ATUALIZAR DURACAO/VELOCIDADE SERIE DA FICHA %s (%s) ===========",
                            ctx, to.getCodigo()));
                } catch (Exception ex) {
                    Logger.getLogger(ProgramaTreinoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        }
    }

    @Override
    public ProgramaTreinoAndamento ************************(final String ctx, final String idPrograma,
                                                            final String idFicha, final Date dataBase,
                                                            boolean fichaConcluida, String origem,
                                                            final String chaveExecucao,
                                                            final String unidadeExecucao, HttpServletRequest request) throws ServiceException {
        ProgramaTreinoFicha programaTreinoFicha = obterProgramaTreinoFicha(ctx, Integer.valueOf(idPrograma), Integer.valueOf(idFicha));

        TreinoRealizado tr = new TreinoRealizado();
        if (origem.equals("app")) {
            tr = inserirTreinoRealizadoSemSerie(ctx, programaTreinoFicha.getPrograma(), idFicha,
                    Calendario.getData(dataBase, Calendario.MASC_DATAHORA),
                    Calendario.getData(dataBase, Calendario.MASC_DATAHORA),
                    OrigemExecucaoEnum.SMARTPHONE, programaTreinoFicha, null, null, request);
        } else {
            tr = inserirTreinoRealizadoSemSerie(ctx, programaTreinoFicha.getPrograma(), idFicha,
                    Calendario.getData(dataBase, Calendario.MASC_DATAHORA),
                    Calendario.getData(dataBase, Calendario.MASC_DATAHORA),
                    fichaConcluida ? OrigemExecucaoEnum.FICHA_CONCLUIDA : OrigemExecucaoEnum.FICHA_IMPRESSA, programaTreinoFicha,
                    chaveExecucao, unidadeExecucao, request);
        }

        if (tr != null) {
            TreinoRealizado treino = obterTreinoEmAndamento(ctx,
                    programaTreinoFicha.getPrograma().getCliente() == null ? null :
                            programaTreinoFicha.getPrograma().getCliente().getCodigo(),
                    programaTreinoFicha.getCodigo(), Uteis.getDataAplicandoFormatacao(dataBase, "dd/MM/yyyy HH:mm:ss"));
            adicionarAndamentoPrograma(ctx, programaTreinoFicha.getPrograma(), treino, false);
        }

        try {
            verificarSincronizacaoTreinosRealizados(ctx, programaTreinoFicha.getPrograma());
        } catch (Exception ex) {
            Uteis.logar(ex, ProgramaTreinoServiceImpl.class);
        }

        try {
            return getProgramaTreinoAndamentoDao().findObjectByParam(ctx, "SELECT obj FROM ProgramaTreinoAndamento obj "
                    + "where programa.codigo = " + idPrograma, new HashMap<String, Object>());
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    private void verificarSincronizacaoTreinosRealizados(final String ctx, ProgramaTreino pt) throws Exception {
        if (pt != null && pt.getCliente() != null) {
            Integer treinosRealizadosProgramaTreino = pt.getNrTreinosRealizados();
            Integer treinosRealizadosAndamento = 0;
            Integer qtdTreinosRealizados = 0;

            try (ResultSet rs = getProgramaTreinoFichaDao().createStatement(ctx,
                    "select nrtreinos from programatreinoandamento p where programa_codigo = " + pt.getCodigo())) {
                if (rs.next()) {
                    treinosRealizadosAndamento = rs.getInt("nrtreinos");
                }
            }

            String sql =
                    "select count(t.codigo) as qtdTreinosRealizados from treinorealizado t \n" +
                            "inner join programatreinoficha ptf on ptf.codigo = t.programatreinoficha_codigo \n" +
                            "where ptf.programa_codigo = " + pt.getCodigo();
            try (ResultSet rs = getProgramaTreinoFichaDao().createStatement(ctx, sql)) {
                if (rs.next()) {
                    qtdTreinosRealizados = rs.getInt("qtdTreinosRealizados");
                }
            }

            if (qtdTreinosRealizados > 0) {
                if (!qtdTreinosRealizados.equals(treinosRealizadosProgramaTreino)) {
                    programatreinoDao.executeNativeSQL(ctx, "update programatreino p set nrtreinosrealizados = " + qtdTreinosRealizados +
                            " where p.codigo = " + pt.getCodigo());
                }
                if (!qtdTreinosRealizados.equals(treinosRealizadosAndamento)) {
                    programatreinoDao.executeNativeSQL(ctx, "update programatreinoandamento p set nrtreinos = " + qtdTreinosRealizados +
                            " where p.programa_codigo = " + pt.getCodigo());
                }
            }
        }
    }

    public void atualizarSerieRealizada(final String ctx, final Integer idSerie,
                                        final String valor1, String valor2, final Integer duracao, boolean forcarCargaPraFicha) throws ServiceException {
        try {
            SerieRealizada serieRealizada = this.obterSerieRealizadaHoje(ctx, idSerie);
            if (serieRealizada != null) {
                Integer v1 = Integer.valueOf(valor1);
                valor2 = valor2 == null ? "0.0" : valor2.replaceAll(",", ".");
                Double v2 = Double.valueOf(valor2);
                ConfiguracaoSistema cfgAcima = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.PORC_ACIMA_NOTIFICAR);
                ConfiguracaoSistema cfgAbaixo = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.PORC_ABAIXO_NOTIFICAR);
                serieRealizada.setForcarAtualizacaoFicha(forcarCargaPraFicha);

                TipoAtividadeEnum tipo = serieRealizada.getAtividadeFicha().getAtividade().getTipo();

                Double cargaNova = (v1 * v2);
                Double cargaAntiga = ((tipo == TipoAtividadeEnum.ANAEROBICO)
                        ? (serieRealizada.getCarga() * serieRealizada.getRepeticao())
                        : (serieRealizada.getVelocidade() * serieRealizada.getDuracao() * serieRealizada.getDistancia()));
                serieRealizada.ajustarDadosPorTipo(valor1, valor2);
                TipoNotificacaoEnum tipoNotf = obterNotificacao(ctx, cargaNova, cargaAntiga, cfgAcima, cfgAbaixo);
                serieRealizada.setTipoNotificacao(tipoNotf);
                if (duracao != null) {
                    serieRealizada.setDuracao(duracao);
                }

                getSerieRealizadaDao().update(ctx, serieRealizada);

                atualizarSerieComBaseNaSerieRealizada(ctx, serieRealizada);

                if (tipoNotf != null
                        && ((tipoNotf.equals(TipoNotificacaoEnum.AUMENTOU_CARGA) || tipoNotf.equals(TipoNotificacaoEnum.DIMINUIU_CARGA))
                        && configuracaoSistemaService.notificacaoConfigurada(ctx, tipoNotf))) {
                    notfService.notificarSerieRealizada(ctx, serieRealizada, tipoNotf);
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.serienaoencontrada"));
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public TipoNotificacaoEnum obterNotificacao(final String ctx, Double cargaNova, Double cargaAntiga,
                                                ConfiguracaoSistema cfgAcima, ConfiguracaoSistema cfgAbaixo) {
        if (cfgAbaixo != null && cfgAcima != null) {
            Double percDiferenca = (cargaNova / cargaAntiga - 1) * 100;
            if (cargaNova > cargaAntiga && percDiferenca > cfgAcima.getValorAsInteger()) {
                return TipoNotificacaoEnum.AUMENTOU_CARGA;
            } else if (cargaNova < cargaAntiga && (percDiferenca * -1) > cfgAbaixo.getValorAsInteger()) {
                return TipoNotificacaoEnum.DIMINUIU_CARGA;
            }
        }
        return TipoNotificacaoEnum.EDITOU_SERIE;
    }

    @Override
    public void inserirSerieRealizada(final String ctx, SerieRealizada sr) throws ServiceException {
        try {
            if (UteisValidacao.emptyNumber(sr.getTreinoRealizado().getCodigo())) {
                TreinoRealizado tr = sr.getTreinoRealizado();
                List<String> p = new ArrayList();
                List<Object> v = new ArrayList();
                TreinoRealizado.preencherListaAtributoValor(tr, p, v);
                Integer idTr = treinoRealizadoDao.insertAlgunsCampos(ctx, p.toArray(new String[0]), v.toArray());
                tr = treinoRealizadoDao.findById(ctx, idTr.intValue());
                sr.setTreinoRealizado(tr);
                Integer idSr = getSerieRealizadaDao().insertAlgunsCampos(ctx, new String[]{
                                "carga", "datafim", "datainicio", "distancia", "duracao",
                                "ordem", "repeticao", "velocidade",
                                "atividadeficha_codigo", "serie_codigo", "treinorealizado_codigo"},
                        new Object[]{
                                sr.getCarga(),
                                sr.getDataFim(),
                                sr.getDataInicio(),
                                sr.getDistancia(),
                                sr.getDuracao(),
                                sr.getOrdem(),
                                sr.getRepeticao(),
                                sr.getVelocidade(),
                                sr.getAtividadeFicha().getCodigo(),
                                sr.getSerie().getCodigo(),
                                tr.getCodigo()
                        });
                sr = getSerieRealizadaDao().findById(ctx, idSr);
            } else {
                if (sr.getCodigo().equals(0)) {
                    Integer idSr = getSerieRealizadaDao().insertAlgunsCampos(ctx, new String[]{
                                    "carga", "datafim", "datainicio", "distancia", "duracao",
                                    "ordem", "repeticao", "velocidade",
                                    "atividadeficha_codigo", "serie_codigo", "treinorealizado_codigo"},
                            new Object[]{
                                    sr.getCarga(),
                                    sr.getDataFim(),
                                    sr.getDataInicio(),
                                    sr.getDistancia(),
                                    sr.getDuracao(),
                                    sr.getOrdem(),
                                    sr.getRepeticao(),
                                    sr.getVelocidade(),
                                    sr.getAtividadeFicha().getCodigo(),
                                    sr.getSerie().getCodigo(),
                                    sr.getTreinoRealizado().getCodigo()
                            });
                    sr = getSerieRealizadaDao().findById(ctx, idSr);
                } else {
                    getSerieRealizadaDao().updateAlgunsCampos(ctx,
                            new String[]{
                                    "carga", "datafim", "datainicio", "distancia", "duracao",
                                    "ordem", "repeticao", "velocidade", "tiponotificacao"},
                            new Object[]{
                                    sr.getCarga(),
                                    sr.getDataFim(),
                                    sr.getDataInicio(),
                                    sr.getDistancia(),
                                    sr.getDuracao(),
                                    sr.getOrdem(),
                                    sr.getRepeticao(),
                                    sr.getVelocidade(),
                                    sr.getTipoNotificacao()},
                            new String[]{"codigo"}, new Object[]{sr.getCodigo()});
                }
            }
            getSerieDao().executeNativeSQL(ctx, String.format("update serie set dataUltimaExecucao = '%s' where codigo = %s",
                    Calendario.getData(sr.getDataInicio(), "yyyy-MM-dd HH:mm:ss"), sr.getCodigo()));
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public ProgramaTreinoAndamento concluirTreinoEmAndamento(final String ctx, final String username, final String idPrograma, final int idFicha,
                                                             final int dia, final String nota, final int tempo, final String comentario, Date diaDoTreino) throws ServiceException {
        try {
            ProgramaTreino programa = this.obterPorId(ctx, Integer.valueOf(idPrograma));
            System.out.println("ProgramaTreinoServiceImpl.concluirTreinoEmAndamento() - programa: " + programa);
            if (programa != null) {
                Date diaTreino = diaDoTreino == null ? Calendario.hoje() : diaDoTreino;
                ProgramaTreinoFicha programaTreinoFicha = this.obterProgramaTreinoFicha(ctx, programa.getCodigo(),
                        Integer.valueOf(idFicha));
                if (programaTreinoFicha == null) {
                    throw new ServiceException(ProgramaTreinoExcecoes.FICHA_NAO_ENCONTRADA);
                }
                TreinoRealizado treinoAndamento = obterTreinoEmAndamento(ctx, programa.getCliente().getCodigo(),
                        programaTreinoFicha.getCodigo(), Uteis.getDataAplicandoFormatacao(diaTreino, Calendario.MASC_DATAHORA));
                if (treinoAndamento != null) {
                    treinoAndamento.setTempoUtil(tempo);
                    treinoAndamento.setNota(nota);
                    treinoAndamento.setComentario(comentario);
                    treinoAndamento.setDataFim(diaTreino);
                    treinoAndamento = getTreinoRealizadoDao().update(ctx, treinoAndamento);
                } else {
                    treinoAndamento = new TreinoRealizado();
                    treinoAndamento.setProfessor(programa.getProfessorCarteira());
                    treinoAndamento.setCliente(programa.getCliente());
                    treinoAndamento.setDataInicio(diaTreino);
                    treinoAndamento.setDataFim(diaTreino);
                    treinoAndamento.setOrigem(OrigemExecucaoEnum.SMARTPHONE);
                    treinoAndamento.setNota(nota);
                    treinoAndamento.setComentario(comentario);
                    treinoAndamento.setTempoUtil(tempo);
                    treinoAndamento.setProgramaTreinoFicha(programaTreinoFicha);
                    treinoAndamento = getTreinoRealizadoDao().insert(ctx, treinoAndamento);
                }

                ProgramaTreinoAndamento result = new ProgramaTreinoAndamento();
                // não atualiza execuções do andamento caso a ficha já tenha sido executada no mesmo dia
                if (!isFichaJaExecutadaHoje(treinoAndamento, idFicha)) {
                    //atualizar andamento do programa
                    result = adicionarAndamentoPrograma(ctx, programa, treinoAndamento, false);
                } else {
                    result = getProgramaTreinoAndamentoDao().findObjectByParam(ctx, "SELECT obj FROM ProgramaTreinoAndamento obj "
                            + "where programa.codigo = " + programa.getCodigo(), new HashMap<String, Object>());
                }

                //calcular badges do aluno
                calcularBadges(ctx, programa);

                return result;
            } else {
                return null;
            }
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public ProgramaTreinoAndamento obterAndamento(final String ctx, final ProgramaTreino programa) throws ServiceException {
        try {
            return getProgramaTreinoAndamentoDao().findObjectByParam(ctx, "SELECT obj FROM ProgramaTreinoAndamento obj "
                    + "where programa.codigo = " + programa.getCodigo(), new HashMap<String, Object>());
        } catch (Exception ex) {
            Logger.getLogger(ProgramaTreinoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException(ex.getMessage());
        }
    }

    public boolean isFichaJaExecutadaHojeExcetoFicha(final String ctx, TreinoRealizado treinoRealizado) throws Exception {
        final String sql = "select obj from TreinoRealizado obj where cliente.codigo = :cliente and programaTreinoFicha.ficha.codigo != :ficha and cast(dataInicio as date) = :dataInicio";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("cliente", treinoRealizado.getCliente().getCodigo());
        params.put("ficha", treinoRealizado.getProgramaTreinoFicha().getFicha().getCodigo());
        params.put("dataInicio", treinoRealizado.getDataInicio());
        return getTreinoRealizadoDao().findObjectByParam(ctx, sql, params) != null;
    }

    public boolean isFichaJaExecutadaHoje(TreinoRealizado treino, int idFicha) throws Exception {
        if (treino != null
                && idFicha == treino.getProgramaTreinoFicha().getFicha().getCodigo()
                && Calendario.igual(Calendario.hoje(), treino.getDataInicio())) {
            return true;
        } else {
            return false;
        }
    }

    public ProgramaTreinoAndamento adicionarAndamentoPrograma(final String ctx, ProgramaTreino programa,
                                                              TreinoRealizado treinoRealizado, boolean povoando) throws ServiceException {
        try {
            ProgramaTreinoAndamento andamento = getProgramaTreinoAndamentoDao().findObjectByParam(ctx, "SELECT obj FROM ProgramaTreinoAndamento obj "
                    + "where programa.codigo = " + programa.getCodigo(), new HashMap<String, Object>());
            if (andamento == null || andamento.getCodigo() == null || andamento.getCodigo().equals(0)) {
                andamento = new ProgramaTreinoAndamento();
                andamento.setPrograma(programa);
                andamento = getProgramaTreinoAndamentoDao().insert(ctx, andamento);
            } else if (povoando) {
                return null;
            }

            //não adicionar o andamento se já teve um treino no mesmo dia
            if (andamento.getUltimoTreino() != null
                    && Calendario.igual(andamento.getUltimoTreino(),
                    treinoRealizado.getDataInicio()) && !isFichaJaExecutadaHojeExcetoFicha(ctx, treinoRealizado)) {
                return andamento;
            }

            List<SerieRealizada> series = obterSeriesDoTreino(ctx, treinoRealizado.getCodigo());
            for (SerieRealizada serReal : series) {
                if (serReal.getAtividadeFicha().getAtividade().getAerobica()) {
                    andamento.setDistancia(andamento.getDistancia() + serReal.getDistancia());
                    andamento.setDuracao(andamento.getDuracao() + serReal.getDuracao());

                    andamento.setVelocidade(serReal.getVelocidade() != null
                            && serReal.getVelocidade() > 0.0
                            ? (((andamento.getNrVelocidade() * andamento.getVelocidade()) + serReal.getVelocidade()) / (andamento.getNrVelocidade() + 1))
                            : 0);

                    andamento.setNrVelocidade(serReal.getVelocidade() != null
                            && serReal.getVelocidade() > 0.0 ? andamento.getNrVelocidade() + 1 : andamento.getNrVelocidade());

                } else {
                    andamento.setCarga(andamento.getCarga() + serReal.getCarga());
                    andamento.setRepeticao(andamento.getRepeticao() + serReal.getRepeticao());
                }
            }
            long diasPrograma = Uteis.nrDiasEntreDatas(programa.getDataInicio(), programa.getDataTerminoPrevisto());
            long diasTranscorridos = Uteis.nrDiasEntreDatas(programa.getDataInicio(), treinoRealizado.getDataInicio());
            double percentualAndamento = (diasTranscorridos / diasPrograma) * 100;
            andamento.setPercentualAndamento(percentualAndamento);
            andamento.setNrTreinos(andamento.getNrTreinos() + 1);
            andamento.setUltimoTreino(treinoRealizado.getDataInicio());

            getProgramaTreinoAndamentoDao().updateAlgunsCampos(ctx,
                    new String[]{"carga", "distancia", "duracao", "nrtreinos",
                            "nrvelocidade", "percentualandamento", "repeticao",
                            "ultimotreino", "velocidade"},
                    new Object[]{andamento.getCarga(), andamento.getDistancia(),
                            andamento.getDuracao(), andamento.getNrTreinos(), andamento.getNrVelocidade(),
                            andamento.getPercentualAndamento(), andamento.getRepeticao(), andamento.getUltimoTreino(),
                            andamento.getVelocidade()},
                    new String[]{"programa.codigo"},
                    new Object[]{programa.getCodigo()});
            return andamento;

        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<Badge> calcularBadges(final String ctx, ProgramaTreino programa) throws ServiceException {
        try {
            List<Badge> badgesConquistados = new ArrayList<Badge>();
            //obter andamento do treino
            ProgramaTreinoAndamento andamento = getProgramaTreinoAndamentoDao().findObjectByParam(ctx, "SELECT obj FROM ProgramaTreinoAndamento obj "
                    + "where programa.codigo = " + programa.getCodigo(), new HashMap<String, Object>());
            //cliente que irá ganhar os badges
            ClienteSintetico aluno = programa.getCliente();

            //obter badges já conquistados pelo aluno
            List<Badge> badgesAluno = bs.consultarPorAluno(ctx, aluno.getCodigo());

            if (andamento != null && andamento.getCodigo() != null && andamento.getCodigo() > 0) {
                //iterar em todos os badges disponiveis
                List<Badge> todos = bs.obterTodos(ctx);
                for (Badge bdg : todos) {
                    //se o aluno já tem o badge, pular para o próximo
                    if (badgesAluno.contains(bdg)) {
                        continue;
                    }
                    switch (bdg.getTipo()) {
                        case CARGA:
                            //se o aluno atingiu a carga estimada pelo badge
                            if ((andamento.getCarga() / 1000) >= bdg.getParametro1()) {
                                gravarClienteBadge(ctx, aluno, bdg, Calendario.hoje(), bs);
                                badgesConquistados.add(bdg);
                            }
                            break;
                        case DISTANCIA:
                            //se o aluno atingiu a distancia estimada pelo badge
                            if (andamento.getDistancia() >= bdg.getParametro1()) {
                                gravarClienteBadge(ctx, aluno, bdg, Calendario.hoje(), bs);
                                badgesConquistados.add(bdg);
                            }
                            break;
                        case FREQUENCIA:
                            //deve calcular a frequencia do aluno a partir de um determinado estágio do treino.
                            if (andamento.getPercentualAndamento() >= bdg.getParametro2()) {
                                Double percentualFrequencia = andamento.getPercentualFrequenciaAteHoje();
                                if (percentualFrequencia.intValue() >= bdg.getParametro1()) {
                                    gravarClienteBadge(ctx, aluno, bdg, Calendario.hoje(), bs);
                                    badgesConquistados.add(bdg);
                                }
                            }
                            break;
                    }
                }
            }

            return badgesConquistados;
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public void gravarClienteBadge(String ctx, ClienteSintetico cliente, Badge badge, Date dataConquista, BadgeService bs) throws Exception {
        ClienteBadge cliBadge = new ClienteBadge();
        cliBadge.setCliente(cliente);
        cliBadge.setBadge(badge);
        cliBadge.setDataConquista(dataConquista);
        getClienteBadge().insert(ctx, cliBadge);
    }

    public List<ProgramaTreino> obterProgramasPorCliente(final String ctx, final Integer codigoCliente, final Date inicio, final Date fim,
                                                         final Integer codigoProgramaTreino, Integer maxResults)
            throws ServiceException {
        try {
            HashMap<String, Object> p = new HashMap<String, Object>();
            StringBuilder query = new StringBuilder();
            query.append(" select obj from ProgramaTreino obj where cliente.codigo = :cliente ");
            if (inicio != null && fim != null) {
                query.append(" and ((obj.dataInicio between :inicio and :fim) OR ");
                query.append(" (obj.dataTerminoPrevisto between :inicio and :fim) OR ");
                query.append(" (obj.dataInicio <= :inicio and obj.dataTerminoPrevisto >= :fim)) ");
                p.put("inicio", inicio);
                p.put("fim", fim);
            }
            if (codigoProgramaTreino != null && codigoProgramaTreino > 0) {
                query.append(" and obj.codigo < :codigoProgramaTreino");
                p.put("codigoProgramaTreino", codigoProgramaTreino);
            }
            query.append(" order by obj.dataTerminoPrevisto desc");

            p.put("cliente", codigoCliente);
            if (maxResults != null) {
                return getProgramaTreinoDao().findByParam(ctx, query.toString(), p, maxResults, 0);
            } else {
                return getProgramaTreinoDao().findByParam(ctx, query.toString(), p);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }


    public List<ProgramaTreino> obterProgramasPorClienteZW(final String ctx, final Integer codigoCliente, final Date inicio, final Date fim,
                                                           final Integer codigoProgramaTreino, Integer maxResults)
            throws ServiceException {
        try {
            HashMap<String, Object> p = new HashMap<String, Object>();
            StringBuilder query = new StringBuilder();
            query.append(" select obj from ProgramaTreino obj where cliente.codigoCliente = :cliente ");
            if (inicio != null && fim != null) {
                query.append(" and ((obj.dataInicio between :inicio and :fim) OR ");
                query.append(" (obj.dataTerminoPrevisto between :inicio and :fim) OR ");
                query.append(" (obj.dataInicio <= :inicio and obj.dataTerminoPrevisto >= :fim)) ");
                p.put("inicio", inicio);
                p.put("fim", fim);
            }
            if (codigoProgramaTreino != null && codigoProgramaTreino > 0) {
                query.append(" and obj.codigo < :codigoProgramaTreino");
                p.put("codigoProgramaTreino", codigoProgramaTreino);
            }
            query.append(" order by obj.dataTerminoPrevisto desc");

            p.put("cliente", codigoCliente);
            if (maxResults != null) {
                return getProgramaTreinoDao().findByParam(ctx, query.toString(), p, maxResults, 0);
            } else {
                return getProgramaTreinoDao().findByParam(ctx, query.toString(), p);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ProgramaTreino> obterProgramasPorColaborador(final String ctx, final Integer codigoColaborador, final Date inicio, final Date fim,
                                                             final Integer codigoProgramaTreino, Integer maxResults)
            throws ServiceException {
        try {
            HashMap<String, Object> p = new HashMap<String, Object>();
            StringBuilder query = new StringBuilder();
            query.append(" select obj from ProgramaTreino obj where codigocolaborador = :colaborador ");
            if (inicio != null && fim != null) {
                query.append(" and ((obj.dataInicio between :inicio and :fim) OR ");
                query.append(" (obj.dataTerminoPrevisto between :inicio and :fim) OR ");
                query.append(" (obj.dataInicio <= :inicio and obj.dataTerminoPrevisto >= :fim)) ");
                p.put("inicio", inicio);
                p.put("fim", fim);
            }
            if (codigoProgramaTreino != null && codigoProgramaTreino > 0) {
                query.append(" and obj.codigo < :codigoProgramaTreino");
                p.put("codigoProgramaTreino", codigoProgramaTreino);
            }
            query.append(" order by obj.dataTerminoPrevisto desc");

            p.put("colaborador", codigoColaborador);
            if (maxResults != null) {
                return getProgramaTreinoDao().findByParam(ctx, query.toString(), p, maxResults, 0);
            } else {
                return getProgramaTreinoDao().findByParam(ctx, query.toString(), p);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ProgramaTreinoFicha obterProgramasFicha(final String ctx, final Integer programa, final Integer ficha)
            throws ServiceException {
        try {
            String query = "select obj from ProgramaTreinoFicha obj "
                    + "where obj.ficha.codigo = :ficha and obj.programa.codigo = :programa ";
            HashMap<String, Object> p = new HashMap<String, Object>();
            p.put("ficha", ficha);
            p.put("programa", programa);
            return getProgramaTreinoFichaDao().findObjectByParam(ctx, query.toString(), p);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ProgramaTreinoFicha> obterProgramasFichasPorPrograma(String ctx, Integer programaId) throws ServiceException {
        return getProgramaTreinoFichaDao().obterPorProgramaTreino(ctx, programaId);
    }

    public ProgramaTreinoFicha alterarProgramaFicha(final String ctx, ProgramaTreinoFicha object) throws ServiceException {
        try {
            verificarAlteracoesProgramaFicha(ctx, object);
            return getProgramaTreinoFichaDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluirProgramaFicha(final String ctx, ProgramaTreinoFicha object) throws ServiceException {
        try {
            getProgramaTreinoFichaDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ProgramaTreinoFicha inserirProgramaFicha(final String ctx, ProgramaTreinoFicha object) throws ServiceException {
        try {
            object.setVersao(1);
            atualizarVersaoPrograma(ctx, object.getPrograma());
            return getProgramaTreinoFichaDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public void atualizar(final String ctx, ProgramaTreino object) throws ServiceException {
        try {
            getProgramaTreinoDao().refresh(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ProgramaTreinoFicha> obterFichaPorPrograma(final String ctx, Integer codigo, Integer codFicha, boolean todos) throws ServiceException {

        Map<String, Object> params = new HashMap<String, Object>();
        String s = "SELECT obj FROM ProgramaTreinoFicha obj where obj.programa.codigo = :_codigo  ";
        if (!todos) {
            s += " and obj.tipoExecucao is not null ";
        }
        if (codFicha != null && codFicha > 0) {
            s += " and obj.ficha.codigo <> :codProgramaFicha";
            params.put("codProgramaFicha", codFicha);
        }
        s += " ORDER BY obj.ficha.nome";
        params.put("_codigo", codigo);

        try {
            return getProgramaTreinoFichaDao().findByParam(ctx, s, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void ordenarFichas(final String ctx, ProgramaTreino programa) throws ServiceException {
        try {
            if (programa == null || programa.getProgramaFichas() == null || programa.getProgramaFichas().isEmpty()) {
                return;
            }
            ordenarProgramaTreinoFichas(ctx, programa.getCodigo(), programa.getProgramaFichas());
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public void ordenarProgramaTreinoFichas(final String ctx, final Integer codigoPrograma, List<ProgramaTreinoFicha> listaFichas) throws ServiceException {
        Integer fichaAtualCodigo = obterFichaAtual(ctx, codigoPrograma, null);
        Ordenacao.ordenarLista(listaFichas, "codigo");
        for (ProgramaTreinoFicha ptf : new ArrayList<ProgramaTreinoFicha>(listaFichas)) {
            if (ptf.getTipoExecucao() == null) {
                listaFichas.remove(ptf);
            }
            if (ptf.getFicha().getCodigo().equals(fichaAtualCodigo)) {
                listaFichas.remove(ptf);
                listaFichas.add(0, ptf);
                break;
            }
        }
    }

    public Map<Integer, String> obterUltimosTreinosPrograma(final String ctx, final Integer codigoPrograma) throws Exception {
        String s = " SELECT codigo, ficha_codigo FROM ProgramaTreinoFicha "
                + " where programa_codigo = " + codigoPrograma;
        Map<Integer, String> arr;
        try (ResultSet rs = getProgramaTreinoFichaDao().createStatement(ctx, s)) {
            arr = new HashMap<Integer, String>();
            while (rs.next()) {
                List datas = getTreinoRealizadoDao().listOfObjects(ctx, "SELECT dataInicio FROM TreinoRealizado "
                        + " WHERE programaTreinoFicha_codigo = " + rs.getInt("codigo") + " ORDER BY dataInicio DESC LIMIT 1");
                arr.put(rs.getInt("ficha_codigo"), datas == null
                        || datas.isEmpty() ? "" : Uteis.getDataAplicandoFormatacao((Date) datas.get(0), "dd/MM/yyyy HH:mm"));
            }
        }
        return arr;
    }

    public Integer obterFichaAtual(String ctx, Integer codigoPrograma, TreinoRealizado ultimoTreino) throws ServiceException {
        Calendar calendar = Calendario.getInstance();
        Integer diaSemanaHoje = calendar.get(Calendar.DAY_OF_WEEK);
        List<ProgramaTreinoFicha> fichasPorPrograma = obterFichaPorPrograma(ctx, codigoPrograma, null, false);
        //verificar se existe uma ficha para o dia da semana atual
        for (ProgramaTreinoFicha ptf : fichasPorPrograma) {
            if (ptf.getTipoExecucao() != null
                    && ptf.getTipoExecucao().equals(TipoExecucaoEnum.DIAS_SEMANA)
                    && ptf.isFichaDeHoje(Calendario.hoje())) {
                return ptf.getFicha().getCodigo();
            }
        }
        //se não existe uma ficha para hoje, deve-se pegar uma ficha com base em algumas regras:
        //ficha alternada que nunca foi treinada
        //ficha alternada que foi treinada a mais tempo
        //ficha nunca treinada

        ProgramaTreinoFicha fichaMaisAntiga = null;
        ProgramaTreinoFicha fichaAlternadaNuncaTreinada = null;
        ProgramaTreinoFicha fichaMaisProxima = null;
        int diaSemanaProximo = 0;
        Ordenacao.ordenarLista(fichasPorPrograma, "codigo");

        for (ProgramaTreinoFicha ptf : fichasPorPrograma) {
            List<TreinoRealizado> ultimosTreinosRealizadosFicha = obterUltimosTreinosRealizadosFicha(ctx, ptf.getCodigo(), 1);
            //se ela é alternada e nunca foi treinada
            if (ptf.getTipoExecucao() != null
                    && ptf.getTipoExecucao().equals(TipoExecucaoEnum.ALTERNADO)
                    && (ultimosTreinosRealizadosFicha == null || ultimosTreinosRealizadosFicha.isEmpty())
                    && fichaAlternadaNuncaTreinada == null) {
                fichaAlternadaNuncaTreinada = ptf;
            }
            //é uma ficha alternada e
            if ((ptf.getTipoExecucao() != null
                    && ptf.getTipoExecucao().equals(TipoExecucaoEnum.ALTERNADO))
                    //não tem ainda ficha mais antiga
                    && (fichaMaisAntiga == null
                    //ou ela foi treinada antes da já escolhida como mais antiga
                    || (fichaMaisAntiga.getUltimaVezTreinada() != null
                    && (ultimosTreinosRealizadosFicha != null && !ultimosTreinosRealizadosFicha.isEmpty()
                    && Calendario.menorComHora(ultimosTreinosRealizadosFicha.get(0).getDataInicio(), fichaMaisAntiga.getUltimaVezTreinada()))))) {

                fichaMaisAntiga = ptf;
                fichaMaisAntiga.setUltimaVezTreinada(ultimosTreinosRealizadosFicha == null || ultimosTreinosRealizadosFicha.isEmpty() ? null
                        : ultimosTreinosRealizadosFicha.get(0).getDataInicio());
            }

            //é uma ficha com dia da semana e é a próxima
            if (ptf.getDiaSemana() != null) {
                for (String diaS : ptf.getDiaSemana()) {
                    DiasSemana ds = DiasSemana.getDiaSemana(diaS);
                    int diaSemanaTemp = ds.getNumeral();
                    if ((diaSemanaProximo == 0
                            || (diaSemanaTemp > diaSemanaHoje && diaSemanaTemp < diaSemanaProximo)
                            || (diaSemanaProximo < diaSemanaHoje && diaSemanaTemp > diaSemanaHoje))) {
                        diaSemanaProximo = diaSemanaTemp;
                        fichaMaisProxima = ptf;
                    }
                }
            }
        }

        return fichaAlternadaNuncaTreinada == null ? (fichaMaisAntiga == null
                ? (fichaMaisProxima == null ? null : fichaMaisProxima.getFicha().getCodigo())
                : fichaMaisAntiga.getFicha().getCodigo())
                : fichaAlternadaNuncaTreinada.getFicha().getCodigo();
    }

    public Integer obterProximaFicha(String ctx, Integer codigoPrograma, Ficha fichaAtual) throws ServiceException {
        Calendar diaAtual = Calendario.getInstance();
        diaAtual.add(5, 1);
        Integer diaSemanaHoje = diaAtual.get(Calendar.DAY_OF_WEEK);
        List<ProgramaTreinoFicha> fichasPorPrograma = obterFichaPorPrograma(ctx, codigoPrograma, null, false);
        //verificar se existe uma ficha para o dia da semana atual
        for (ProgramaTreinoFicha ptf : fichasPorPrograma) {
            if (ptf.getTipoExecucao() != null
                    && ptf.getTipoExecucao().equals(TipoExecucaoEnum.DIAS_SEMANA)
                    && ptf.isFichaDeHoje(diaAtual.getTime())) {
                return ptf.getFicha().getCodigo();
            }
        }

        //se não existe uma ficha para hoje, deve-se pegar uma ficha com base em algumas regras:
        //ficha alternada que nunca foi treinada
        //ficha alternada que foi treinada a mais tempo
        //ficha nunca treinada

        ProgramaTreinoFicha fichaMaisAntiga = null;
        ProgramaTreinoFicha fichaAlternadaNuncaTreinada = null;
        ProgramaTreinoFicha fichaMaisProxima = null;
        int diaSemanaProximo = 0;
        Ordenacao.ordenarLista(fichasPorPrograma, "codigo");

        Set<Integer> qtdFichasDiferentes = new HashSet<>();
        for (ProgramaTreinoFicha ptf : fichasPorPrograma) {
            qtdFichasDiferentes.add(ptf.getFicha().getCodigo());
        }

        for (ProgramaTreinoFicha ptf : fichasPorPrograma) {
            if (qtdFichasDiferentes.size() > 1 &&
                    fichaAtual != null && ptf.getFicha().getCodigo().equals(fichaAtual.getCodigo())) {
                continue;
            }

            List<TreinoRealizado> ultimosTreinosRealizadosFicha = obterUltimosTreinosRealizadosFicha(ctx, ptf.getCodigo(), 1);
            //se ela é alternada e nunca foi treinada
            if (ptf.getTipoExecucao() != null
                    && ptf.getTipoExecucao().equals(TipoExecucaoEnum.ALTERNADO)
                    && (ultimosTreinosRealizadosFicha == null || ultimosTreinosRealizadosFicha.isEmpty())
                    && fichaAlternadaNuncaTreinada == null) {
                fichaAlternadaNuncaTreinada = ptf;
            }
            //é uma ficha alternada e
            if ((ptf.getTipoExecucao() != null
                    && ptf.getTipoExecucao().equals(TipoExecucaoEnum.ALTERNADO))
                    //não tem ainda ficha mais antiga
                    && (fichaMaisAntiga == null
                    //ou ela foi treinada antes da já escolhida como mais antiga
                    || (fichaMaisAntiga.getUltimaVezTreinada() != null
                    && (ultimosTreinosRealizadosFicha != null && !ultimosTreinosRealizadosFicha.isEmpty()
                    && Calendario.menorComHora(ultimosTreinosRealizadosFicha.get(0).getDataInicio(), fichaMaisAntiga.getUltimaVezTreinada()))))) {

                fichaMaisAntiga = ptf;
                fichaMaisAntiga.setUltimaVezTreinada(ultimosTreinosRealizadosFicha == null || ultimosTreinosRealizadosFicha.isEmpty() ? null
                        : ultimosTreinosRealizadosFicha.get(0).getDataInicio());
            }

            //é uma ficha com dia da semana e é a próxima
            if (ptf.getDiaSemana() != null) {
                for (String diaS : ptf.getDiaSemana()) {
                    DiasSemana ds = DiasSemana.getDiaSemana(diaS);
                    int diaSemanaTemp = ds.getNumeral();
                    if ((diaSemanaProximo == 0
                            || (diaSemanaTemp > diaSemanaHoje && diaSemanaTemp < diaSemanaProximo)
                            || (diaSemanaProximo < diaSemanaHoje && diaSemanaTemp > diaSemanaHoje))) {
                        diaSemanaProximo = diaSemanaTemp;
                        fichaMaisProxima = ptf;
                    }
                }
            }
        }

        return fichaAlternadaNuncaTreinada == null ? (fichaMaisAntiga == null
                ? (fichaMaisProxima == null ? null : fichaMaisProxima.getFicha().getCodigo())
                : fichaMaisAntiga.getFicha().getCodigo())
                : fichaAlternadaNuncaTreinada.getFicha().getCodigo();
    }

    public TreinoRealizado obterUltimoTreinoRealizadoFicha(final String ctx, final Integer idFicha) throws ServiceException {
        try {
            List<TreinoRealizado> treinoRealizados = getTreinoRealizadoDao().findListByAttributes(ctx, new String[]{"programaTreinoFicha.ficha.codigo"}, new Object[]{idFicha},
                    "dataInicio DESC", 1);
            return treinoRealizados.isEmpty() ? null : treinoRealizados.get(0);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<TreinoRealizado> obterUltimosTreinosRealizadosFicha(final String ctx, final Integer idProgramaFicha,
                                                                    final int maxResults) throws ServiceException {
        try {
            return getTreinoRealizadoDao().findListByAttributes(ctx, new String[]{"programaTreinoFicha.codigo"}, new Object[]{idProgramaFicha},
                    "dataInicio DESC", maxResults);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public String marcarTreinoRealizadoFichaAvulsa(final String ctx, final Integer ficha, final String usuario, final String dia) throws ServiceException {
        try {
            Date data = Uteis.getDate(dia.replace("-", "/"));
            StringBuilder sql = new StringBuilder();
            sql.append("Update TreinoRealizado set executadoFichaDia = false WHERE codigo in (Select tr.codigo from TreinoRealizado as tr");
            sql.append(" INNER JOIN ProgramaTreinoFicha ptf ON ptf.codigo = tr.ProgramaTreinoFicha_codigo and ptf.ficha_codigo = ").append(ficha).append("\n");
            sql.append(" INNER JOIN ClienteSintetico cs ON cs.codigo = tr.cliente_codigo ");
            sql.append(" INNER JOIN Usuario u ON u.cliente_codigo = cs.codigo ");
            sql.append(" WHERE ");
            sql.append(" tr.dataInicio BETWEEN '").append(Calendario.getDataComHora(data, "00:00:00")).append("' AND '").append(Calendario.getDataComHora(data, "23:59:59")).append("'\n");
            sql.append(" AND u.userName = '").append(usuario).append("')\n");
            getProgramaTreinoDao().executeNativeSQL(ctx, sql.toString());
            return "OK";
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public TreinoRealizado fichaSendoExecutada(final String ctx, final Date hoje, final Integer codigoPrograma) throws ServiceException {
        try {

            Map<String, Object> params = new HashMap<String, Object>();
            String s = "SELECT obj FROM TreinoRealizado obj where obj.dataFim is null  ";
            s += " AND obj.dataInicio BETWEEN :inicio and :fim";
            s += " AND obj.programaTreinoFicha.programa.codigo = :codigo";
            s += " ORDER BY obj.programaTreinoFicha.ficha.nome";
            params.put("codigo", codigoPrograma);
            params.put("inicio", Calendario.getDataComHoraZerada(hoje));
            params.put("fim", Calendario.getDataComHora(hoje, "23:59:59"));
            List<TreinoRealizado> findListByAttributes = getTreinoRealizadoDao().findByParam(ctx, s, params);
            return findListByAttributes == null || findListByAttributes.isEmpty() ? null : findListByAttributes.get(0);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public Double calcularAndamentoFicha(final String ctx, Integer ficha, Integer treinoRealizado) throws ServiceException {
        try {
            Long series = (Long) getSerieDao().count(ctx, "codigo", new String[]{"atividadeFicha.ficha.codigo"},
                    new Object[]{ficha});

            Long seriesRealizadas = (Long) getSerieRealizadaDao().count(ctx, "codigo", new String[]{"treinoRealizado.codigo"},
                    new Object[]{treinoRealizado});
            if (seriesRealizadas <= 0) {
                return 0.0;
            }
            return (seriesRealizadas.doubleValue() / series.doubleValue()) * 100;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<TreinoRealizado> obterTreinosRealizado(final String ctx,
                                                       final Integer codigoPrograma, Date inicio, Date fim,
                                                       final Integer codigoCliente, final Integer maxResults, final Integer index)
            throws ServiceException {
        return obterTreinosRealizadoCliente(ctx, codigoPrograma, inicio, fim, codigoCliente, maxResults, index, false);
    }

    @Override
    public List<TreinoRealizado> obterTodosTreinosRealizadoPrograma(final String ctx,
                                                                    final Integer codigoPrograma, Date inicio, Date fim,
                                                                    final Integer codigoCliente, final Integer maxResults, final Integer index)
            throws ServiceException {
        return obterTreinosRealizadoCliente(ctx, codigoPrograma, inicio, fim, codigoCliente, maxResults, index, true);
    }

    public List<TreinoRealizado> obterTreinosRealizadoCliente(final String ctx,
                                                              final Integer codigoPrograma, Date inicio, Date fim,
                                                              final Integer codigoCliente, final Integer maxResults, final Integer index, boolean treinosForaVigenciaPrograma)
            throws ServiceException {
        Map<String, Object> params = new HashMap<String, Object>();
        StringBuilder s = new StringBuilder(" where ");
        if (treinosForaVigenciaPrograma) {
            s.append(" (obj.dataInicio between :inicio and :fim or obj.programaTreinoFicha.programa.dataInicio  > obj.dataInicio) ");
        } else {
            s.append(" obj.dataInicio between :inicio and :fim ");
        }
        if (codigoCliente == null) {
            s.append(" and obj.programaTreinoFicha.programa.codigo = :codigoPrograma  ");
            params.put("codigoPrograma", codigoPrograma);
        } else {
            if (codigoPrograma != null) {
                s.append(" and obj.programaTreinoFicha.programa.codigo = :codigoPrograma  ");
                params.put("codigoPrograma", codigoPrograma);
            }
            s.append(" and obj.cliente.codigo = :codigoCliente  ");
            params.put("codigoCliente", codigoCliente);
        }
        s.append(" order by obj.dataInicio DESC ");
        params.put("inicio", Calendario.getDataComHora(inicio, "00:00:00"));
        params.put("fim", Calendario.getDataComHora(fim, "23:59:59"));

        try {
            if (index != null && maxResults != null) {
                return getTreinoRealizadoDao().findByParam(ctx, s, params,
                        maxResults, index);
            } else {
                return getTreinoRealizadoDao().findByParam(ctx, s, params);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    public void povoarAndamentoTreino(String ctx) throws ServiceException {
        List<ProgramaTreino> todos = obterTodos(ctx);
        for (ProgramaTreino pt : todos) {
            List<TreinoRealizado> treinosRealizados = obterUltimosTreinosRealizados(ctx, pt.getCodigo(), 1000);
            for (TreinoRealizado treino : treinosRealizados) {
                adicionarAndamentoPrograma(ctx, pt, treino, true);
            }
        }
    }

    public void corrigirVersoesProgramasRenovados(final String ctx) throws ServiceException {
        Map<String, Object> p = new HashMap<String, Object>();
        p.put(ctx, "");
        try {
            List<ProgramaTreino> programas = programatreinoDao.findByParam(ctx,
                    new StringBuilder("current_timestamp between obj.dataInicio and obj.dataTerminoPrevisto").
                            append(" and "),
                    p);
        } catch (Exception ex) {
            Logger.getLogger(ProgramaTreinoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void verificarSeriesRealizadas(String ctx, ProgramaTreinoFicha ficha, TreinoRealizado treino) throws ServiceException {
        try {
            for (AtividadeFicha atividadeFicha : ficha.getFicha().getAtividades()) {
                atividadeFicha.setNrSeriesRealizadas(((Long) getSerieRealizadaDao().count(ctx, "codigo", new String[]{"treinoRealizado.codigo", "atividadeFicha.codigo"},
                        new Object[]{treino.getCodigo(), atividadeFicha.getCodigo()})).intValue());
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void verificarAlteracoes(final String ctx, ProgramaTreino programaTreino, boolean alterouObjetivos, final ProfessorSintetico professor) throws ServiceException {
        try {
            ProgramaTreino antesAlteracao = programaTreino.getProgramaAntesAlteracao();
            if (antesAlteracao == null) {
                return;
            }
            //nome
            if (!antesAlteracao.getNome().equals(programaTreino.getNome())
                    //dias por semana
                    || (antesAlteracao.getDiasPorSemana().intValue() != programaTreino.getDiasPorSemana().intValue())
                    //inicio
                    || (antesAlteracao.getDataInicio().getTime() != programaTreino.getDataInicio().getTime())
                    //revisao
                    || (antesAlteracao.getDataProximaRevisao().getTime() != programaTreino.getDataProximaRevisao().getTime())
                    //fim
                    || (antesAlteracao.getDataTerminoPrevisto().getTime() != programaTreino.getDataTerminoPrevisto().getTime())
                    //objetivos
                    || alterouObjetivos) {

                atualizarVersaoPrograma(ctx, programaTreino);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void gravarHistoricoRevisoes(final String ctx, final ProgramaTreino programa,
                                        final String justificativa,
                                        final Date proximaRevisao,
                                        final ProfessorSintetico professor) throws Exception {
        HistoricoRevisaoProgramaTreino histExist = historicoRevisaoDao.findObjectByAttributes(ctx,
                new String[]{"programa.codigo", "proximaRevisao"}, new Object[]{programa.getCodigo(), proximaRevisao}, "codigo desc");
        if (histExist != null) {
            histExist.setPrograma(programa);
            histExist.setProximaRevisao(proximaRevisao);
            histExist.setProfessorRevisou(professor);
            histExist.setDataRegistro(Calendario.hoje());
            histExist.setCliente(programa.getCliente());
            histExist.setJustificativa(justificativa);
            historicoRevisaoDao.update(ctx, histExist);
        } else {
            if (justificativa != null && !justificativa.trim().isEmpty()) {
                HistoricoRevisaoProgramaTreino historico = new HistoricoRevisaoProgramaTreino();
                historico.setPrograma(programa);
                historico.setProximaRevisao(proximaRevisao);
                historico.setProfessorRevisou(professor);
                historico.setDataRegistro(Calendario.hoje());
                historico.setCliente(programa.getCliente());
                historico.setJustificativa(justificativa);
                historicoRevisaoDao.insert(ctx, historico);
                programa.setDataProximaRevisao(proximaRevisao);
                programatreinoDao.update(ctx, programa);
            } else {
                throw new ValidacaoException(getViewUtils().getMensagem("obrigatorio.justificativa.revisao"));
            }
        }
    }

    @Override
    public void carregarHistoricoRevisoes(final String ctx, ProgramaTreino programa) throws Exception {
        programa.setHistoricoRevicoes(historicoRevisaoDao.findListByAttributes(ctx, new String[]{"programa.codigo"},
                new Object[]{programa.getCodigo()}, "dataRegistro desc", 0));
    }

    @Override
    public void carregarHistoricoRevisoesSincronizacao(final String ctx, ProgramaTreino programa) throws Exception {
        programa.setHistoricoRevicoesSincronizacao(historicoRevisaoSincronizacaoDao.findListByAttributes(ctx, new String[]{"chaveprimaria", "entidade"},
                new Object[]{programa.getCodigo(), TipoClassSincronizarEnum.ProgramaTreino}, "dataRegistro desc", 0));
    }

    public void atualizarVersaoPrograma(String ctx, ProgramaTreino programa) throws Exception {

        ProgramaTreino prTreino = obterPorId(ctx, programa.getCodigo());
        if (prTreino == null) {
            return;
        }

        prTreino.setVersao(prTreino.getVersao() + 1);
//        getProgramaTreinoDao().updateSomeFildsNoFlush(ctx, new String[]{"versao"},
//                new Object[]{prTreino.getVersao()},
//                new String[]{"codigo"}, new Object[]{prTreino.getCodigo()});
        getProgramaTreinoDao().update(ctx, prTreino);
        programa.manterAntesAlteracao();

    }

    public void atualizarVersaoProgramaSemGerarLog(String ctx, ProgramaTreino programa) throws Exception {
        ProgramaTreino prTreino = obterPorId(ctx, programa.getCodigo());
        if (prTreino == null) {
            return;
        }
        prTreino.setVersao(prTreino.getVersao() + 1);
        getProgramaTreinoDao().updateSomeFildsNoFlush(ctx, new String[]{"versao"},
                new Object[]{prTreino.getVersao()},
                new String[]{"codigo"}, new Object[]{prTreino.getCodigo()});
        programa.manterAntesAlteracao();
    }

    public void atualizarVersaoProgramas(String ctx, List<Integer> codsPrograma) throws Exception {
        StringBuilder sqlIncrementarVersaoProgramaTreino = new StringBuilder();
        sqlIncrementarVersaoProgramaTreino.append("UPDATE programatreino\n")
                .append("SET versao = versao + 1\n")
                .append("WHERE codigo IN ( ")
                .append(StringUtils.join(codsPrograma.toArray(new Integer[]{}), ","))
                .append(")");
        getProgramaTreinoDao().executeNativeSQL(ctx, sqlIncrementarVersaoProgramaTreino.toString());
    }

    public void verificarAlteracoesProgramaFicha(String ctx, ProgramaTreinoFicha programaTreinoFicha) throws ServiceException {
        try {
            ProgramaTreinoFicha antesAlteracao = programaTreinoFicha.getFichaAntesAlteracao();
            if (antesAlteracao == null) {
                return;
            }
            //nome
            if (!antesAlteracao.getFicha().getNome().equals(programaTreinoFicha.getFicha().getNome())
                    //categoria
                    || CategoriaFicha.diferente(antesAlteracao.getFicha().getCategoria(), programaTreinoFicha.getFicha().getCategoria())
                    //mensagem
                    || Uteis.diferente(antesAlteracao.getFicha().getMensagemAluno(), programaTreinoFicha.getFicha().getMensagemAluno())
                    //tipo execucao
                    || (antesAlteracao.getTipoExecucao() == null && programaTreinoFicha.getTipoExecucao() != null)
                    || (antesAlteracao.getTipoExecucao() != null && programaTreinoFicha.getTipoExecucao() == null)
                    || (!antesAlteracao.getTipoExecucao().equals(programaTreinoFicha.getTipoExecucao()))
                    //dia semana
                    || (antesAlteracao.getDiaSemana() == null && programaTreinoFicha.getDiaSemana() != null)
                    || (antesAlteracao.getDiaSemana() != null && programaTreinoFicha.getDiaSemana() == null)
                    || (antesAlteracao.getDiaSemana() != null && programaTreinoFicha.getDiaSemana() != null
                    && !antesAlteracao.getDiaSemanaAsString().equals(programaTreinoFicha.getDiaSemanaAsString()))) {
                programaTreinoFicha.setVersao(antesAlteracao.getVersao() + 1);
                getProgramatreinofichaDao().updateAlgunsCampos(ctx, new String[]{"versao"},
                        new Object[]{programaTreinoFicha.getVersao()},
                        new String[]{"codigo"}, new Object[]{programaTreinoFicha.getCodigo()});
                atualizarVersaoPrograma(ctx, programaTreinoFicha.getPrograma());
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ProgramaTreinoFicha obterProgramaTreinoFichaPorFicha(final String ctx, final Integer ficha) throws ServiceException {
        try {
            ProgramaTreinoFicha programaTreinoFicha = getProgramaTreinoFichaDao().findObjectByAttributes(ctx, new String[]{"ficha.codigo"},
                    new Object[]{ficha}, "ficha.nome");
            if (programaTreinoFicha != null) {
                getProgramaTreinoFichaDao().refresh(ctx, programaTreinoFicha);
            }
            return programaTreinoFicha;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public void obterObjetivosDoPrograma(String ctx, ProgramaTreino programa) throws ServiceException {
        try {
            programa.setObjetivos(obterObjetivosPrograma(ctx, programa));
            List<ObjetivoPredefinido> objetivos = objetivoService.obterTodos(ctx);

            if (programa.getObjetivosPredef() == null) {
                programa.setObjetivosPredef(new ArrayList<GenericoTO>());
            }
            for (ObjetivoPredefinido objPred : objetivos) {
                GenericoTO generico = new GenericoTO(objPred.getCodigo(), objPred.getNome());
                generico.setEscolhido(false);

                for (ObjetivoPrograma objProg : programa.getObjetivos()) {
                    if (objPred.getCodigo().intValue() == objProg.getObjetivo().getCodigo().intValue()) {
                        generico.setEscolhido(true);
                    }
                }
                if (!programa.getObjetivosPredef().contains(generico)) {
                    programa.getObjetivosPredef().add(generico);
                }
            }

        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public void obterRestricoesPrograma(String ctx, ProgramaTreino programa) throws ServiceException {
        try {
            if (UteisValidacao.emptyList(programa.getRestricoes())) {
                programa.setRestricoes(new ArrayList<GenericoTO>());
                for (RestricoesEnum rest : RestricoesEnum.values()) {
                    GenericoTO generico = new GenericoTO(rest.ordinal(), getViewUtils().getLabel(rest.name()));

                    generico.setEscolhido(programa.getRestricoesTreino() != null
                            && programa.getRestricoesTreino().contains(rest));

                    programa.getRestricoes().add(generico);
                }
            }

        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public boolean prepararPersistirObjetivos(String ctx, ProgramaTreino programa) throws Exception {
        boolean retorno = false;
        List<ObjetivoPrograma> listaPersistida = new ArrayList<ObjetivoPrograma>(programa.getObjetivos());
        for (GenericoTO objPred : programa.getObjetivosPredef()) {
            boolean adicionar = objPred.getEscolhido();
            for (ObjetivoPrograma objProg : listaPersistida) {
                if (objPred.getEscolhido() && objProg.getObjetivo().getCodigo().intValue() == objPred.getCodigo().intValue()) {
                    adicionar = false;
                }
                if (!objPred.getEscolhido() && objProg.getObjetivo().getCodigo().intValue() == objPred.getCodigo().intValue()) {
                    removerObjetivo(ctx, programa, objProg);
                    retorno = true;
                }
            }
            if (adicionar) {
                ObjetivoPrograma objAdd = new ObjetivoPrograma(getObjetivoPredefinidoDao().findById(ctx, objPred.getCodigo()), programa);
                programa.getObjetivos().add(objAdd);
                retorno = true;
            }
        }
        return retorno;
    }

    public void prepararPersistirRestricoes(String ctx, ProgramaTreino programa) throws Exception {
        programa.setRestricoesTreino(new HashSet<RestricoesEnum>());
        for (GenericoTO rest : programa.getRestricoes()) {
            if (rest.getEscolhido()) {
                for (RestricoesEnum r : RestricoesEnum.values()) {
                    if (r.ordinal() == rest.getCodigo().intValue()) {
                        programa.getRestricoesTreino().add(r);
                    }
                }
            }
        }
    }

    public void removerObjetivo(String ctx, ProgramaTreino programa, ObjetivoPrograma objetivo) throws Exception {
        programa.getObjetivos().remove(objetivo);
        objetivoProgramaDao.delete(ctx, objetivo);

    }

    public List<ObjetivoPrograma> obterObjetivosPrograma(String ctx, ProgramaTreino programa) throws ServiceException {
        try {
            return objetivoProgramaDao.findListByAttributes(ctx, new String[]{"programa.codigo"}, new Object[]{programa.getCodigo()}, null, 0);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

    }

    public void resetSpinsSeries(Ficha ficha, boolean isMobile) {
        if (!isMobile) {
            for (AtividadeFicha atvF : ficha.getAtividades()) {
                for (Serie serie : atvF.getSeries()) {
                    serie.resetSpins();
                }
            }
        }
    }

    @Override
    public Ficha gravarFichaSemTratarExcecao(String ctx, boolean cadastroPrograma,
                                             TipoExecucaoEnum tipoExecucaoEnum,
                                             List<String> diasSemana, ProgramaTreino programa, Ficha ficha, Integer codigoCategoria,
                                             ProgramaTreinoFicha fichaAntesAlteracao, boolean adicionandoFichaPreDefinida,
                                             final ProfessorSintetico professor, boolean isMobile) throws Exception {
        if (diasSemana != null && diasSemana.isEmpty()) {
            diasSemana = null;
        }
        if (cadastroPrograma) {
            //obter a forma de execucao e o dia da semana
            ficha.setNome(validacao.validarFicha(programa, ctx, ficha.getCodigo(), ficha.getNome(), false, tipoExecucaoEnum, adicionandoFichaPreDefinida));
        } else {
            fichaValidacaoService.validarNomeFichaPreDefinida(ctx, ficha.getCodigo(), ficha.getNome());
            ficha.setUsarComoPredefinida(true);
        }
        if (codigoCategoria != null && codigoCategoria > 0) {
            ficha.setCategoria(new CategoriaFicha());
            ficha.getCategoria().setCodigo(codigoCategoria);
        }

        resetSpinsSeries(ficha, isMobile);

        if (ficha.getCodigo() == null || ficha.getCodigo() <= 0) {
            ficha = fichaService.inserir(ctx, ficha);

            for (AtividadeFicha atvF : ficha.getAtividades()) {
                if (atvF.getCodigo() == null || atvF.getCodigo().equals(0)) {
                    atvF.setCodigo(null);
                    atvF = fichaService.inserirAtividadeFicha(ctx, atvF);
                    for (Serie serie : atvF.getSeries()) {
                        if (serie.getCodigo() == null || serie.getCodigo() == 0) {
                            serie.setRepeticaoApp(serie.getRepeticao() + " - " + serie.getRepeticaoComp());
                            serie.setCargaApp(serie.getCarga() + " - " + serie.getCargaComp());
                            serie.setAtualizadoApp(true);
                            serieService.inserir(ctx, serie);
                        }
                    }
                }
            }

            for (AtividadeFicha atvF : ficha.getAtividades()) {
                for (AtividadeFicha outraAtv : ficha.getAtividades()) {
                    if (!UteisValidacao.emptyString(outraAtv.getSetId())
                            && outraAtv.getSetId().contains(atvF.getCodigoAntigo().toString())) {
                        outraAtv.setSetId(outraAtv.getSetId().replaceAll(atvF.getCodigoAntigo().toString(), atvF.getCodigo().toString()));
                        atividadeFichaDao.update(ctx, atvF);
                    }
                }
            }

        } else {
            /*ficha.setAtividades(fichaService.obterAtividadesFicha(ctx, ficha.getCodigo()));*/


            for (AtividadeFicha atvF : ficha.getAtividades()) {
                for (Serie serie : atvF.getSeriesRemover()) {
                    if (serie.getCodigo() != null || serie.getCodigo() > 0) {
                        serieService.excluir(ctx, serie);
                    }
                }
            }


            ficha = fichaService.alterar(ctx, ficha, true);
            fichaService.refresh(ctx, ficha);
        }
        ficha = fichaService.obterPorId(ctx, ficha.getCodigo());
        if (cadastroPrograma && programa != null) {
            //TODO: mudei aqui para evitar  Failed to lazily initialize a collection, no session or session was closed
            ProgramaTreinoFicha programaFicha = obterProgramasFicha(ctx, programa.getCodigo(), ficha.getCodigo());
            if (programaFicha != null) {
                getProgramaTreinoFichaDao().refresh(ctx, programaFicha);
            }
            if (programaFicha == null || programa.getCodigo() == null || programa.getCodigo() == 0) {
                getProgramaTreinoFichaDao().atualizarProgramaTreinoFicha(ctx, new ProgramaTreinoFicha(programa, ficha, tipoExecucaoEnum, diasSemana));
            } else {
                programaFicha.setFicha(ficha);
                programaFicha.setDiaSemana(diasSemana);
                programaFicha.setTipoExecucao(tipoExecucaoEnum);
                programaFicha.setFichaAntesAlteracao(fichaAntesAlteracao);
                alterarProgramaFicha(ctx, programaFicha);
                programa = obterPorId(ctx, programa.getCodigo());
            }
            gravarProgramaSemTratarExcecao(ctx, programa, professor);
        }
        return ficha;
    }

    @Override
    public ProgramaTreino gravarProgramaSemTratarExcecao(String ctx,
                                                         ProgramaTreino programa, final ProfessorSintetico professor)
            throws ServiceException, ValidacaoException {
        if (programa.getDataInicio() == null) {
            throw new ServiceException("O campo Data Inicio está vazio.");
        }
        if (programa.getDiasPorSemana() == null) {
            throw new ServiceException("O campo Dias por Semana está vazio.");
        }
        if (programa.getDataTerminoPrevisto() == null) {
            throw new ServiceException("O campo Término(Previsão) está vazio.");
        }

        obterObjetivosDoPrograma(ctx, programa);
        obterRestricoesPrograma(ctx, programa);

//        programa.setDataLancamento(Calendario.hoje());
        if (programa.getCodigo() == null || programa.getCodigo() <= 0) {
            programa = inserir(ctx, programa);
            programa.manterAntesAlteracao();
        } else {
            programa = alterar(ctx, programa, professor);
            programa.manterAntesAlteracao();
        }

        try {
            atualizarVersaoPrograma(ctx, programa);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
        return programa;
    }

    public String importarProgramasJson(String ctx, List<ProgramaFichaJSON> programasJSON) throws ServiceException {
        try {
            String retorno = "countSucesso Programas foram salvos com sucesso e countErro programas não foram salvos por algum problema";
            retorno += "\n ------------------------------------------";
            Integer sucesso = 0;
            Integer erro = 0;
            for (ProgramaFichaJSON programa : programasJSON) {
                try {
                    Integer codigoPrograma = 0;
                    try (ResultSet st = this.programatreinoDao.createStatement(ctx, "select a.codigo from programatreino a \n" +
                            "where upper(a.nome) = '" + programa.getNomePrograma().toUpperCase() + "'")) {
                        if (st.next()) {
                            codigoPrograma = st.getInt("codigo");
                        }
                    }

                    if (UteisValidacao.emptyNumber(codigoPrograma)) {
                        String sql = String.format("insert into programatreino (nome, situacao, predefinido, totalaulasprevistas, dataLancamento, diasporsemana) values ('%s', %d, true, %d, '%s', %d) returning codigo;",
                                programa.getNomePrograma(), 0, programa.getTotalAulasPrevistas(), Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd HH:mm:ss"),
                                programa.getDiasPorSemana());
                        try (ResultSet st = this.programatreinoDao.createStatement(ctx, sql)) {
                            if (st.next()) {
                                codigoPrograma = st.getInt("codigo");
                            }
                        }
                        System.out.println("Programa salvo com sucesso: " + programa.getNomePrograma());
                    }

                    if (!UteisValidacao.emptyNumber(codigoPrograma)) {
                        String sql2 = String.format("insert into ficha (codigo, nome, ativo, usarcomopredefinida) values (%d, '%s', true, false)",
                                programa.getCodigoFicha(), programa.getNomeFicha());
                        this.fichaDao.executeNativeSQL(ctx, sql2);
                        System.out.println("Ficha salva com sucesso: " + programa.getNomeFicha());

                        String sql3 = String.format("insert into programatreinoficha (ficha_codigo, programa_codigo) values (%d, %d)", programa.getCodigoFicha(), codigoPrograma);
                        this.programaTreinoFichaDao.executeNativeSQL(ctx, sql3);
                        System.out.println("ProgramaTreinoFicha salvo com sucesso.");
                    }
                    sucesso++;
                } catch (Exception ex) {
                    retorno += "\n Erro ao salvar programa/ficha: " + programa.getNomePrograma() + " - " + programa.getNomeFicha()+ "\n" + ex.getMessage();
                    retorno += "\n ------------------------------------------";
                    erro++;
                    System.out.println("Erro ao programa/ficha: " + programa.getNomePrograma() + " - " + programa.getNomeFicha());
                    Uteis.logar(ex, PovoadorAtividadeServiceImpl.class);
                }

            }
            retorno = retorno.replace("countSucesso", sucesso.toString());
            retorno = retorno.replace("countErro", erro.toString());
            return retorno;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void calcularAulasPrevistas(final ProgramaTreino programa) throws ServiceException {
        if (programa != null && programa.getDataInicio() != null
                && programa.getDataTerminoPrevisto() != null
                && (programa.getDiasPorSemana() != null && programa.getDiasPorSemana() > 0)) {
            Long dias = Uteis.nrDiasEntreDatas(programa.getDataInicio(), programa.getDataTerminoPrevisto());
            Double valor = (dias.doubleValue() / 7.0) * programa.getDiasPorSemana();
            int totalAulasPrevistas = (int) Math.round(valor);
            programa.setTotalAulasPrevistas(totalAulasPrevistas);
        } else {
            if (programa != null) {
                programa.setTotalAulasPrevistas(null);
            }
        }
    }

    public void calcularTerminoPrevisto(final String ctx, final ProgramaTreino programa) throws ServiceException {
        if (programa.getDataInicio() != null
                && (programa.getTotalAulasPrevistas() != null && programa.getTotalAulasPrevistas() > 0)
                && (programa.getDiasPorSemana() != null && programa.getDiasPorSemana() > 0)) {
            Integer dias = programa.getTotalAulasPrevistas() * 7 / programa.getDiasPorSemana();
            try {
                Empresa empresa = empresaService.obterPorIdZW(ctx, programa.getCliente().getEmpresa());
                programa.setDataTerminoPrevisto(Uteis.somarDias(programa.getDataInicio(), dias, empresa.getTimeZoneDefault()));
            } catch (Exception e) {
                programa.setDataTerminoPrevisto(Uteis.somarDias(programa.getDataInicio(), dias));
            }
        } else {
            programa.setDataTerminoPrevisto(null);
        }
    }

    public Long obterTempoUtil(final String ctx, final Integer treinoRealizado) throws ServiceException {
        try {
            return (Long) getSerieRealizadaDao().sum(ctx, "duracao", new String[]{"treinoRealizado.codigo"}, new Object[]{treinoRealizado});
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

    }

    public void atualizarTempoUtilTreinoRealizado(final String ctx, final Integer treinoRealizado) throws ServiceException {
        try {
            Integer tempo = obterTempoUtil(ctx, treinoRealizado).intValue();
            getTreinoRealizadoDao().updateAlgunsCampos(ctx, new String[]{"tempoUtil"}, new Object[]{tempo},
                    new String[]{"codigo"}, new Object[]{treinoRealizado});
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }

    }

    private void desabilitarNotificacoesTreino(final String ctx, final Integer codigoCliente) throws ServiceException {
        try {
            Integer nrDiasNotificacao = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.NUMERO_DIAS_NOTIFICAR_TREINO_VENCIDO).getValorAsInteger();
            if (UteisValidacao.emptyNumber(nrDiasNotificacao)) {
                return;
            }
            Date dia = Uteis.somarDias(Calendario.hoje(), -(nrDiasNotificacao));
            StringBuilder sql = new StringBuilder();
            sql.append("Select obj from  Notificacao obj  WHERE").append("\n");
            if (!UteisValidacao.emptyNumber(codigoCliente)) {
                sql.append(" obj.ativa = true AND obj.tipo  = :tipo\n");
            } else {
                sql.append(" obj.ativa = true and obj.programa.dataTerminoPrevisto < :dataFim AND obj.tipo  = :tipo\n");
            }

            if (!UteisValidacao.emptyNumber(codigoCliente)) {
                sql.append("AND obj.cliente.codigo = :cliente");
            }
            HashMap<String, Object> params = new HashMap<String, Object>();
            params.put("tipo", TipoNotificacaoEnum.SOLICITAR_RENOVACAO);
            if (!UteisValidacao.emptyNumber(codigoCliente)) {
                params.put("cliente", codigoCliente);
            } else {
                params.put("dataFim", dia);
            }

            List<Notificacao> notificacaos = getNotfService().obterPorParam(ctx, sql.toString(), params);
            for (Notificacao obj : notificacaos) {
                obj.setAtiva(false);
                getNotfService().alterar(ctx, obj);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public void desabilitarNotificacoesTreinoVencido(final String ctx) throws ServiceException {
        desabilitarNotificacoesTreino(ctx, null);

    }

    public void desabilitarNotificacoesTreinoVencidoCliente(final String ctx, final Integer codigoCliente) throws ServiceException {
        desabilitarNotificacoesTreino(ctx, codigoCliente);

    }

    public List<ProgramaTreino> consultarPrevistosRenovarNosProximosDias(final String ctx, final Date dataBase,
                                                                         Integer professor, Integer aluno,
                                                                         TipoLembreteEnum tipoLembrete) throws ServiceException {
        Map<String, Object> p = new HashMap<String, Object>();
        p.put("dataInicio", dataBase);
        p.put("tipoLembrete", tipoLembrete);
        StringBuilder hql = new StringBuilder("select o from " + ProgramaTreino.class.getSimpleName() + " o ");
        hql.append("where 1 = 1 ");
        hql.append("and (EXTRACT(EPOCH FROM dataTerminoPrevisto - :dataInicio").append(")/60) between 0 and ").append(tipoLembrete.getnMinutos()).append(" ");
        hql.append("and codigo not in (select programa.codigo from LembreteAgendamento where tipo = :tipoLembrete)");
        if (professor != null) {
            hql.append(" and programa.professorCarteira.codigo = :professor");
            p.put("professor", professor);
        }
        if (aluno != null) {
            hql.append("and programa.cliente.codigo = :aluno");
            p.put("aluno", aluno);
        }
        try {
            return obterPorParam(ctx, hql.toString(), p);
        } catch (ServiceException ex) {
            Logger.getLogger(ProgramaTreinoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException(ex.getMessage());
        }
    }

    /**
     * Notificar alunos que possuem Programas Vigentes, que nos últimos 7 dias
     * (TipoLembreteEnum) não tiveram Treinos Realizados O lembrete deve ser
     * enviado a cada 7 dias
     *
     * @param ctx
     * @param dataBase
     * @param professor
     * @param aluno
     * @param tipoNotificacao
     * @param tipoLembrete
     * @return
     * @throws ServiceException
     */
    public List<ProgramaTreino> consultarAtrasados(final String ctx, final Date dataBase,
                                                   Integer professor, Integer aluno,
                                                   TipoNotificacaoEnum tipoNotificacao,
                                                   TipoLembreteEnum tipoLembrete) throws ServiceException {
        Map<String, Object> p = new HashMap<String, Object>();
        p.put("dataInicio", Calendario.getDataComHoraZerada(dataBase));
        p.put("tipoNotificacao", tipoNotificacao);
        StringBuilder hql = new StringBuilder("select o from " + ProgramaTreino.class.getSimpleName() + " o ");
        hql.append("where 1 = 1 ");
        hql.append("and :dataInicio between dataInicio and dataTerminoPrevisto ");
        hql.append("and :dataInicio - (select max(dataCriacao) \n");
        hql.append("                                    from LembreteAgendamento l"
                + "                     where tipoNotificacao = :tipoNotificacao "
                + "                     and l.programa.codigo=o.codigo) > '").append(tipoLembrete.getnMinutos()).append(" minutes'");

        if (professor != null) {
            hql.append(" and programa.professorCarteira.codigo = :professor");
            p.put("professor", professor);
        }
        if (aluno != null) {
            hql.append("and programa.cliente.codigo = :aluno");
            p.put("aluno", aluno);
        }
        try {
            return obterPorParam(ctx, hql.toString(), p);
        } catch (ServiceException ex) {
            Logger.getLogger(ProgramaTreinoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public boolean clienteTemProgramaVigente(final String ctx, final ClienteSintetico cliente) throws ServiceException {
        try {
            return ((Long) getProgramaTreinoDao().count(ctx, "codigo",
                    new String[]{"cliente.codigo", "datainicio <", "dataterminoprevisto >"},
                    new Object[]{cliente.getCodigo(), Calendario.getDataComHoraZerada(Uteis.somarDias(Calendario.hoje(), 1)),
                            Calendario.getDataComHora(Uteis.somarDias(Calendario.hoje(), -1), "23:59:59")})) > 0;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public ProgramaTreino renovarProgramaTreino(final String ctx, final ProgramaTreino programa, final ProfessorSintetico progessorMontou) throws ServiceException {
        try {
            getValidacao().validarInsercao(programa, ctx, true);
            ProgramaTreino programaRenovar = new ProgramaTreino(programa, progessorMontou);
            if (programaRenovar.getDataTerminoPrevisto() == null) {
                calcularTerminoPrevisto(ctx, programaRenovar);
                if (programaRenovar.getDataTerminoPrevisto() == null) {
                    throw new ServiceException("Data término prevista não pode ser calculada. Verifique se a quantidade de aulas previstas e de dias por semana foram informados no programa que se deseja renovar.");
                }
            }
            //TODO WM 17/05/15: temporário para contornar problema de versionamento do aplicativo PactoTreino que não atualiza o treino que foi renovado
            programaRenovar.setVersao(programa.getVersao() + 1);
            //
            inserir(ctx, programaRenovar);

            List<ProgramaTreinoFicha> programaFichas = obterFichaPorPrograma(ctx, programa.getCodigo(), null, true);
            for (ProgramaTreinoFicha programaTreinoFicha : programaFichas) {

                programaTreinoFicha = getProgramatreinofichaDao().findById(ctx, programaTreinoFicha.getCodigo());
                Ficha ficha = new Ficha(programaTreinoFicha.getFicha(), programaTreinoFicha.getFicha().getAtividades(), false);
                ficha = fichaService.inserir(ctx, ficha);
                for (AtividadeFicha atvFich : ficha.getAtividades()) {
                    fichaService.inserirAtividadeFicha(ctx, atvFich);
                    for (Serie serie : atvFich.getSeries()) {
                        serieService.inserir(ctx, serie);
                    }
                }
                ProgramaTreinoFicha programaFichaNovo = new ProgramaTreinoFicha(programaRenovar,
                        ficha, programaTreinoFicha.getTipoExecucao(), new ArrayList(programaTreinoFicha.getDiaSemana()));
                getProgramatreinofichaDao().insert(ctx, programaFichaNovo);
            }
            List<ObjetivoPrograma> objetivosPrograma = obterObjetivosPrograma(ctx, programa);
            for (ObjetivoPrograma objProg : objetivosPrograma) {
                ObjetivoPrograma objProgNovo = new ObjetivoPrograma(objProg.getObjetivo(), programaRenovar);
                objetivoProgramaDao.insert(ctx, objProgNovo);
            }

            programa.setProgramaTreinoRenovado(programaRenovar.getCodigo());
            programa.setDataRenovacao(Calendario.hoje());
            alterar(ctx, programa, progessorMontou);
            return programaRenovar;
        } catch (ValidacaoException ve) {
            throw ve;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public ProgramaTreino consultarUltimoTreinoDataBaseAluno(final String ctx, final ClienteSintetico cliente, final Date data) throws ServiceException {
        try {
            if (data == null) {
                List<ProgramaTreino> list = getProgramaTreinoDao().findListByAttributesProgramTreino(ctx, new String[]{"cliente.codigo"},
                        new Object[]{cliente.getCodigo()}, "dataTerminoPrevisto desc", 1);
                return list.isEmpty() ? null : list.get(0);
            } else {
                List<ProgramaTreino> list = getProgramaTreinoDao().findListByAttributesProgramTreino(ctx, new String[]{"cliente.codigo", " dataTerminoPrevisto < "},
                        new Object[]{cliente.getCodigo(), data}, "dataTerminoPrevisto desc", 1);
                return list.isEmpty() ? null : list.get(0);
            }

        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public ProgramaTreino consultarSomenteDataTreinoAtual(final String ctx, final ClienteSintetico cliente) throws ServiceException {
        try {
            List<ProgramaTreino> list = getProgramaTreinoDao().findObjectsByAttributesSimple(ctx, new String[]{"dataTerminoPrevisto"},
                    new String[]{"cliente.codigo"}, new Object[]{cliente.getCodigo()},
                    "dataTerminoPrevisto desc", 1);
            return list.isEmpty() ? null : list.get(0);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public boolean clienteTemProgramaVencido(final String ctx, final ClienteSintetico cliente) throws ServiceException {
        try {
            return ((Long) getProgramaTreinoDao().count(ctx, "codigo",
                    new String[]{"cliente.codigo", "dataterminoprevisto <"},
                    new Object[]{cliente.getCodigo(), Calendario.getDataComHoraZerada(Calendario.hoje())})) > 0;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public String atualizarAndamentoAluno(final String ctx, final String matricula) throws ServiceException {
        List<ClienteSintetico> consultarPorMatriculaOuNome = cs.consultarPorMatriculaOuNome(ctx, null, null, matricula, 0);
        if (consultarPorMatriculaOuNome == null || consultarPorMatriculaOuNome.isEmpty()) {
            return "Aluno não encontrado";
        } else {
            ClienteSintetico cliente = consultarPorMatriculaOuNome.get(0);
            List<ProgramaTreino> programas = obterProgramasPorCliente(ctx, cliente.getCodigo(), null, null, null, null);
            if (programas == null || programas.isEmpty()) {
                return "Nenhum treino.";
            } else {
                programas = Ordenacao.ordenarLista(programas, "dataTerminoPrevisto");
                Collections.reverse(programas);
                ProgramaTreino programa = programas.get(0);
                cliente.setNrTreinosPrevistos(programa.getTotalAulasPrevistas());
                cliente.setNrTreinosRealizados(programa.getNrTreinosRealizados());
                cs.alterar(ctx, cliente);
                return "Aulas previstas = " + programa.getTotalAulasPrevistas() + " - Aulas realizadas = " + programa.getNrTreinosRealizados();
            }
        }

    }

    @Override
    public void atualizarNrTreinosRealizados(final String ctx, final Integer codPrograma,
                                             final Integer nrTreinos) throws ServiceException {
        try {
            programatreinoDao.updateAlgunsCampos(ctx, new String[]{"nrtreinosrealizados"},
                    new Object[]{nrTreinos}, new String[]{"codigo"}, new Object[]{codPrograma});
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProgramaTreino prepararPersistenciaJSON(final String ctx, final ProgramaWriteJSON pw)
            throws ServiceException {
        ProgramaTreino p = new ProgramaTreino();
        if (pw.getCodigo() != null && !pw.getCodigo().equals(0)) {
            p = obterPorId(ctx, pw.getCodigo());
        }
        p.setCliente(cs.obterPorId(ctx, pw.getCliente()));
        p.setDataInicio(pw.getDataInicio());
        p.setDiasPorSemana(pw.getDiasPorSemana());
        p.setTotalAulasPrevistas(pw.getTotalAulasPrevistas());
        p.setDataLancamento(pw.getDataLancamento());
        p.setDataProximaRevisao(pw.getDataProximaRevisao());
        p.setDataRenovacao(pw.getDataRenovacao());
        p.setDataTerminoPrevisto(pw.getDataTerminoPrevisto());
        p.setNome(pw.getNome());
        //professores
        p.setProfessorCarteira(professorService.obterPorId(ctx, pw.getProfessorCargeira()));
        p.setProfessorMontou(professorService.obterPorId(ctx, pw.getProfessorMontou()));
        //objetivos programa
        List<ObjetivoProgramaJSON> objsJSON = pw.getObjetivosPrograma();
        if (objsJSON != null) {
            obterRestricoesPrograma(ctx, p);
            obterObjetivosDoPrograma(ctx, p);
            for (ObjetivoProgramaJSON ob : objsJSON) {
                try {
                    final Integer codObjetivo = ob.getObjetivo();
                    GenericoTO objPredef = null;
                    if (codObjetivo != null && codObjetivo > 0) {
                        objPredef = (GenericoTO) ColecaoUtils.find(p.getObjetivosPredef(), new Predicate() {
                            @Override
                            public boolean evaluate(Object o) {
                                GenericoTO gen = (GenericoTO) o;
                                return gen.getCodigo().equals(codObjetivo);
                            }
                        });
                    }
                    if (objPredef != null) {
                        objPredef.setEscolhido(true);
                    }
                } catch (Exception ex) {
                    Logger.getLogger(ProgramaTreinoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
                    throw new ServiceException(ex);
                }
            }
        }
        p.setProgramaTreinoRenovacao(pw.getProgramaTreinoRenovacao());
        p.setProgramaTreinoRenovado(pw.getProgramaTreinoRenovado());
        p.setSituacao(ProgramaSituacaoEnum.valueOf(pw.getSituacao()));
        validacao.validarInsercao(p, ctx, true);
        return p;
    }

    public ProgramaTreino gerarProgramaDefault(String ctx, ClienteSintetico cliente, final Usuario usuario, Integer codigoColaborador, ProgramaTreinoTO programaTreinoTO) throws ServiceException {
        ProgramaTreino programa = new ProgramaTreino();
        programa.setCliente(cliente);
        programa.setCodigoColaborador(codigoColaborador);
        programa.setDataLancamento(Calendario.hoje());
        programa.setNome("PROG " + Calendario.getData(Calendario.hoje(), "ddMMyyHHmm"));
        programa.setDataInicio(Calendario.getDataComHoraZerada(Calendario.hoje()));
        programa.setDataProximaRevisao(Calendario.proximoDeveSerUtil(programa.getDataInicio(), 7));
        programa.setTotalAulasPrevistas(12);
        programa.setDiasPorSemana(2);
        programa.setProfessorCarteira(cliente == null ? null : cliente.getProfessorSintetico());
        programa.setProfessorMontou(obterProfessorUsuarioLogado(ctx, usuario, cliente));
        if (programaTreinoTO != null) {
            programa.setEmRevisaoProfessor(programaTreinoTO.getEmRevisaoProfessor());
            programa.setGeradoPorIA(programaTreinoTO.getGeradoPorIA());
        }
        //
        iniciarAulasPrevistas(ctx, programa);
        return programa;
    }

    public ProfessorSintetico obterProfessorUsuarioLogado(String ctx, Usuario usuario, ClienteSintetico cliente) throws ServiceException {
        ProfessorSintetico ps = new ProfessorSintetico();
        if (cliente != null && !usuario.getProfessor().getEmpresa().getCodZW().equals(cliente.getEmpresa())) {
            try {
                ps = professorSinteticoDao.findObjectByAttributes(
                        ctx,
                        new String[]{"codigoPessoa", "empresa.codZW"},
                        new Object[]{usuario.getProfessor().getCodigoPessoa(), cliente.getEmpresa()},
                        "nome");
                ps = ps != null ? ps : usuario.getProfessor();
            } catch (Exception ex) {
                throw new ServiceException(ex);
            }
        } else {
            ps = usuario.getProfessor();
        }
        return ps;
    }

    public ProgramaTreino gerarProgramaPredefinidoDefault(String ctx, ProgramaTreinoTO programaTreinoTO) throws ServiceException {
        ProfessorSintetico professor = professorService.consultarPorCodigoColaborador(ctx, programaTreinoTO.getProfessorId());
        ProgramaTreino programa = new ProgramaTreino();
        programa.setDataLancamento(Calendario.hoje());
        programa.setNome(programaTreinoTO.getNome());
        programa.setTotalAulasPrevistas(programaTreinoTO.getTotalTreinos());
        programa.setDiasPorSemana(programaTreinoTO.getQtdDiasSemana());
        programa.setProfessorMontou(professor);
        programa.setPreDefinido(true);
        programa.setGenero(programaTreinoTO.getGenero());
        iniciarAulasPrevistas(ctx, programa);
        return programa;
    }

    private void iniciarAulasPrevistas(String ctx, ProgramaTreino programa) {
        try {
            if (programa.getDataTerminoPrevisto() != null) {
                // Verificar se a data término foi definida manualmente (renovação com duração fixa)
                // Se sim, não recalcular para manter a duração exata
                if (programa.getDataInicio() != null) {
                    long duracaoAtual = (programa.getDataTerminoPrevisto().getTime() - programa.getDataInicio().getTime()) / (1000 * 60 * 60 * 24);
                } else {
                    calcularAulasPrevistas(programa);
                }
            } else if (programa.getTotalAulasPrevistas() != null) {
                calcularTerminoPrevisto(ctx, programa);
            }
        } catch (Exception e) {
            getViewUtils().mensErro(e.getMessage());
        }
    }

    public AtividadeFicha salvarAtividadeFicha(String ctx, Atividade atividadeSelecionada,
                                               Serie serie, Integer nrSeries, String fichaSelecionada,
                                               Ficha ficha, Integer ordem, boolean alternar) throws ServiceException {
        try {
            AtividadeFicha atividadeFicha = new AtividadeFicha();
            atividadeFicha.setFicha(ficha);
            atividadeFicha.setOrdem(ordem);
            atividadeFicha.setAtividade(atividadeSelecionada);
            atividadeFicha.setSeries(new ArrayList<Serie>());

            atividadeFicha = atividadeFichaDao.insert(ctx, atividadeFicha);

            if (atividadeSelecionada.getAerobica()) {
                atividadeFicha.addSeries(atividadeFicha, nrSeries,
                        Uteis.converterMinutosEmSegundos(serie.getDuracaoStr()),
                        serie.getDistancia(), serie.getVelocidade(),
                        Uteis.converterMinutosEmSegundos(serie.getDescansoStr()));
            } else {
                atividadeFicha.addSeries(atividadeFicha, nrSeries,
                        UteisValidacao.emptyString(serie.getRepeticoesVetor()) ? "1" : serie.getRepeticoesVetor(),
                        UteisValidacao.emptyString(serie.getCargaVetor()) ? "0" : serie.getCargaVetor(),
                        Uteis.converterMinutosEmSegundos(serie.getDescansoStr()), false, alternar, serie.getComplemento());
            }
            int ordemS = 1;
            for (Serie s : atividadeFicha.getSeries()) {
                s.setOrdem(ordemS++);
                s.setAtividadeFicha(atividadeFicha);
                serieService.inserir(ctx, s);
            }

//            atividadeFichaDao.refresh(ctx, atividadeFicha);
            return atividadeFicha;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }

    }

    @Override
    public AtividadeFichaTO salvarAtividadeFichaRapida(String ctx, Atividade atividadeSelecionada,
                                                       Serie serie, Integer nrSeries, String fichaSelecionada,
                                                       Ficha ficha, Integer ordem, boolean alternar) throws ServiceException {
        try {
            AtividadeFicha atividadeFicha = salvarAtividadeFicha(ctx, atividadeSelecionada,
                    serie, nrSeries, fichaSelecionada, ficha, ordem, alternar);

            AtividadeFichaTO atvFic = new AtividadeFichaTO();
            atvFic.setNomeAtividade(atividadeSelecionada.getNome());
            atvFic.setCodigoAtividade(atividadeSelecionada.getCodigo());
            atvFic.setAerobica(atividadeSelecionada.getAerobica());
            atvFic.setFicha(fichaSelecionada);
            atvFic.setFichaOriginal(fichaSelecionada);
            atvFic.setSeries(atividadeSelecionada.getAerobica() ? 1 : nrSeries);
            atvFic.setDescanso(Uteis.converterMinutosEmSegundos(serie.getDescansoStr()));
            atvFic.setDuracao(Uteis.converterMinutosEmSegundos(serie.getDuracaoStr()));
            atvFic.setVelocidade(serie.getVelocidade());
            atvFic.setCarga(serie.getCarga());
            atvFic.setRepeticoes(serie.getRepeticao());
            atvFic.setCodigoAtividadeFicha(atividadeFicha.getCodigo());
            atvFic.setOrdem(fichaSelecionada + (ordem < 10 ? "0" : "") + ordem);
            atvFic.setCargaVetor(serie.getCargaVetor());
            atvFic.setRepeticoesVetor(serie.getRepeticoesVetor());
            return atvFic;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Integer atualizarNrTreinosRealizados(String ctx, Integer idPrograma) throws ServiceException {
        try {
            Number count = getTreinoRealizadoDao().count(ctx, "codigo", new String[]{"programaTreinoFicha.programa.codigo"},
                    new Object[]{idPrograma});
            if (count != null) {
                getProgramaTreinoDao().updateAlgunsCampos(ctx, new String[]{"nrTreinosRealizados"},
                        new Object[]{count.intValue()},
                        new String[]{"codigo"},
                        new Object[]{idPrograma});
                getProgramaTreinoAndamentoDao().updateAlgunsCampos(ctx, new String[]{"nrTreinos"},
                        new Object[]{count.intValue()},
                        new String[]{"programa.codigo"},
                        new Object[]{idPrograma});
                return count.intValue();
            }
            return 0;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluirHistoricoRevisaoSincronizacao(String ctx, ProgramaTreino object) throws Exception {
        carregarHistoricoRevisoesSincronizacao(ctx, object);
        if (object.getHistoricoRevicoesSincronizacao() != null) {
            for (HistoricoRevisao hr : object.getHistoricoRevicoesSincronizacao()) {
                historicoRevisaoSincronizacaoDao.delete(ctx, hr);
            }
        }
    }

    public void notificarOuvintes(String ctx, final ProgramaTreino programa) {
        notificarOuvintes(ctx, programa.getCliente() == null ? null : programa.getCliente().getCodigo(),
                programa.getCodigoColaborador()
        );
    }

    public void notificarOuvintes(String ctx, final Integer codigoCliente, final Integer codigoColaborador) {
        try {
            notificarOuvintes(ctx, codigoCliente, codigoColaborador, JSFUtilities.getRequest());
        } catch (Exception e) {
            Uteis.logar(e, ProgramaTreinoServiceImpl.class);
        }
    }

    @Override
    public void notificarOuvintes(String ctx, final Integer codigoCliente, final Integer codigoColaborador, HttpServletRequest request) {
        try {
            Usuario usuario = codigoCliente == null ?
                    usuarioService.obterPorAtributo(ctx, "professor.codigoColaborador", codigoColaborador) :
                    usuarioService.obterPorAtributo(ctx, "cliente.codigo", codigoCliente);
            Map<String, Object> p = new HashMap<String, Object>();
            p.put("tipo", TipoObjetoSincronizarEnum.PROGRAMA.name());
            p.put("chaveBusca", usuario.getUserName());
            if (request != null) {
                URL u = new URL(request.getRequestURL().toString());
                String url = String.format("%s://%s:%s%s/prest/filaImpressao/%s/enfileirarSync",
                        u.getProtocol(),
                        u.getHost(),
                        u.getPort() == -1 ? 80 : u.getPort(),
                        request.getContextPath(),
                        ctx);
                Propagador.doExecuteRequestInstances(url, p, 500);
            }
            filaImpressaoService.enfileirar(ctx, TipoObjetoSincronizarEnum.PROGRAMA, usuario.getUserName());
        } catch (Exception e) {
            Uteis.logar(e, ProgramaTreinoServiceImpl.class);
        }
    }

    @Override
    public ProgramaTreinoAndamento consultarAndamentoSimples(String ctx, Integer programa) throws ServiceException {
        try {
            List<ProgramaTreinoAndamento> lista = getProgramaTreinoAndamentoDao().findObjectsByAttributesSimple(ctx,
                    new String[]{"nrTreinos", "programa.totalAulasPrevistas",},
                    new String[]{"programa.codigo"}, new Object[]{programa},
                    null, 0);
            return lista == null || lista.isEmpty() ? null : lista.get(0);
        } catch (Exception ex) {
            Uteis.logar(ex, ProgramaTreinoServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public List<TreinoRealizado> obterTreinosRealizados(final String ctx,
                                                        final Integer codigoPrograma, Date inicio, Date fim,
                                                        final Integer codigoCliente) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT pta.ficha_codigo as ficha, ta.codigo as codta, datainicio, f.nome as nomeficha,  \n");
        sql.append(" prof.nome as professor, executadofichadia, origem, unidadeexecucao FROM treinorealizado ta \n");
        sql.append(" INNER JOIN programatreinoficha pta ON pta.codigo = ta.programatreinoficha_codigo \n");
        sql.append(" INNER JOIN ficha f ON f.codigo = pta.ficha_codigo \n");
        sql.append(" LEFT JOIN professorsintetico prof on prof.codigo = ta.professoracompanhamento_codigo");
        if (codigoPrograma == null) {
            sql.append(" WHERE ta.cliente_codigo = ").append(codigoCliente);
        } else {
            sql.append(" WHERE pta.programa_codigo = ").append(codigoPrograma);
        }

        if (inicio != null && fim != null) {
            sql.append(" AND datainicio BETWEEN '").append(Uteis.getDataAplicandoFormatacao(Calendario.getDataComHora(inicio, "00:00"), "yyyy-MM-dd HH:mm:ss"));
            sql.append("' and '");
            sql.append(Uteis.getDataAplicandoFormatacao(Calendario.getDataComHora(fim, "23:59"), "yyyy-MM-dd HH:mm:ss")).append("' ");
        }
        sql.append(" ORDER BY datainicio");
        try (ResultSet rs = treinoRealizadoDao.createStatement(ctx, sql.toString())) {
            return montarDadosTreinosRealizados(ctx, rs);
        }
    }

    private List<TreinoRealizado> montarDadosTreinosRealizados(final String ctx, ResultSet rs) throws Exception {
        List<TreinoRealizado> treinos = new ArrayList<TreinoRealizado>();
        while (rs.next()) {
            TreinoRealizado treino = new TreinoRealizado();
            treino.setCodigo(rs.getInt("codta"));
            treino.setDataInicio(rs.getTimestamp("datainicio"));
            treino.setProgramaTreinoFicha(new ProgramaTreinoFicha());
            treino.getProgramaTreinoFicha().setFicha(new Ficha());

            try (ResultSet rsfd = treinoRealizadoDao.createStatement(ctx, "select executadofichadia from treinorealizado  where codigo = "
                    + rs.getInt("codta"))) {
                if (rsfd.next()) {
                    treino.setExecutadoFichaDia(rsfd.getBoolean("executadofichadia"));
                } else {
                    treino.setExecutadoFichaDia(rs.getBoolean("executadofichadia"));
                }
            }

            try (ResultSet rsfc = treinoRealizadoDao.createStatement(ctx, "select codigo from ficha where versao = 2 and codigo = "
                    + rs.getInt("ficha"))) {
                if (rsfc.next()) {
                    treino.getProgramaTreinoFicha().getFicha().setNome(rs.getString("nomeficha") + " (REMOVIDA) ");
                } else {
                    treino.getProgramaTreinoFicha().getFicha().setNome(rs.getString("nomeficha"));
                }
            }


            if (!UteisValidacao.emptyString(rs.getString("professor"))) {
                treino.setProfessorAcompanhamento(new ProfessorSintetico());
                treino.getProfessorAcompanhamento().setNome(rs.getString("professor"));
            }
            treino.setUnidadeExecucao(rs.getString("unidadeexecucao"));
            treino.setOrigem(OrigemExecucaoEnum.getFromId(rs.getInt("origem")));
            treinos.add(treino);
        }

        return treinos;
    }

    @Override
    public String pesquisarTreinoRelaizadoPorClientes(String ctx, String codigoClientes, Date data) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT clie.codigocliente from TreinoRealizado tr").append("\n");
            sql.append(" inner join clientesintetico clie on clie.codigo = tr.cliente_codigo ").append("\n");
            sql.append(" WHERE clie.codigocliente IN (").append(codigoClientes).append(") \n");
            sql.append(" AND cast(tr.datainicio as date) = '").append(Uteis.getDataAplicandoFormatacao(data, "yyyy-MM-dd")).append("' \n");

            StringBuilder codigos;
            try (ResultSet rs = treinoRealizadoDao.createStatement(ctx, sql.toString())) {
                codigos = new StringBuilder("");

                while (rs.next()) {
                    if (codigos.toString().isEmpty()) {
                        codigos.append(rs.getInt("codigocliente"));
                    } else {
                        codigos.append(",").append(rs.getInt("codigocliente"));
                    }
                }
            }

            return codigos.toString();
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public void refresh(String ctx, ProgramaTreino object) throws ServiceException {
        try {
            getProgramaTreinoDao().refresh(ctx, object);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public void refresh(String ctx, List<ProgramaTreino> object) throws ServiceException {
        try {
            getProgramaTreinoDao().refresh(ctx, object);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public void refresh(String ctx, ProgramaTreinoFicha object) throws ServiceException {
        try {
            getProgramaTreinoFichaDao().refresh(ctx, object);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public ProgramaTreinoFicha obterProgramaTreinoFicha(final String ctx, final Integer ficha) throws ServiceException {
        try {
            return getProgramaTreinoFichaDao().findObjectByAttributes(ctx,
                    new String[]{"ficha.codigo"},
                    new Object[]{ficha}, "ficha.nome");
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public ProgramaTreinoFicha novaFicha(final String ctx, final List<ProgramaTreinoFicha> fichas,
                                         ProgramaTreino programa, Ficha predefinida) throws Exception {
        Ficha ficha = new Ficha();
        Set<String> vincBiTriSet = new HashSet<>();
        if (predefinida == null) {
            ficha.setNome(fichaService.sugerirNomeFicha(fichas));
        } else if (UteisValidacao.emptyNumber(predefinida.getCodigo())) {
            ficha = new Ficha(predefinida, predefinida.getAtividades(), false);
            vincBiTriSet = getVinculos(predefinida.getAtividades());
        } else {
            List<AtividadeFicha> atividadesFicha = fichaService.obterAtividadesFicha(ctx, predefinida.getCodigo());
            ficha = new Ficha(predefinida, atividadesFicha, false);

            vincBiTriSet = getVinculos(predefinida.getAtividades());
        }
        ConfiguracaoSistema config = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.SUGERIR_DESCANSO);
        ficha.setDescansoRepetir(config.getValor());
        ProgramaTreinoFicha ptf = new ProgramaTreinoFicha();
        ptf.setFicha(ficha);
        ptf.setPrograma(programa);
        ficha.getProgramas().add(ptf);
        ficha = fichaService.cadastrar(ctx, ficha);

        if (!vincBiTriSet.isEmpty()) {
            doAjustarAtividades(ctx, vincBiTriSet, ficha.getAtividades());
        }

        return ptf;

    }

    private void doAjustarAtividades(final String ctx, Set<String> vincBiTriSet, List<AtividadeFicha> atividades) throws ServiceException {
        for (String b : vincBiTriSet) {
            if (b.split("\\|").length > 1) {
                ajustarOrdemSetId(atividades, b);
                Collection<AtividadeFicha> atividadeFichas = ColecaoUtils.select(atividades, new Filter(b));
                if (null != atividadeFichas && !atividadeFichas.isEmpty()) {
                    if (((AtividadeFicha) ((ArrayList) atividadeFichas).get(0)).getMetodoExecucao() == MetodoExecucaoEnum.BI_SET) {
                        atualizarSetIdBiSetPorOrdem(ctx, atividadeFichas);
                    } else if (((AtividadeFicha) ((ArrayList) atividadeFichas).get(0)).getMetodoExecucao() == MetodoExecucaoEnum.TRI_SET) {
                        atualizarSetIdTriSetPorOrdem(ctx, atividadeFichas);
                    }
                }
            }
        }
    }

    private void ajustarOrdemSetId(List<AtividadeFicha> atividades, String vincBiTriSet) {
        String novaOrdem = "";
        String[] id = vincBiTriSet.split("\\|");
        for (AtividadeFicha af : atividades) {
            if (af.getSetId() != null && af.getSetId().contains(id[0])) {
                if (novaOrdem.equals("")) {
                    novaOrdem = af.getSetId();
                } else {
                    af.setSetId(novaOrdem);
                }
            }
        }
    }

    private void atualizarSetIdBiSetPorOrdem(final String ctx, Collection<AtividadeFicha> atividadeFichas) throws ServiceException {
        String setId1 = ((AtividadeFicha) ((ArrayList) atividadeFichas).get(0)).getCodigo() + "|" + ((AtividadeFicha) ((ArrayList) atividadeFichas).get(1)).getCodigo() + "|";
        String setId2 = ((AtividadeFicha) ((ArrayList) atividadeFichas).get(1)).getCodigo() + "|" + ((AtividadeFicha) ((ArrayList) atividadeFichas).get(0)).getCodigo() + "|";
        ((AtividadeFicha) ((ArrayList) atividadeFichas).get(0)).setSetId(setId1);
        ((AtividadeFicha) ((ArrayList) atividadeFichas).get(1)).setSetId(setId2);
        for (AtividadeFicha af : atividadeFichas) {
            fichaService.alterarAtividadeFicha(ctx, af);
        }
    }

    private void atualizarSetIdTriSetPorOrdem(final String ctx, Collection<AtividadeFicha> atividadeFichas) throws ServiceException {
        String setId1 = ((AtividadeFicha) ((ArrayList) atividadeFichas).get(0)).getCodigo() + "|" +
                ((AtividadeFicha) ((ArrayList) atividadeFichas).get(1)).getCodigo() + "|" +
                ((AtividadeFicha) ((ArrayList) atividadeFichas).get(2)).getCodigo() + "|";

        String setId2 = ((AtividadeFicha) ((ArrayList) atividadeFichas).get(1)).getCodigo() + "|" +
                ((AtividadeFicha) ((ArrayList) atividadeFichas).get(0)).getCodigo() + "|" +
                ((AtividadeFicha) ((ArrayList) atividadeFichas).get(2)).getCodigo() + "|";

        String setId3 = ((AtividadeFicha) ((ArrayList) atividadeFichas).get(2)).getCodigo() + "|" +
                ((AtividadeFicha) ((ArrayList) atividadeFichas).get(0)).getCodigo() + "|" +
                ((AtividadeFicha) ((ArrayList) atividadeFichas).get(1)).getCodigo() + "|";

        ((AtividadeFicha) ((ArrayList) atividadeFichas).get(0)).setSetId(setId1);
        ((AtividadeFicha) ((ArrayList) atividadeFichas).get(1)).setSetId(setId2);
        ((AtividadeFicha) ((ArrayList) atividadeFichas).get(2)).setSetId(setId3);

        for (AtividadeFicha af : atividadeFichas) {
            fichaService.alterarAtividadeFicha(ctx, af);
        }
    }

    private Set<String> getVinculos(List<AtividadeFicha> atividades) {
        Set<String> strings = new HashSet<String>();
        for (AtividadeFicha af : atividades) {
            if (!UteisValidacao.emptyString(af.getSetId()) && !af.getSetId().equalsIgnoreCase("null")) {
                strings.add(af.getSetId());
            }
        }
        return strings;
    }

    public void removerFichaPredefinida(final String ctx, List<ProgramaTreinoFicha> fichas,
                                        Ficha predefinida) throws Exception {
        for (ProgramaTreinoFicha ptf : new ArrayList<ProgramaTreinoFicha>(fichas)) {
            if (ptf.getFicha().getNome().equals(predefinida.getNome())) {
                programaTreinoFichaDao.delete(ctx, ptf);
                fichas.remove(ptf);
            }
        }
    }

    public List<ProgramaTreino> programasPredefinidos(String ctx) throws Exception {
        return this.programatreinoDao.programasPredefinidos(ctx);
    }

    public List<ProgramaTreino> programasPredefinidosAtivos(String ctx) throws Exception {
        return this.programatreinoDao.programasPredefinidos(ctx, 0);
    }

    @Override
    public void tornarPreDefinido(String key, ProgramaTreino programa) throws ServiceException {
        try {
            ProgramaTreino preDefinido = new ProgramaTreino(programa, false);
            preDefinido.setPreDefinido(true);
            preDefinido.setProfessorMontou(programa.getProfessorMontou());
            preDefinido.setDataLancamento(new Date());
            preDefinido.setVersao(1);
            ProgramaTreino programaDuplicado = getProgramaTreinoDao().findObjectByAttributes(key, new String[]{"nome", "preDefinido"}, new Object[]{programa.getNome(), true}, "nome");
            if (programaDuplicado == null) {
                preDefinido = programatreinoDao.insert(key, preDefinido);

                List<Ficha> fichas = fichaService.obterPorPrograma(key, programa.getCodigo());

                for (Ficha f : fichas) {
                    /**
                     * criando listas para atualização dos bi-set ou tri-set
                     */
                    List<AtividadeFicha> novasAtividadeFicha = new ArrayList<>();
                    HashMap<String, Object> idsAtividadeFicha = new HashMap<>();

                    List<AtividadeFicha> atividadesFicha = fichaService.obterAtividadesFicha(key, f.getCodigo());
                    Ficha fichaPredefinida = new Ficha(f, atividadesFicha, false);
                    fichaPredefinida = fichaService.inserir(key, fichaPredefinida);

                    //gravar atividades
                    for (AtividadeFicha atvFic : fichaPredefinida.getAtividades()) {
                        atvFic.setFicha(fichaPredefinida);
                        atvFic = fichaService.inserirAtividadeFicha(key, atvFic);

                        for (AtividadeFicha atividadeFicha : atividadesFicha) {
                            if (atividadeFicha.getAtividade().getCodigo().equals(atvFic.getAtividade().getCodigo()) &&
                                    (atividadeFicha.getMetodoExecucao() != null && (atividadeFicha.getMetodoExecucao().equals(MetodoExecucaoEnum.BI_SET) ||
                                            atividadeFicha.getMetodoExecucao().equals(MetodoExecucaoEnum.TRI_SET)))) {
                                novasAtividadeFicha.add(atvFic);
                                idsAtividadeFicha.put(atividadeFicha.getCodigo().toString(), atvFic.getCodigo());
                            }
                        }
                        for (Serie serie : atvFic.getSeries()) {
                            serie.setAtividadeFicha(atvFic);
                            serieService.inserir(key, serie);
                        }
                    }

                    if (!UteisValidacao.emptyList(novasAtividadeFicha)) {
                        atualizarSetsAtividadeFichaPreDefinido(key, novasAtividadeFicha, idsAtividadeFicha);
                    }

                    ProgramaTreinoFicha ptf = new ProgramaTreinoFicha();
                    ptf.setFicha(fichaPredefinida);
                    ptf.setPrograma(preDefinido);

                    programatreinofichaDao.insert(key, ptf);

                }
            } else {
                throw new ServiceException(ProgramaTreinoExcecoes.ERRO_PROGRAMA_TREINO_PREDEFINIDO_DUPLICADO);
            }


        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_TORNAR_PROGRAMA_TREINO_PREDEFINIDO, e);
        }
    }

    private void atualizarSetsAtividadeFichaPreDefinido(String ctx, List<AtividadeFicha> atividadeFichas, HashMap<String, Object> idsAtividadeFicha) throws ServiceException {
        for (AtividadeFicha atividadeFicha : atividadeFichas) {
            String[] vinculoIds = atividadeFicha.getSetId().split("\\|");

            atividadeFicha.setSetId("");
            for (String vinculoId : vinculoIds) {
                for (Map.Entry<String, Object> entry : idsAtividadeFicha.entrySet()) {
                    if (vinculoId.equals(entry.getKey())) {
                        atividadeFicha.setSetId(atividadeFicha.getSetId() + "|" + entry.getValue());
                    }
                }
            }
            atividadeFicha.setSetId(atividadeFicha.getSetId().replaceFirst("\\|", ""));
            fichaService.alterarAtividadeFicha(ctx, atividadeFicha);
        }
    }


    @Override
    public ProgramaTreino escolherPreDefinido(String key, ClienteSintetico cliente, ProgramaTreino predefinido,
                                              Usuario usuario, Boolean verificarConfiguracaoSistema, Integer colaborador, ProgramaTreinoTO programaTreinoTO) throws Exception {
        ProgramaTreino novo = gerarProgramaDefault(key, cliente, usuario, colaborador, programaTreinoTO);
        List<ProgramaTreino> programas;
        if (cliente != null) {
            programas = obterProgramasPorCliente(key, cliente.getCodigo(), novo.getDataInicio(), novo.getDataTerminoPrevisto(), novo.getCodigo(), null);
            if (!programas.isEmpty()) {
                // Buscar o programa atual do cliente para manter a duração
                ProgramaTreino programaAtual = null;
                if (!programas.isEmpty()) {
                    programaAtual = programas.get(0); // Programa mais recente
                }
                novo = criarProximoProgramaTreino(key, usuario, cliente, programaAtual);
                novo.setProgramaTreinoRenovacao(predefinido.getCodigo());
            }
        } else {
            programas = obterProgramasPorColaborador(key, colaborador, novo.getDataInicio(), novo.getDataTerminoPrevisto(), novo.getCodigo(), null);
            if (!programas.isEmpty()) {
                novo = criarProximoProgramaTreinoColaborador(key, colaborador, programaTreinoTO);
                novo.setProgramaTreinoRenovacao(predefinido.getCodigo());
                novo.setCodigoColaborador(colaborador);
            }
        }

        // Aplicar apenas o NOME do predefinido, mantendo a duração do programa atual
        novo.setNome(predefinido.getNome());

        // Só aplicar configurações do predefinido se for um programa novo (sem programas existentes)
        if (programas.isEmpty()) {
            novo.setDiasPorSemana(predefinido.getDiasPorSemana());

            if (predefinido.getDataInicio() == null && predefinido.getDataTerminoPrevisto() == null
                    && (predefinido.getTotalAulasPrevistas() == null || predefinido.getTotalAulasPrevistas() == 0)) {
                predefinido.setTotalAulasPrevistas(36);
            }

            if (predefinido.getDataInicio() == null && predefinido.getDataTerminoPrevisto() == null
                    && predefinido.getTotalAulasPrevistas() != null
                    && predefinido.getTotalAulasPrevistas() > 0) {
                novo.setTotalAulasPrevistas(predefinido.getTotalAulasPrevistas() != null ? predefinido.getTotalAulasPrevistas() : novo.getTotalAulasPrevistas());
                calcularTerminoPrevisto(key, novo);
            }
        }

        novo = inserir(key, novo, verificarConfiguracaoSistema);

        List<ProgramaTreinoFicha> fichas = obterFichaPorPrograma(key, predefinido.getCodigo(), null, true);
        fichas = Ordenacao.ordenarLista(fichas, "codigo");

        for (ProgramaTreinoFicha f : fichas) {
            List<AtividadeFicha> atividadesFicha = fichaService.obterAtividadesFicha(key, f.getFicha().getCodigo());
            Ficha ficha = new Ficha(f.getFicha(), atividadesFicha, false);
            gravarFichaSemTratarExcecao(key, true,
                    f.getTipoExecucao(),
                    f.getDiaSemana(),
                    novo,
                    ficha,
                    f.getFicha().getCategoria() == null ? null : f.getFicha().getCategoria().getCodigo(),
                    f, true, usuario.getProfessor(), false);
        }
        return novo;
    }

    private ProgramaTreinoResponseTO programaTreinoFranqueadora(Integer id,
                                                                String chaveOrigem) throws Exception {
        JSONObject result = Uteis.getJSON(Aplicacao.getProp(Aplicacao.discoveryUrls) + "/find/" + chaveOrigem);
        String treinoUrl = result.getJSONObject("content").getJSONObject("serviceUrls").getString("treinoUrl");
        HttpGet httpGet = new HttpGet(treinoUrl + "/prest/programa/franqueadora/" + id + "/" + chaveOrigem);
        httpGet.setHeader("Content-Type", "application/json");
        HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpResponse response = client.execute(httpGet);
        String responseBody = EntityUtils.toString(response.getEntity());
        JSONObject json = new JSONObject(responseBody);
        if (json.has("content")) {
            return JSONMapper.getObject(json.getJSONObject("content"), ProgramaTreinoResponseTO.class);
        }
        throw new Exception("nao foi possivel obter o treino padrao da franqueadora");
    }

    public ProgramaTreino escolherPreDefinidoFranqueadora(String key,
                                                          ClienteSintetico cliente,
                                                          Integer codigoColaborador,
                                                          Integer id,
                                                          String chaveOrigem,
                                                          Usuario usuario, Boolean verificarConfiguracaoSistema, ProgramaTreinoTO programaTreinoTO) throws Exception {
        try {
            ProgramaTreino novo = gerarProgramaDefault(key, cliente, usuario, codigoColaborador, programaTreinoTO);
            List<ProgramaTreino> programas = cliente == null ? new ArrayList<>() :
                    obterProgramasPorCliente(key, cliente.getCodigo(), novo.getDataInicio(), novo.getDataTerminoPrevisto(), novo.getCodigo(), null);
            if (!programas.isEmpty()) {
                novo = criarProximoProgramaTreino(key, cliente);
            }
            ProgramaTreinoResponseTO predefinido = programaTreinoFranqueadora(id, chaveOrigem);
            novo.setNome(predefinido.getNome());
            novo.setDiasPorSemana(predefinido.getQtdDiasSemana());

            if (predefinido.getTotalTreinos() != null) {
                novo.setTotalAulasPrevistas(predefinido.getTotalTreinos() != null ? predefinido.getTotalTreinos() : novo.getTotalAulasPrevistas());
                calcularTerminoPrevisto(key, novo);
            } else {
                calcularAulasPrevistas(novo);
            }

            novo = inserir(key, novo, verificarConfiguracaoSistema);

            List<FichaResponseTO> fichas = Ordenacao.ordenarLista(predefinido.getFichas(), "id");

            for (FichaResponseTO fichaTO : fichas) {
                if (fichaTO == null) {
                    continue;
                }
                List<AtividadeFicha> atividadesFicha = new ArrayList<>();
                if (fichaTO.getAtividades() != null) {
                    for (AtividadeFichaResponseTO af : fichaTO.getAtividades()) {
                        if (af == null || af.getAtividade() == null) {
                            continue;
                        }
                        Atividade atividade = atividadeDao.obterPorNomeExato(key, af.getAtividade().getNome());
                        if (atividade == null) {
                            atividade = new Atividade();
                            atividade.setNome(af.getAtividade().getNome());
                            atividade = atividadeDao.insert(key, atividade);
                        }
                        AtividadeFicha atividadeFicha = new AtividadeFicha();
                        atividadeFicha.setAtividade(atividade);
                        atividadeFicha.setNome(af.getNomeNaFicha() != null ? af.getNomeNaFicha() : "");
                        atividadeFicha.setOrdem(af.getSequencia() != null ? af.getSequencia() : 0);
                        atividadeFicha.setComplementoNomeAtividade(af.getComplementoNomeAtividade() != null ? af.getComplementoNomeAtividade() : "");
                    List<Serie> series = new ArrayList<>();
                    if (af.getSeries() != null && !af.getSeries().isEmpty()) {
                        for (SerieResponseTO serieTO : af.getSeries()) {
                            if (serieTO != null) {
                                Serie serie = new Serie();
                                Integer sequencia = serieTO.getSequencia();
                                if (sequencia == null) {
                                    serie.setOrdem(0);
                                } else {
                                    serie.setOrdem(sequencia);
                                }
                                serie.setRepeticaoComp(serieTO.getRepeticoes() != null ? serieTO.getRepeticoes() : String.valueOf(0));
                                serie.setCargaComp(serieTO.getCarga() != null ? serieTO.getCarga() : String.valueOf(0.0));
                                serie.setarCompValores();
                                serie.setCadencia(serieTO.getCadencia() != null ? serieTO.getCadencia() : "");
                                serie.setDescanso(serieTO.getDescanso() != null ? serieTO.getDescanso() : 0);
                                serie.setComplemento(serieTO.getComplemento() != null ? serieTO.getComplemento() : "");
                                serie.setVelocidade(serieTO.getVelocidade() != null ? serieTO.getVelocidade() : 0.0);
                                serie.setDuracao(serieTO.getDuracao() != null ? serieTO.getDuracao() : 0);
                                serie.setDistancia(serieTO.getDistancia() != null ? serieTO.getDistancia() : 0);
                                serie.setAtividadeFicha(atividadeFicha);
                                series.add(serie);
                            }
                        }
                    }
                    atividadeFicha.setSeries(series);
                    atividadesFicha.add(atividadeFicha);
                    }
                }
                Ficha fichaFranqueadora = new Ficha();
                fichaFranqueadora.setNome(fichaTO.getNome());
                fichaFranqueadora.setMensagemAluno(fichaTO.getMensagem());
                Ficha ficha = new Ficha(fichaFranqueadora, atividadesFicha, false);
                gravarFichaSemTratarExcecao(key, true,
                        fichaTO.getTipo_execucao(),
                        fichaTO.getDias_semana(),
                        novo,
                        ficha,
                        null,
                        null, true, usuario.getProfessor(), false);
            }
            return novo;
        } catch (Exception e) {
            Uteis.logar(e, ProgramaTreinoServiceImpl.class);
            throw e;
        }
    }

    private ProgramaTreinoResponseTO prepararProgramaTreinoResponseTO(String ctx, ProgramaTreino pt) throws Exception {
        Usuario usuario = null;
        if (pt.getProfessorCarteira() != null) {
            usuario = usuarioService.consultarPorProfessor(ctx, pt.getProfessorCarteira().getCodigo());
        }

        if (pt.getDiasPorSemana() == null || pt.getTotalAulasPrevistas() == null) {
            List<ProgramaTreinoFicha> fichas = this.programaTreinoFichaDao.obterPorProgramaTreino(ctx, pt.getCodigo());
            pt.setDiasPorSemana(fichas.size());

            if (pt.getDataInicio() != null && pt.getDataTerminoPrevisto() != null) {
                long diferenca = pt.getDataTerminoPrevisto().getTime() - pt.getDataInicio().getTime();
                int semanas = (int) (diferenca / Uteis.calcularMillisPorSemana());
                pt.setTotalAulasPrevistas(semanas * pt.getDiasPorSemana());
            }

            programatreinoDao.update(ctx, pt);
        }

        ProgramaTreinoResponseTO ptr = new ProgramaTreinoResponseTO(pt, usuario,
                this.programaTreinoFichaDao.obterPorProgramaTreino(ctx, pt.getCodigo()),
                SuperControle.independente(ctx));

        if (pt.getProfessorCarteira() != null && ptr.getProfessor() != null) {
            ptr.getProfessor().setImageUri(Aplicacao.obterUrlFotoDaNuvem(pt.getProfessorCarteira().getPessoa().getFotoKey()));
        }

        List<FichaResponseTO> fichas = ptr.getFichas();
        fichas.sort(Comparator
                .comparing(FichaResponseTO::getOrdem, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(FichaResponseTO::getId)
        );

        int maxOrdemExistente = fichas.stream()
                .filter(f -> f.getOrdem() != null)
                .mapToInt(FichaResponseTO::getOrdem)
                .max()
                .orElse(0);

        int proximaOrdem = maxOrdemExistente + 1;

        for (FichaResponseTO ficha : fichas) {
            if (ficha.getOrdem() == null) {
                ficha.setOrdem(proximaOrdem);
                proximaOrdem++;
            }
        }
        ptr.setFichas(fichas);

        return ptr;
    }


    @Override
    public List<ProgramaTreinoResponseTO> obterProgramasPreDefinidos(PaginadorDTO paginadorDTO, FiltroProgramaTreinoJSON filtros, Boolean app) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<ProgramaTreino> pts;
            if (filtros == null && paginadorDTO == null) {
                pts = programasPredefinidosAtivos(ctx);
            } else {
                try {
                    String quickSearch = "";
                    Integer situacao = null;
                    try {
                        quickSearch = new JSONObject(filtros).optString("quicksearchValue");
                        JSONArray situacoes = new JSONObject(filtros).optJSONArray("situacao");
                        if (situacoes != null && situacoes.length() <= 1) {
                            situacao = situacoes.get(0).toString().equalsIgnoreCase("ATIVO") ? 0 : 1;
                        }
                    } catch (Exception e) {
                    }
                    int indiceInicial = 0;
                    int maxResults = paginadorDTO.getSize() == null ? MAXIMO_ATIVIDADES_LISTAR : paginadorDTO.getSize().intValue();
                    indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
                    StringBuilder hql = new StringBuilder();
                    Map<String, Object> param = new HashMap<>();
                    hql.append("SELECT obj FROM ProgramaTreino obj where obj.preDefinido IS TRUE ");

                    if (filtros != null && (filtros.getNome()) && (!filtros.getParametro().trim().equals(""))) {
                        hql.append(" AND upper(obj.nome) like CONCAT('%',:nome,'%')");
                        param.put("nome", filtros.getParametro().toUpperCase());
                    }

                    if (filtros != null && filtros.getAtivo() != null) {
                        if (!Boolean.parseBoolean(filtros.getAtivo())) {
                            hql.append(" AND obj.situacao = '" + ProgramaSituacaoEnum.INATIVO.getId() + "' ");
                        } else {
                            hql.append(" AND obj.situacao = '" + ProgramaSituacaoEnum.ATIVO.getId() + "' ");
                        }
                    }

                    if (filtros != null && !filtros.getGeneroList().isEmpty()) {
                        if (filtros.getGeneroList().size() < 3) {
                            String whereGenero = "";
                            for (String genero : filtros.getGeneroList()) {
                                if (whereGenero.equals("")) {
                                    whereGenero = "'" + genero + "'";
                                } else {
                                    whereGenero = whereGenero + ",'" + genero + "'";
                                }
                            }
                            hql.append(" AND  genero in ( " + whereGenero + ") ");
                        }
                    }

                    if (paginadorDTO != null) {
                        paginadorDTO.setQuantidadeTotalElementos(programatreinoDao.countWithParamHqlFullFromSizeList(ctx, hql.toString(), param).longValue());
                    }
                    if (paginadorDTO.getSort() == null) {
                        if (filtros != null && filtros.getOrdenacao() != null) {
                            hql.append(" ORDER BY obj.nome " + filtros.getOrdenacao());
                        } else {
                            hql.append(" ORDER BY obj.nome ASC");
                        }

                    } else {
                        hql.append(paginadorDTO.getSQLOrderByUse());
                    }

                    pts = programatreinoDao.findByParam(ctx, hql.toString(), param, maxResults, indiceInicial);
                    paginadorDTO.setSize((long) maxResults);
                    paginadorDTO.setPage((long) indiceInicial);
                } catch (Exception e) {
                    throw new ServiceException(ProgramaTreinoExcecoes.ERRO_BUSCAR_PROGRAMA_TREINO, e);
                }
            }
            List<ProgramaTreinoResponseTO> ret = new ArrayList<ProgramaTreinoResponseTO>();
            for (ProgramaTreino pt : pts) {
                ProgramaTreinoResponseTO ptr = new ProgramaTreinoResponseTO(pt, null,
                        this.programaTreinoFichaDao.obterPorProgramaTreino(ctx, pt.getCodigo()),
                        SuperControle.independente(ctx));
                if (pt.getProfessorCarteira() != null && ptr.getProfessor() != null) {
                    ptr.getProfessor().setImageUri(Aplicacao.obterUrlFotoDaNuvem(pt.getProfessorCarteira().getPessoa().getFotoKey()));
                }
                Comparator<FichaResponseTO> colunaOrdenarFicha;
                colunaOrdenarFicha = (FichaResponseTO o1, FichaResponseTO o2) -> o1.getId().compareTo(o2.getId());
                Collections.sort(ptr.getFichas(), colunaOrdenarFicha);
                ret.add(ptr);
            }

            if(app) {
                try {
                    String chaveFranqueadora = obtemChaveRede(ctx);

                    if(possuiRedeFranqueadora(chaveFranqueadora)) {
                        String nome = filtros.getNome() ? filtros.getParametro().toUpperCase() : "";
                        ret.addAll(obterDaFranqueadora(nome, chaveFranqueadora));
                    }
                } catch (Exception e) {
                    Uteis.logar(e, ProgramaTreinoServiceImpl.class);
                    System.out.println("Erro ao buscar programas da franqueadora: " + e.getMessage());
                }
            }

            return ret;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_BUSCAR_PROGRAMAS_TREINO_PREDEFINIDOS, e);
        }
    }

    private boolean possuiRedeFranqueadora(String chaveFranqueadora) {
        return !UteisValidacao.emptyString(chaveFranqueadora)
                && !SEM_REDE_FRANQUEADORA.equals(chaveFranqueadora);
    }

    private String obtemChaveRede(String ctx) throws IOException {
               String url = Aplicacao.getProp(Aplicacao.urlOAMD) + "/prest/empresaFinanceiro/configsTreinoRede?chaveZW=" + ctx;
        String identificador = "chaveFranqueadora-" + ctx;
        String chaveFranqueadora = memcached.ler(ctx, identificador);

        if(!UteisValidacao.emptyString(chaveFranqueadora)) {
            return chaveFranqueadora;
        }
        CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpGet httpGet = new HttpGet(url);
        CloseableHttpResponse response = client.execute(httpGet);
        String s = EntityUtils.toString(response.getEntity());
        JSONObject json = new JSONObject(s).getJSONObject("configsTreinoRede");
        String chave = json.optString("chaveFranqueadora");
        chaveFranqueadora = UteisValidacao.emptyString(chave) ? SEM_REDE_FRANQUEADORA : chave;

        memcached.gravar(ctx, identificador, chaveFranqueadora);

        return chaveFranqueadora;

    }

    @Override
    public List<ProgramaTreinoResponseTO> obterProgramasPreDefinidosPorChave(String ctx) throws ServiceException {
        try {
            List<ProgramaTreino> pts = programasPredefinidosAtivos(ctx);
            List<ProgramaTreinoResponseTO> ret = new ArrayList<ProgramaTreinoResponseTO>();
            for (ProgramaTreino pt : pts) {
                ProgramaTreinoResponseTO ptr = new ProgramaTreinoResponseTO(
                        pt, null, this.programaTreinoFichaDao.obterPorProgramaTreino(ctx, pt.getCodigo()), SuperControle.independente(ctx)
                );
                Comparator<FichaResponseTO> colunaOrdenarFicha;
                colunaOrdenarFicha = (FichaResponseTO o1, FichaResponseTO o2) -> o1.getId().compareTo(o2.getId());
                Collections.sort(ptr.getFichas(), colunaOrdenarFicha);
                ret.add(ptr);
            }
            return ret;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_BUSCAR_PROGRAMAS_TREINO_PREDEFINIDOS, e);
        }
    }

    public List<ProgramaTreinoResponseTO> processarListagem(List<ProgramaTreinoResponseTO> lista, PaginadorDTO paginadorDTO, String filtros)
            throws JSONException {
        List<ProgramaTreinoResponseTO> listaFiltrada = new ArrayList<ProgramaTreinoResponseTO>();
        for (ProgramaTreinoResponseTO pt : lista) {
            String genero = "";
            if (pt.getGenero() != null) {
                switch (pt.getGenero()) {
                    case "F":
                        genero = "Feminino";
                        break;
                    case "M":
                        genero = "Masculino";
                        break;
                    default:
                        genero = "Feminino, Masculino";
                }
            }
            pt.setGenero(genero);
        }

        JSONObject objJson = new JSONObject(filtros);
        String search = objJson.optString("quicksearchValue");
        if (!search.isEmpty()) {
            for (ProgramaTreinoResponseTO pt : lista) {
                if (pt.getNome().toLowerCase().contains(search.toLowerCase())
                        || pt.getGenero().toLowerCase().contains(search.toLowerCase())
                        || pt.getProfessorMontou().getNome().toLowerCase().contains(search.toLowerCase())
                        || pt.getSituacao().getDescricao().toLowerCase().contains(search.toLowerCase())) {
                    listaFiltrada.add(pt);
                }
            }
        } else {
            listaFiltrada = lista;
        }

        if (paginadorDTO.getSort() != null) {
            String array[] = paginadorDTO.getSort().split(",");
            Comparator<ProgramaTreinoResponseTO> coluna;
            if (array[1].equals("ASC")) {
                if (array[0].equals("nome")) {
                    coluna = (ProgramaTreinoResponseTO o1, ProgramaTreinoResponseTO o2) -> o1.getNome().toLowerCase().compareTo(o2.getNome().toLowerCase());
                } else if (array[0].equals("genero")) {
                    coluna = (ProgramaTreinoResponseTO o1, ProgramaTreinoResponseTO o2) -> o1.getGenero().toLowerCase().compareTo(o2.getGenero().toLowerCase());
                } else if (array[0].equals("professorMontou.nome")) {
                    coluna = (ProgramaTreinoResponseTO o1, ProgramaTreinoResponseTO o2) -> o1.getProfessorMontou().getNome().toLowerCase().compareTo(o2.getProfessorMontou().getNome().toLowerCase());
                } else {
                    coluna = (ProgramaTreinoResponseTO o1, ProgramaTreinoResponseTO o2) -> o1.getSituacao().compareTo(o2.getSituacao());
                }
                Collections.sort(listaFiltrada, coluna);
            } else {
                if (array[0].equals("nome")) {
                    coluna = (ProgramaTreinoResponseTO o1, ProgramaTreinoResponseTO o2) -> o1.getNome().toLowerCase().compareTo(o2.getNome().toLowerCase());
                } else if (array[0].equals("genero")) {
                    coluna = (ProgramaTreinoResponseTO o1, ProgramaTreinoResponseTO o2) -> o1.getGenero().toLowerCase().compareTo(o2.getGenero().toLowerCase());
                } else if (array[0].equals("professorMontou.nome")) {
                    coluna = (ProgramaTreinoResponseTO o1, ProgramaTreinoResponseTO o2) -> o1.getProfessorMontou().getNome().toLowerCase().compareTo(o2.getProfessorMontou().getNome().toLowerCase());
                } else {
                    coluna = (ProgramaTreinoResponseTO o1, ProgramaTreinoResponseTO o2) -> o1.getSituacao().compareTo(o2.getSituacao());
                }
                Collections.sort(listaFiltrada, coluna.reversed());
            }
        }
        paginadorDTO.setQuantidadeTotalElementos((long) listaFiltrada.size());
        return paginadorProgramasPredefinidos(listaFiltrada, paginadorDTO);
    }

    public List<ProgramaTreinoResponseTO> paginadorProgramasPredefinidos(List<ProgramaTreinoResponseTO> programas, PaginadorDTO paginadorDTO) {
        List<ProgramaTreinoResponseTO> listaProgramasPaginada = new ArrayList();
        if (paginadorDTO.getSize() != null) {
            if (paginadorDTO.getSize() > 0) {
                int i = 0;
                int iPaginador = 0;
                long pular;
                for (ProgramaTreinoResponseTO pp : programas) {
                    pular = paginadorDTO.getPage() * 10;
                    if (paginadorDTO.getPage() > 0) {
                        if (iPaginador >= pular) {
                            listaProgramasPaginada.add(pp);
                            i++;
                        }
                        iPaginador++;
                    } else {
                        listaProgramasPaginada.add(pp);
                        i++;
                        iPaginador++;
                    }
                    if (i == paginadorDTO.getSize() || programas.size() - iPaginador == 0) {
                        paginadorDTO.setQuantidadeTotalElementos((long) programas.size());
                        return listaProgramasPaginada;
                    }
                }
            }
        }
        return listaProgramasPaginada;
    }

    @Override
    public ProgramaTreinoResponseTO consultarProgramaTreino(Integer programaTreinoId, String chaveFranqueadora) throws ServiceException {
        try {
            String ctx = chaveFranqueadora == null ? sessaoService.getUsuarioAtual().getChave() : chaveFranqueadora;
            ProgramaTreino pt = obterPorId(ctx, programaTreinoId);
            ProgramaTreinoResponseTO programaReturn = prepararProgramaTreinoResponseTO(ctx, pt);

            for (FichaResponseTO ficha : programaReturn.getFichas()) {
                List<AtividadeFichaResponseTO> atividades = new ArrayList<>();
                List<AtividadeFichaResponseTO> atividadesOrenadas = ficha.getAtividades();
                atividadesOrenadas.sort(Comparator.comparing(AtividadeFichaResponseTO::getSequencia));
                for (AtividadeFichaResponseTO atividadeResponse : atividadesOrenadas) {
                    atividades.add(obterVinculosAtividadeFicha(ctx, atividadeResponse));
                }
                ficha.setAtividades(atividades);
            }
            ;
            return programaReturn;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_INCLUIR_PROGRAMA_TREINO, e);
        }
    }

    @Override
    public Integer consultarProgramaTreinoAnterior(Integer clienteCodigo, Integer programaTreinoId, String chaveFranqueadora) throws ServiceException {
        try {
            String ctx = chaveFranqueadora == null ? sessaoService.getUsuarioAtual().getChave() : chaveFranqueadora;
            return obterCodigoProgramaAnterior(ctx, clienteCodigo, programaTreinoId);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_INCLUIR_PROGRAMA_TREINO, e);
        }
    }

    private Integer obterCodigoProgramaAnterior(String ctx, Integer codCliente, Integer codPrograma) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select codigo from programatreino p where cliente_codigo = ")
                .append(codCliente).append(" and codigo < ").append(codPrograma).append(" order by datainicio desc limit 1 ");

        Integer codigoProgramaAnterior = 0;
        try (ResultSet rs = programatreinoDao.createStatement(ctx, sql.toString())) {
            if (rs.next()) {
                codigoProgramaAnterior = rs.getInt("codigo");
            }
        }

        return codigoProgramaAnterior;
    }

    private AtividadeFichaResponseTO obterVinculosAtividadeFicha(String ctx, AtividadeFichaResponseTO atividadeFichaResponseTO) throws Exception {
        AtividadeFicha atividadeFicha = atividadeFichaDao.findById(ctx, atividadeFichaResponseTO.getId());
        List<AtividadeFicha> atividadeFichasVinculadas = new ArrayList<>();

        if (atividadeFichaResponseTO.getMetodoExecucao() != null &&
                (atividadeFichaResponseTO.getMetodoExecucao().equals(MetodoExecucaoEnum.BI_SET) || atividadeFichaResponseTO.getMetodoExecucao().equals(MetodoExecucaoEnum.TRI_SET))) {

            if (!StringUtils.isBlank(atividadeFicha.getSetId())) {
                String[] setAtividades = atividadeFicha.getSetId().split("\\|");
                for (String setAtividade : setAtividades) {
                    if (!atividadeFicha.getCodigo().equals(Integer.valueOf(setAtividade))) {
                        AtividadeFicha atividadeVinculada = atividadeFichaDao.findById(ctx, Integer.valueOf(setAtividade));
                        atividadeFichasVinculadas.add(atividadeVinculada);
                    }
                }
            }
        }
        AtividadeFichaResponseTO atividadeJSON = new AtividadeFichaResponseTO(atividadeFicha, atividadeFichasVinculadas);

        List<AtividadeVideoTO> listaAtividadeVideosJSON = new ArrayList<>();
        for (AtividadeVideo link : atividadeFicha.getAtividade().getLinkVideos()) {
            listaAtividadeVideosJSON.add(new AtividadeVideoTO(link));
        }
        atividadeJSON.getAtividade().setUrlLinkVideos(listaAtividadeVideosJSON);
        return atividadeJSON;
    }

    private ProgramaTreino montarPrograma(ProgramaTreinoTO programaTreinoTO, Boolean novo) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            if (usuario == null) {
                throw new ServiceException(ProgramaTreinoExcecoes.ERRO_BUSCAR_PROFESSOR_SESSAO);
            }
            if (usuario.getProfessor() == null) {
                throw new ServiceException(ProgramaTreinoExcecoes.ERRO_BUSCAR_PROFESSOR_SESSAO);
            }
            ClienteSintetico cliente = programaTreinoTO.getAlunoId() == null ? null :
                    cs.obterPorCodigo(ctx, programaTreinoTO.getAlunoId());
            if (novo) {
                return gerarProgramaDefault(ctx, cliente, usuario, programaTreinoTO.getColaboradorId(), programaTreinoTO);
            } else {
                ProgramaTreino pt = obterPorId(ctx, programaTreinoTO.getId());
                boolean predefinido = programaTreinoTO.getPredefinido() != null && programaTreinoTO.getPredefinido() ? true : false;
                if (pt == null) {
                    throw new ServiceException(ProgramaTreinoExcecoes.PROGRAMA_TREINO_NAO_ENCONTRADO);
                }
                ProgramaTreino programaConflitante = validarProgramaConflitante(ctx, programaTreinoTO);
                if (programaConflitante != null) {
                    throw new ServiceException(ProgramaTreinoExcecoes.ERRO_PERIODO_INVALIDO);
                }
                ProfessorSintetico professor;
                if (SuperControle.independente(ctx)) {
                    professor = professorService.obterPorId(ctx, programaTreinoTO.getProfessorId());
                } else {
                    professor = professorService.consultarPorCodigoColaborador(ctx, programaTreinoTO.getProfessorId());
                }
                pt.setNome(programaTreinoTO.getNome());
                pt.setProfessorMontou(professor);
                if (!predefinido) {
                    pt.setProfessorCarteira(cliente == null ? null : cliente.getProfessorSintetico());
                    pt.setDataInicio(programaTreinoTO.getInicioComoData());
                    pt.setDataTerminoPrevisto(programaTreinoTO.getTerminoComoData());
                }
                pt.setTotalAulasPrevistas(programaTreinoTO.getTotalTreinos());
                pt.setDiasPorSemana(programaTreinoTO.getQtdDiasSemana());
                if (programaTreinoTO.getEmRevisaoProfessor() != null) {
                    pt.setEmRevisaoProfessor(programaTreinoTO.getEmRevisaoProfessor());
                }
                if (programaTreinoTO.getGeradoPorIA() != null) {
                    pt.setGeradoPorIA(programaTreinoTO.getGeradoPorIA());
                }
                if (programaTreinoTO.getColaboradorId() != null) {
                    pt.setCodigoColaborador(programaTreinoTO.getColaboradorId());
                }
                return pt;
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            if (novo) {
                throw new ServiceException(ProgramaTreinoExcecoes.ERRO_MONTAR_PROGRAMA_TREINO);
            } else {
                throw new ServiceException(ProgramaTreinoExcecoes.ERRO_BUSCAR_PROGRAMA_TREINO);
            }
        }
    }

    private ProgramaTreino montarProgramaPredefinido(ProgramaTreinoTO programaTreinoTO, Boolean novo, String ctx) throws ServiceException {
        try {
            Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            if (usuario == null) {
                throw new ServiceException(ProgramaTreinoExcecoes.ERRO_BUSCAR_PROFESSOR_SESSAO);
            }
            if (usuario.getProfessor() == null) {
                throw new ServiceException(ProgramaTreinoExcecoes.ERRO_BUSCAR_PROFESSOR_SESSAO);
            }
            return gerarProgramaPredefinidoDefault(ctx, programaTreinoTO);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            if (novo) {
                throw new ServiceException(ProgramaTreinoExcecoes.ERRO_MONTAR_PROGRAMA_TREINO);
            } else {
                throw new ServiceException(ProgramaTreinoExcecoes.ERRO_BUSCAR_PROGRAMA_TREINO);
            }
        }
    }

    private ProgramaTreino validarProgramaConflitante(String ctx, ProgramaTreinoTO programaTreinoTO) throws ServiceException {
        ProgramaTreino result = null;
        List<ProgramaTreino> programasCliente = new ArrayList<ProgramaTreino>();
        if (!UteisValidacao.emptyNumber(programaTreinoTO.getAlunoId())) {
            programasCliente = obterProgramasPorCliente(ctx, programaTreinoTO.getAlunoId(), null, null, null, null);
        } else if (!UteisValidacao.emptyNumber(programaTreinoTO.getColaboradorId())) {
            programasCliente = obterProgramasPorColaborador(ctx, programaTreinoTO.getColaboradorId(), null, null, null, null);
        }
        if (programasCliente.size() > 1) {
            for (ProgramaTreino programa : programasCliente) {
                if (!programa.getCodigo().equals(programaTreinoTO.getId())) {
                    Date inicio = new Date(programaTreinoTO.getInicio());
                    Date termino = new Date(programaTreinoTO.getTermino());
                    boolean inicioDtoMenorIgualInicioItem = Calendario.menor(inicio, programa.getDataInicio()) || inicio.getTime() == programa.getDataInicio().getTime();
                    boolean inicioDtoMaiorIgualInicioItem = Calendario.maior(inicio, programa.getDataInicio()) || inicio.getTime() == programa.getDataInicio().getTime();
                    boolean inicioDtoMenorIgualTerminoItem = Calendario.menor(inicio, programa.getDataTerminoPrevisto()) || inicio.getTime() == programa.getDataTerminoPrevisto().getTime();
                    boolean terminoDtoMaiorIgualInicioItem = Calendario.maior(termino, programa.getDataInicio()) || termino.getTime() == programa.getDataInicio().getTime();
                    boolean terminoDtoMenorIgualTerminoItem = Calendario.menor(termino, programa.getDataTerminoPrevisto()) || termino.getTime() == programa.getDataTerminoPrevisto().getTime();
                    boolean terminoDtoMaiorIgualTerminoItem = Calendario.maior(termino, programa.getDataTerminoPrevisto()) || termino.getTime() == programa.getDataTerminoPrevisto().getTime();

                    if ((inicioDtoMenorIgualInicioItem && terminoDtoMaiorIgualInicioItem && terminoDtoMenorIgualTerminoItem)
                            || (inicioDtoMaiorIgualInicioItem && inicioDtoMenorIgualTerminoItem && terminoDtoMaiorIgualTerminoItem)
                            || (inicioDtoMenorIgualInicioItem && terminoDtoMaiorIgualTerminoItem)
                            || (inicioDtoMaiorIgualInicioItem && terminoDtoMenorIgualTerminoItem)) {
                        result = programa;
                        break;
                    }
                }
            }
        }
        return result;
    }

    public ProgramaTreinoResponseTO programaConflitante(ProgramaTreinoTO programaTreinoTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            ProgramaTreino programaConflitante = validarProgramaConflitante(ctx, programaTreinoTO);
            ProgramaTreinoResponseTO result = null;
            if (programaConflitante != null) {
                result = prepararProgramaTreinoResponseTO(ctx, programaConflitante);
            }
            return result;
        } catch (Exception ex) {
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_VALIDAR_PROGRAMA_CONFLITANTE);
        }
    }

    @Override
    public ProgramaTreinoResponseTO updateCalculosAulasPrevistas(Integer programaId, String campoAlterado, String value, Long inicio, Long termino, Integer totalTreinos, Integer qtdDiasSemana) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            ProgramaTreino pt = programatreinoDao.obterPorId(ctx, programaId);
            pt.setDataInicio(new Date(inicio));
            pt.setDataTerminoPrevisto(new Date(termino));
            pt.setTotalAulasPrevistas(totalTreinos);
            pt.setDiasPorSemana(qtdDiasSemana);
            if (campoAlterado.equals("inicio")) {
                pt.setDataInicio(new Date(Long.parseLong(value)));
                calcularAulasPrevistas(pt);
            }
            if (campoAlterado.equals("termino")) {
                pt.setDataTerminoPrevisto(new Date(Long.parseLong(value)));
                calcularAulasPrevistas(pt);
            }
            if (campoAlterado.equals("qtdDiasSemana")) {
                pt.setDiasPorSemana(Integer.parseInt(value));
                calcularAulasPrevistas(pt);
            }
            if (campoAlterado.equals("totalTreinos")) {
                pt.setTotalAulasPrevistas(Integer.parseInt(value));
                calcularTerminoPrevisto(ctx, pt);
            }

            return prepararProgramaTreinoResponseTO(ctx, pt);
        } catch (Exception ex) {
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_CALCULAR_AULAS_PREVISTAS);
        }
    }

    @Override
    public ProgramaTreinoResponseTO criarProgramaTreino(Integer empresa,
                                                        ProgramaTreinoTO programaTreinoTO,
                                                        Integer preDefinidoId,
                                                        String chaveFranqueadora,
                                                        Boolean renovarAtual,
                                                        HttpServletRequest request, Integer origemProgramaTreino) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        ClienteSintetico cliente = UteisValidacao.emptyNumber(programaTreinoTO.getAlunoId()) ? null : cs.obterPorCodigo(ctx, programaTreinoTO.getAlunoId());
        if (cliente != null) {
            Hibernate.initialize(cliente.getProgramaTreinos());
            cs.acao(renovarAtual != null && renovarAtual && !UteisValidacao.emptyList(cliente.getProgramaTreinos()) ? AcaoAlunoEnum.RENOVOU_PROGRAMA : AcaoAlunoEnum.NOVO_PROGRAMA, cliente.getMatriculaString());
        }
        Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
        String username = sessaoService.getUsuarioAtual().getUsername();
        if (!sessaoService.getUsuarioAtual().getUsername().equals(usuario.getUserName())) {
            incluirLogErroUsernameProfessor(usuario);
            username = usuario.getUserName();
        }
        ProgramaTreino pt = new ProgramaTreino();
        try {
            if (cliente != null && renovarAtual != null && renovarAtual && !UteisValidacao.emptyList(cliente.getProgramaTreinos())) {
                pt = (ProgramaTreino) Ordenacao.ordenarListaReverse(cliente.getProgramaTreinos(), "dataInicio").get(0);
                Integer codigoProgramaTreinoRenovado = pt.getCodigo();
                pt = escolherPreDefinido(ctx, cliente, pt, usuario, false, programaTreinoTO.getColaboradorId(), null);
                pt.setProgramaTreinoRenovacao(codigoProgramaTreinoRenovado);
            } else if (UteisValidacao.emptyNumber(preDefinidoId)) {
                pt = montarPrograma(programaTreinoTO, true);
                List<ProgramaTreino> programas;
                if (cliente != null) {
                    programas = obterProgramasPorCliente(ctx, pt.getCliente().getCodigo(), pt.getDataInicio(), pt.getDataTerminoPrevisto(), pt.getCodigo(), null);
                    if (!programas.isEmpty()) {
                        pt = criarProximoProgramaTreino(ctx, pt.getCliente());
                    }
                } else {
                    programas = obterProgramasPorColaborador(ctx, programaTreinoTO.getColaboradorId(), pt.getDataInicio(), pt.getDataTerminoPrevisto(), pt.getCodigo(), null);
                    if (!programas.isEmpty()) {
                        pt = criarProximoProgramaTreinoColaborador(ctx, programaTreinoTO.getColaboradorId(), programaTreinoTO);
                    }
                    pt.setCodigoColaborador(programaTreinoTO.getColaboradorId());
                }
                if(usuario != null && usuario.getIdPessoa() != null ){
                    ProfessorSintetico p = professorSinteticoService.obterPorCodigoPessoaZW(ctx, usuario.getIdPessoa(), empresa);
                    if(p != null){
                        pt.setProfessorMontou(p);
                    }
                }
                pt = inserir(ctx, pt, false);
                fichaService.criarFicha(null, pt.getCodigo(), request, null, null);
            } else if (!UteisValidacao.emptyString(chaveFranqueadora)) {
                if(usuario != null && usuario.getIdPessoa() != null ){
                    ProfessorSintetico p = professorSinteticoService.obterPorCodigoPessoaZW(ctx, usuario.getIdPessoa(), empresa);
                    if(p != null){
                        programaTreinoTO.setProfessorMontou(p.getCodigo());
                    }
                }
                pt = escolherPreDefinidoFranqueadora(ctx,
                        cliente,
                        programaTreinoTO.getColaboradorId(),
                        preDefinidoId,
                        chaveFranqueadora,
                        usuario,
                        false,
                        programaTreinoTO);
            } else {
                ProgramaTreino preDefinido = obterPorId(ctx, preDefinidoId);
                pt.setOrigem(preDefinido.getOrigem());
                pt = escolherPreDefinido(ctx, cliente, preDefinido, usuario, false, programaTreinoTO.getColaboradorId(), programaTreinoTO);
                if(usuario != null && usuario.getIdPessoa() != null ){
                    ProfessorSintetico p = professorSinteticoService.obterPorCodigoPessoaZW(ctx, usuario.getIdPessoa(), empresa);
                    if(p != null){
                        programaTreinoTO.setProfessorMontou(p.getCodigo());
                    }
                }else {
                    if(preDefinido.getProfessorMontou() != null) {
                        pt.setProfessorMontou(preDefinido.getProfessorMontou());
                    }
                }
            }
            pt.setProgramaFichas(programaTreinoFichaDao.obterPorProgramaTreino(ctx, pt.getCodigo()));
            atualizarDadoSintetico(ctx, pt, empresa);
            atualizarOrigemProgramaTreino(ctx, origemProgramaTreino, pt);
            ProgramaTreino finalPt = pt;
            String finalUsername = username;
            pt.getProgramaFichas().forEach(pF -> {
                if (!isNull(pF.getFicha())) {
                    incluirLog(ctx, pF.getFicha().getCodigo().toString(), finalPt.getCodigo().toString(), "", pF.getFicha().getDescricaoParaLog(null), "INCLUSÃO", "INCLUSÃO DE FICHA",
                            EntidadeLogEnum.FICHA, "Ficha", finalUsername, logDao, null, null);
                }
            });
            if (pt.getCliente() != null) {
                incluirLog(ctx, pt.getCodigo().toString(), pt.getCliente().getCodigo().toString(), "",
                        pt.getDescricaoParaLog(null), "INCLUSÃO", "INCLUSÃO DE PROGRAMA",
                        EntidadeLogEnum.PROGRAMA, "Programa", username, logDao, null, null);
                PushMobileRunnable.executarNoticacaoAppDoAluno(ctx, "Novo programa de Treino disponível", "Acesse e veja os exercícios e metas para o programa.", usuario.getUserName(), "");
            }
            return prepararProgramaTreinoResponseTO(ctx, pt);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_INCLUIR_PROGRAMA_TREINO, e);
        } finally {
            notificarOuvintes(ctx, pt.getCliente() == null ? null : pt.getCliente().getCodigo(), pt.getCodigoColaborador(), request);
            cs.leaveAcao();
        }
    }

    private void atualizarOrigemProgramaTreino(String ctx, Integer origem, ProgramaTreino pt) {
        try {
            if (!UteisValidacao.emptyNumber(origem)) {
                pt.setOrigem(origem);
                programatreinoDao.update(ctx, pt);
            }
        } catch (Exception e) {
            Uteis.logar(e, ProgramaTreinoServiceImpl.class);
        }
    }

    private void incluirLogErroUsernameProfessor(Usuario usuarioObtidoPorIdSessao) throws ServiceException {
        // RESPONSÁVEL POR REGISTRAR LOG DE ERRO QUANDO O USERNAME NÃO FOR DO MESMO CODIGO DE USUARIOZW DA SESSÃO
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            JSONObject json = new JSONObject();
            json.put("#sessaoService_username", sessaoService.getUsuarioAtual().getUsername());
            json.put("#sessaoService_id_codUserZw", sessaoService.getUsuarioAtual().getId());
            json.put("#sessaoService_colaboradorId", sessaoService.getUsuarioAtual().getColaboradorId());
            json.put("#sessaoService_empresaAtual", sessaoService.getUsuarioAtual().getEmpresaAtual());
            json.put("#sessaoService_chave", sessaoService.getUsuarioAtual().getChave());
            json.put("usuarioById_usernameUsuarioId", usuarioObtidoPorIdSessao.getUserName());
            json.put("usuarioById_nomeProfessor", usuarioObtidoPorIdSessao.getProfessor() != null ? usuarioObtidoPorIdSessao.getProfessor().getNome() : "Usuario sem vinculo com professor");

            logErrosService.incluir(ctx, "ERRO AO CRIAR PROGRAMA: REGISTRA LOG PROGRAMA COM USERNAME DE OUTRO PROFESSOR NÃO LOGADO", json.toString());
        } catch (Exception ex) {
            Uteis.logar(ex, ProgramaTreinoServiceImpl.class);
        }
    }

    private void enviarDataProgramaTreinoColaborador(String ctx, ProgramaTreino pt) {
        try {
            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("chave", ctx));
            params.add(new BasicNameValuePair("fimPrograma", Uteis.getData(pt.getDataTerminoPrevisto())));
            params.add(new BasicNameValuePair("empresa", "0"));
            params.add(new BasicNameValuePair("colaborador", pt.getCodigoColaborador().toString()));
            String retorno = Uteis.chamadaZW(ctx, "/prest/treino/prescricao-colaborador-data", params);
        } catch (Exception e) {
            Uteis.logar(e, ProgramaTreinoService.class);
        }
    }

    private void atualizarDadoSintetico(String ctx, ProgramaTreino pt, Integer empresaZW) {
        try {
            if (pt.getCliente() == null) {
                professorService.sincronizarLancamentoPrograma(ctx, empresaZW, pt.getCodigoColaborador());
                enviarDataProgramaTreinoColaborador(ctx, pt);
            } else {
                programatreinoDao.executeNativeSQL(ctx, "update clientesintetico c \n" +
                        "set terminoUltimoPrograma = (select max(dataterminoprevisto) from programatreino where cliente_codigo = c.codigo)" +
                        " where c.codigo = " + pt.getCliente().getCodigo());
            }

        } catch (Exception e) {
            Uteis.logar(e, ProgramaTreinoService.class);
        }
    }

    private ProgramaTreino criarProximoProgramaTreino(String ctx, ClienteSintetico clienteSintetico) throws ServiceException {
        return criarProximoProgramaTreino(ctx, null, clienteSintetico, null);
    }

    private ProgramaTreino criarProximoProgramaTreino(String ctx,
                                                      Usuario usuario,
                                                      ClienteSintetico clienteSintetico, ProgramaTreino base) throws ServiceException {
        List<ProgramaTreino> programas = obterProgramasPorCliente(ctx, clienteSintetico.getCodigo(), null, null, null, null);
        ProgramaTreino programa = new ProgramaTreino();
        programa.setCliente(clienteSintetico);
        programa.setDataLancamento(Calendario.hoje());
        programa.setDataInicio(programas == null || programas.isEmpty() ?
                Calendario.hoje() :
                Calendario.proximo(Calendar.DAY_OF_MONTH, programas.get(0).getDataTerminoPrevisto()));
        programa.setDataProximaRevisao(Calendario.proximoDeveSerUtil(programa.getDataInicio(), 7));

        if (base == null) {
            ProgramaTreino ultimoPrograma = null;
            if (programas != null && !programas.isEmpty()) {
                ultimoPrograma = programas.get(0);
            }

            programa.setNome("PROG " + Calendario.getData(Calendario.proximo(Calendar.DAY_OF_MONTH, programas.get(0).getDataTerminoPrevisto()), "ddMMyyHHmm"));

            if (ultimoPrograma != null && ultimoPrograma.getDataInicio() != null && ultimoPrograma.getDataTerminoPrevisto() != null) {
                long duracaoEmDias = (ultimoPrograma.getDataTerminoPrevisto().getTime() - ultimoPrograma.getDataInicio().getTime()) / (1000 * 60 * 60 * 24);

                programa.setDiasPorSemana(ultimoPrograma.getDiasPorSemana());
                programa.setTotalAulasPrevistas(ultimoPrograma.getTotalAulasPrevistas());

                int diasParaSomar = (int)duracaoEmDias - 1;

                try {
                    Empresa empresa = empresaService.obterPorIdZW(ctx, programa.getCliente().getEmpresa());
                    Date dataTerminoCalculada = Uteis.somarDias(programa.getDataInicio(), diasParaSomar, empresa.getTimeZoneDefault());
                    programa.setDataTerminoPrevisto(dataTerminoCalculada);
                } catch (Exception e) {
                    Date dataTerminoCalculada = Uteis.somarDias(programa.getDataInicio(), diasParaSomar);
                    programa.setDataTerminoPrevisto(dataTerminoCalculada);
                }
            } else {
                programa.setTotalAulasPrevistas(12);
                programa.setDiasPorSemana(2);
            }
        } else {
            programa.setNome(base.getNome());
            programa.setProfessorMontou(base.getProfessorMontou());

            if (base.getDataInicio() != null && base.getDataTerminoPrevisto() != null) {
                long duracaoEmDias = (base.getDataTerminoPrevisto().getTime() - base.getDataInicio().getTime()) / (1000 * 60 * 60 * 24);

                programa.setDiasPorSemana(base.getDiasPorSemana());
                programa.setTotalAulasPrevistas(base.getTotalAulasPrevistas());

                int diasParaSomar = (int)duracaoEmDias - 1;

                try {
                    Empresa empresa = empresaService.obterPorIdZW(ctx, programa.getCliente().getEmpresa());
                    Date dataTerminoCalculada = Uteis.somarDias(programa.getDataInicio(), diasParaSomar, empresa.getTimeZoneDefault());
                    programa.setDataTerminoPrevisto(dataTerminoCalculada);
                } catch (Exception e) {
                    Date dataTerminoCalculada = Uteis.somarDias(programa.getDataInicio(), diasParaSomar);
                    programa.setDataTerminoPrevisto(dataTerminoCalculada);

                }
            } else {
                // Fallback para valores originais se não conseguir calcular
                programa.setTotalAulasPrevistas(base.getTotalAulasPrevistas());
                programa.setDiasPorSemana(base.getDiasPorSemana());
            }
        }
        programa.setProfessorCarteira(clienteSintetico.getProfessorSintetico());
        usuario = usuario == null ?
                usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId())
                : usuario;
        if (usuario != null && programa.getProfessorMontou() == null) {
            programa.setProfessorMontou(obterProfessorUsuarioLogado(ctx, usuario, clienteSintetico));
        }
        iniciarAulasPrevistas(ctx, programa);
        return programa;
    }

    public ProgramaTreino criarProximoProgramaTreinoJSON(String ctx, Integer empresa,
                                                         ClienteSintetico clienteSintetico, ProgramaTreino base) throws Exception {
        ProgramaTreino programa = new ProgramaTreino();
        programa.setCliente(clienteSintetico);
        programa.setDataLancamento(Calendario.hoje());
        programa.setDataInicio(base.getDataInicio());
        programa.setDataTerminoPrevisto(base.getDataTerminoPrevisto());
        programa.setDataProximaRevisao(base.getDataTerminoPrevisto());
        programa.setNome(base.getNome());
        programa.setTotalAulasPrevistas(base.getTotalAulasPrevistas());
        programa.setDiasPorSemana(base.getDiasPorSemana());
        programa.setProfessorMontou(base.getProfessorMontou());
        programa.setProfessorCarteira(clienteSintetico.getProfessorSintetico());
        iniciarAulasPrevistas(ctx, programa);
        inserir(ctx, programa, true);
        List<ProgramaTreinoFicha> fichas = obterFichaPorPrograma(ctx, base.getCodigo(), null, true);
        fichas = Ordenacao.ordenarLista(fichas, "codigo");
        for (ProgramaTreinoFicha f : fichas) {
            List<AtividadeFicha> atividadesFicha = fichaService.obterAtividadesFicha(ctx, f.getFicha().getCodigo());
            Ficha ficha = new Ficha(f.getFicha(), atividadesFicha, false);
            gravarFichaSemTratarExcecao(ctx, true,
                    f.getTipoExecucao(),
                    f.getDiaSemana(),
                    programa,
                    ficha,
                    f.getFicha().getCategoria() == null ? null : f.getFicha().getCategoria().getCodigo(),
                    f, true, null, false);
        }

        programa.setProgramaFichas(programaTreinoFichaDao.obterPorProgramaTreino(ctx, programa.getCodigo()));
        atualizarDadoSintetico(ctx, programa, empresa);
        ProgramaTreino finalPt = programa;
        programa.getProgramaFichas().forEach(pF -> {
            if (!isNull(pF.getFicha())) {
                incluirLog(ctx, pF.getFicha().getCodigo().toString(), finalPt.getCodigo().toString(), "", pF.getFicha().getDescricaoParaLog(null), "INCLUSÃO", "INCLUSÃO DE FICHA",
                        EntidadeLogEnum.FICHA, "Ficha", "", logDao, null, null);
            }
        });
        if (programa.getCliente() != null) {
            incluirLog(ctx, programa.getCodigo().toString(), programa.getCliente().getCodigo().toString(), "",
                    programa.getDescricaoParaLog(null), "INCLUSÃO", "INCLUSÃO DE PROGRAMA VIA ENVIAR TREINO AO ALUNO",
                    EntidadeLogEnum.PROGRAMA, "Programa", "RECORRENCIA", logDao, null, null);
        }
        return programa;
    }

    private ProgramaTreino criarProximoProgramaTreinoColaborador(String ctx, final Integer codigoColaborador, ProgramaTreinoTO programaTreinoTO) throws ServiceException {
        List<ProgramaTreino> programas = obterProgramasPorColaborador(ctx, codigoColaborador, null, null, null, null);
        ProgramaTreino programa = new ProgramaTreino();
        programa.setDataLancamento(Calendario.hoje());
        programa.setNome("PROG " + Calendario.getData(Calendario.proximo(Calendar.DAY_OF_MONTH, programas.get(0).getDataTerminoPrevisto()), "ddMMyyHHmm"));
        programa.setDataInicio(Calendario.proximo(Calendar.DAY_OF_MONTH, programas.get(0).getDataTerminoPrevisto()));
        programa.setDataProximaRevisao(Calendario.proximoDeveSerUtil(programa.getDataInicio(), 7));
        programa.setTotalAulasPrevistas(12);
        programa.setDiasPorSemana(2);
        Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
        if (programaTreinoTO != null) {
            programa.setEmRevisaoProfessor(programaTreinoTO.getEmRevisaoProfessor());
            programa.setGeradoPorIA(programaTreinoTO.getGeradoPorIA());
        }
        if (usuario != null) {
            programa.setProfessorMontou(usuario.getProfessor());
        }
        iniciarAulasPrevistas(ctx, programa);
        return programa;
    }

    @Override
    public ProgramaTreinoResponseTO alterarProgramaTreino(Integer empresa,
                                                          ProgramaTreinoTO programaTreinoTO, HttpServletRequest request) throws ServiceException {
        ProgramaTreino programaTreinoAntesAlteracao = null;
        try {
            Date date = new SimpleDateFormat("dd/MM/yyyy").parse("01/01/2000");
            if (programaTreinoTO.getInicio() != null) {
                if (!programaTreinoTO.getPredefinido() && Calendario.maiorOuIgual(date, programaTreinoTO.getInicioComoData())) {
                    throw new ServiceException(ProgramaTreinoExcecoes.ERRO_FORMATO_DATA_INICIO_PROGRAMA_TREINO);
                }
            }
        } catch (ParseException e) {
            throw new ServiceException(e);
        }

        String ctx = sessaoService.getUsuarioAtual().getChave();
        programaTreinoAntesAlteracao = new ProgramaTreino(programatreinoDao.obterPorId(ctx, programaTreinoTO.getId()), true);
        ProgramaTreino pt = montarPrograma(programaTreinoTO, false);
        boolean predefinido = programaTreinoTO.getPredefinido() != null && programaTreinoTO.getPredefinido() ? true : false;
        if (validarExisteProgramaPredefinidaComNome(ctx, programaTreinoTO.getId(), programaTreinoTO.getNome(), predefinido)) {
            throw new ServiceException(ProgramaTreinoExcecoes.VALIDACAO_PROGRAMA_PREDEFINIDO_JA_EXISTE);
        }
        try {
            if (!predefinido && Calendario.maiorOuIgual(new Date(), pt.getDataInicio())
                    && Calendario.maiorOuIgual(pt.getDataTerminoPrevisto(), new Date())
                    && pt.getCliente() != null
            ) {
                cs.alterarAlgunsCampos(
                        ctx,
                        pt.getCliente(),
                        new String[]{"nrTreinosPrevistos"},
                        new Object[]{pt.getTotalAulasPrevistas()});
            }
            pt = programatreinoDao.alterar(ctx, pt);
            String username = sessaoService.getUsuarioAtual().getUsername();
            Usuario usuario = usuarioService.obterPorAtributo(ctx, "username", username);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                pt.manterAntesAlteracao();
                Date dataProximaRevisao = pt.getDataInicio() != null ? Calendario.proximoDeveSerUtil(pt.getDataInicio(), 7) : null;
                gravarHistoricoRevisoes(ctx, pt, "REVISADO", dataProximaRevisao, usuario.getProfessor());
            }
            atualizarDadoSintetico(ctx, pt, empresa);
            atualizarVersaoProgramaSemGerarLog(ctx, pt);
            ProgramaTreinoResponseTO ret = prepararProgramaTreinoResponseTO(ctx, pt);
            if (pt.getCliente() != null) {
                Map<String, String> appInfo = Uteis.obtemNomeEVersaoApp(request);
                String nomeApp = appInfo.get("nomeApp");
                String versaoApp = appInfo.get("versaoApp");

                incluirLog(ctx, programaTreinoAntesAlteracao.getCodigo().toString(), pt.getCliente().getCodigo().toString(), programaTreinoAntesAlteracao.getDescricaoParaLog(pt),
                        pt.getDescricaoParaLog(programaTreinoAntesAlteracao), "ALTERAÇÃO", "ALTERAÇÃO PROGRAMA", EntidadeLogEnum.PROGRAMA, "Programa", sessaoService, logDao, nomeApp, versaoApp);
            }
            return ret;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_ALTERAR_PROGRAMA_TREINO, e);
        } finally {
            if (!predefinido) {
                notificarOuvintes(ctx, pt.getCliente() == null ? null : pt.getCliente().getCodigo(),
                        pt.getCodigoColaborador(),
                        request);
            }
            leaveAcao();
        }
    }

    public void alterarEmRevisaoProfessor(Integer codigoPrograma, Boolean emRevisaoProfessor) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            ProgramaTreino programaTreino = programatreinoDao.obterPorId(ctx, codigoPrograma);
            if (programaTreino == null) {
                throw new ServiceException(ProgramaTreinoExcecoes.PROGRAMA_TREINO_NAO_ENCONTRADO);
            }

            if (programaTreino.getGeradoPorIA()) {
                Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
                if (usuario != null) {
                    ProfessorSintetico professor = professorService.obterPorId(ctx, usuario.getProfessor().getCodigo());
                    if (professor != null) {
                        programaTreino.setProfessorMontou(professor);
                    }
                }
            }

            programaTreino.setEmRevisaoProfessor(emRevisaoProfessor);

            programatreinoDao.alterar(ctx, programaTreino);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_ALTERAR_PROGRAMA_TREINO, e);
        }
    }

    @Override
    public String alterarProgramaTreinoJSON(String ctx, Integer empresa,
                                            ProgramaTreino base) throws ServiceException {
        try {
            ProgramaTreino programaTreinoAntesAlteracao = new ProgramaTreino(programatreinoDao.obterPorId(ctx, base.getCodigo()), true);
            ProgramaTreino pt = programatreinoDao.alterar(ctx, base);
            if (pt.getCliente() != null) {
                incluirLog(ctx, programaTreinoAntesAlteracao.getCodigo().toString(), pt.getCliente().getCodigo().toString(), programaTreinoAntesAlteracao.getDescricaoParaLog(pt),
                        pt.getDescricaoParaLog(programaTreinoAntesAlteracao), "ALTERAÇÃO", "ALTERAÇÃO PROGRAMA", EntidadeLogEnum.PROGRAMA, "Programa", "RECORRENCIA", logDao, null, null);
            }
            return "OK";
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_ALTERAR_PROGRAMA_TREINO, e);
        }
    }

    @Override
    public void tornarProgramaPreDefinido(Integer programaTreinoId) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        ProgramaTreino pt = obterPorId(ctx, programaTreinoId);
        try {
            ProgramaTreino programaDuplicado = getProgramaTreinoDao().findObjectByAttributes(ctx, new String[]{"nome", "preDefinido"}, new Object[]{pt.getNome(), true}, "nome");
            if (programaDuplicado == null) {
                tornarPreDefinido(ctx, pt);
            } else {
                throw new ServiceException(ProgramaTreinoExcecoes.ERRO_PROGRAMA_TREINO_PREDEFINIDO_DUPLICADO);
            }
        } catch (Exception e) {
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_COLOCAR_PROGRAMA_TREINO_COMO_PREDEFINIDO, e);
        }
    }


    @Override
    public void excluirProgramaTreino(Integer programaTreinoId, HttpServletRequest request) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        excluiProgramaTreinoPorId(programaTreinoId, request, ctx, null);

    }

    private void excluiProgramaTreinoPorId(Integer programaTreinoId, HttpServletRequest request, String ctx, String username) throws ServiceException {
        boolean programaPreDefinido = true;
        Integer clienteId = 0;
        Integer colaboradorId = 0;
        String matricula = "";
        try {
            ProgramaTreino pt = obterPorId(ctx, programaTreinoId);
            colaboradorId = pt.getCodigoColaborador();
            if (pt.getCliente() != null) {
                matricula = pt.getCliente().getMatriculaString();
            }
            cs.acao(AcaoAlunoEnum.EXCLUIU_PROGRAMA, matricula);
            programaPreDefinido = pt.getPreDefinido() == null ? false : pt.getPreDefinido();
            if (!programaPreDefinido) {
                clienteId = pt.getCliente().getCodigo();
            }
            ProgramaTreino ptAntesExclusao = UtilReflection.copy(pt);
            boolean registrarLog = !UteisValidacao.emptyString(username);
            excluir(ctx, pt, username, registrarLog);
            Map<String, String> appInfo = Uteis.obtemNomeEVersaoApp(request);
            String nomeApp = appInfo.get("nomeApp");
            String versaoApp = appInfo.get("versaoApp");

            if(!registrarLog) {
                incluirLog(ctx, ptAntesExclusao.getCodigo().toString(), clienteId.toString(), ptAntesExclusao.getDescricaoParaLog(null),
                        "", "EXCLUSÃO", "EXCLUSÃO PROGRAMA", EntidadeLogEnum.PROGRAMA, "Programa", sessaoService, logDao, nomeApp, versaoApp);
            }
        } catch (Exception e) {
            String message = e.getMessage();
            if (message != null && message.contains("treinorealizado")) {
                throw new ServiceException(ProgramaTreinoExcecoes.ERRO_EXCLUIR_PROGRAMA_TREINO_REALIZADO, e);
            } else {
                throw new ServiceException(ProgramaTreinoExcecoes.ERRO_EXCLUIR_PROGRAMA_TREINO, e);
            }
        } finally {
            if (!programaPreDefinido) {
                notificarOuvintes(ctx, clienteId, colaboradorId, request);
            }
            cs.leaveAcao();
        }
    }

    @Override
    public void excluirProgramaTreino(Integer programaTreinoId, String ctx) throws ServiceException {
        excluiProgramaTreinoPorId(programaTreinoId, null, ctx, "Treino por IA");
    }

    private List<Serie> montarSeries(AtividadeFichaEndpointTO atividadeFicha) {
        List<Serie> listSeries = new ArrayList<>();

        if (!UteisValidacao.emptyNumber(atividadeFicha.getSeries())) {
            for (int i = 0; i < atividadeFicha.getSeries(); i++) {
                Serie serie = new Serie();
                serie.setRepeticaoComp(atividadeFicha.getRepeticoes());
                serie.setCargaComp(atividadeFicha.getCarga());
                serie.setCadencia(atividadeFicha.getCadencia());
                serie.setDescanso(atividadeFicha.getDescanso());
                serie.setComplemento(atividadeFicha.getComplemento());
                serie.setVelocidade(atividadeFicha.getVelocidade());
                serie.setDuracao(atividadeFicha.getDuracao());
                serie.setDistancia(atividadeFicha.getDistancia());
                serie.setOrdem(atividadeFicha.getSequencia() == null ? i + 1 : atividadeFicha.getSequencia());
                listSeries.add(serie);
            }
        }

        return listSeries;
    }

    private Serie montarSerie(AtividadeFichaEndpointTO atividadeFichaEndpointTO) throws Exception {
        List<Serie> novasSerie = new ArrayList<>();

        Serie serie = new Serie();
        serie.setRepeticoesVetor(atividadeFichaEndpointTO.getRepeticoes().toString());
        serie.setCargaVetor(atividadeFichaEndpointTO.getCarga().toString());
        serie.setCadencia(atividadeFichaEndpointTO.getCadencia());
        serie.setDescanso(atividadeFichaEndpointTO.getDescanso());
        serie.getDescansoStr();
        serie.setComplemento(atividadeFichaEndpointTO.getComplemento());
        serie.setVelocidade(atividadeFichaEndpointTO.getVelocidade());
        serie.setDuracao(atividadeFichaEndpointTO.getDuracao());
        serie.setDistancia(atividadeFichaEndpointTO.getDistancia());

        return serie;
    }

    @Override
    public AtividadeFichaResponseTO criarAtividadeFicha(AtividadeFichaEndpointTO atividadeFichaTO) throws ServiceException {
        Integer fichaId = null;
        String ctx = null;
        try {
            ctx = sessaoService.getUsuarioAtual().getChave();
            prepare(ctx);
            Atividade atividade = atividadeDao.findById(ctx, atividadeFichaTO.getAtividadeId());
            if (atividade == null) {
                throw new ServiceException(AtividadeFichaExcecoes.ATIVIDADE_NAO_ENCONTRADA);
            }
            Ficha ficha = fichaDao.findById(ctx, atividadeFichaTO.getFichaId());
            if (ficha == null) {
                throw new ServiceException(AtividadeFichaExcecoes.FICHA_NAO_ENCONTRADA);
            }
            AtividadeFicha af = new AtividadeFicha();
            af.setOrdem(atividadeFichaTO.getSequencia());
            af.setAtividade(atividade);
            af.setNome(atividade.getNome());
            af.setIntensidade(atividadeFichaTO.getEsforco());
            af.setMetodoExecucao(atividadeFichaTO.getMetodoExecucao());
            af.setFicha(ficha);
            af.setOrdem(ficha.getAtividades().size());
            if (atividadeFichaTO.getDescanso() == null || atividadeFichaTO.getDescanso() == 0) {
                af.setDescanso(false);
            }
            af = atividadeFichaDao.insert(ctx, af);

            List<Serie> listSeries = new ArrayList<>();
            for (Serie serie : montarSeries(atividadeFichaTO)) {
                serie.setAtividadeFicha(af);

                listSeries.add(serieService.inserir(ctx, serie));
            }
            af.getSeries().addAll(listSeries);
            if (!UteisValidacao.emptyNumber(af.getCodigo())) {
                fichaId = ficha.getCodigo();
            }
            return new AtividadeFichaResponseTO(af);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AtividadeFichaExcecoes.ERRO_INCLUIR_ATIVIDADE_FICHA, e);
        } finally {
            if (!UteisValidacao.emptyNumber(fichaId) && StringUtils.isNotBlank(ctx)) {
                String username = usuarioService.obterUsernameAlunoPorFicha(ctx, fichaId);
                if (StringUtils.isNotBlank(username)) {
                    filaImpressaoService.enfileirar(ctx, TipoObjetoSincronizarEnum.PROGRAMA, username);
                }
            }
        }
    }

    @Override
    public AtividadeFichaResponseTO atualizarAtividadeFicha(AtividadeFichaEndpointTO atividadeFichaTO) throws ServiceException {
        Integer fichaId = null;
        String ctx = null;
        try {
            ctx = sessaoService.getUsuarioAtual().getChave();
            prepare(ctx);
            AtividadeFicha af = atividadeFichaDao.findById(ctx, atividadeFichaTO.getId());
            if (af == null) {
                throw new ServiceException(AtividadeFichaExcecoes.ATIVIDADE_FICHA_NAO_ENCONTRADA);
            }
            fichaId = af.getFicha().getCodigo();
            af.setIntensidade(atividadeFichaTO.getEsforco());
            af.setMetodoExecucao(atividadeFichaTO.getMetodoExecucao());
            if (!UteisValidacao.emptyList(atividadeFichaTO.getSetAtividadesIds()) && (atividadeFichaTO.getMetodoExecucao().equals(MetodoExecucaoEnum.BI_SET)
                    || atividadeFichaTO.getMetodoExecucao().equals(MetodoExecucaoEnum.TRI_SET))) {
                atualizarSet(ctx, af, atividadeFichaTO);
            } else if (!StringUtils.isBlank(af.getSetId())) {
                removendoVinculoComAtividadeFicha(ctx, af, true);
            }

            af = atividadeFichaDao.update(ctx, af);

            List<AtividadeFicha> setAtividades = new ArrayList<>();
            ;
            if (!StringUtils.isBlank(af.getSetId()) && af.getMetodoExecucao() != null &&
                    (af.getMetodoExecucao().equals(MetodoExecucaoEnum.BI_SET) || af.getMetodoExecucao().equals(MetodoExecucaoEnum.TRI_SET))) {
                String[] atividadeFichaIds = af.getSetId().split("\\|");

                for (String atividadeFichaId : atividadeFichaIds) {
                    if (!af.getCodigo().equals(Integer.valueOf(atividadeFichaId))) {
                        AtividadeFicha atividadeFicha = atividadeFichaDao.findById(ctx, Integer.valueOf(atividadeFichaId));
                        setAtividades.add(atividadeFicha);
                    }
                }
            }

            return new AtividadeFichaResponseTO(af, setAtividades);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AtividadeFichaExcecoes.ERRO_ATUALIZAR_ATIVIDADE_FICHA, e);
        } finally {
            if (!UteisValidacao.emptyNumber(fichaId) && StringUtils.isNotBlank(ctx)) {
                String username = usuarioService.obterUsernameAlunoPorFicha(ctx, fichaId);
                if (StringUtils.isNotBlank(username)) {
                    filaImpressaoService.enfileirar(ctx, TipoObjetoSincronizarEnum.PROGRAMA, username);
                }
            }
        }
    }

    private void atualizarSet(String ctx, AtividadeFicha af, AtividadeFichaEndpointTO atividadeFichaTO) throws Exception {
        if (!StringUtils.isBlank(af.getSetId())) {
            verificaAlgumaAtividadeSaiuDoSet(ctx, af, atividadeFichaTO);
            removendoVinculoComAtividadeFicha(ctx, af, false);
        }
        af.setSetId(af.getCodigo() + "|");
        for (Integer setAtividadesId : atividadeFichaTO.getSetAtividadesIds()) {
            if (!setAtividadesId.equals(af.getFicha().getCodigo())) {
                af.setSetId(af.getSetId() + setAtividadesId + "|");
            }
        }
        atualizarSetFilhas(ctx, af);
    }

    private void atualizarSetFilhas(String ctx, AtividadeFicha af) throws Exception {
        String[] atividadesFichaIds = af.getSetId().split("\\|");
        for (String atividadeFichaId : atividadesFichaIds) {
            if (!af.getCodigo().equals(Integer.valueOf(atividadeFichaId))) {
                AtividadeFicha atividadeFicha = atividadeFichaDao.findById(ctx, Integer.valueOf(atividadeFichaId));

                atividadeFicha.setSetId(af.getSetId());
                atividadeFicha.setMetodoExecucao(af.getMetodoExecucao());
                atividadeFichaDao.update(ctx, atividadeFicha);
            }
        }
    }

    private void removendoVinculoComAtividadeFicha(String ctx, AtividadeFicha atv, boolean excluindo) throws Exception {
        String[] atividadesFichaIds = atv.getSetId().split("\\|");

        for (String atividadeFichaId : atividadesFichaIds) {
            if (!atv.getCodigo().equals(Integer.valueOf(atividadeFichaId))) {
                AtividadeFicha atividadeFicha = atividadeFichaDao.findById(ctx, Integer.valueOf(atividadeFichaId));
                if (!StringUtils.isBlank(atividadeFicha.getSetId())) {
                    String[] vinculos = atividadeFicha.getSetId().split("\\|");
                    String newVinculo = "";
                    for (String vinculo : vinculos) {
                        if (!atv.getCodigo().equals(Integer.valueOf(vinculo))) {
                            newVinculo = newVinculo + vinculo + "|";
                        }
                    }
                    atividadeFicha.setSetId(newVinculo);
                    if (excluindo) {
                        if (atividadesFichaIds.length > 2) {
                            atividadeFicha.setMetodoExecucao(MetodoExecucaoEnum.BI_SET);
                        } else {
                            atividadeFicha.setMetodoExecucao(null);
                            atividadeFicha.setSetId("");
                        }
                    }
                    atividadeFichaDao.update(ctx, atividadeFicha);
                }
            }
        }
        if (!StringUtils.isBlank(atv.getSetId())) {
            atv.setSetId("");
            atividadeFichaDao.update(ctx, atv);
        }
    }

    private void verificaAlgumaAtividadeSaiuDoSet(String ctx, AtividadeFicha atividadeArmazenada, AtividadeFichaEndpointTO alteracoesNovaAtividade) throws Exception {
        String[] vinculos = atividadeArmazenada.getSetId().split("\\|");
        for (String vinculo : vinculos) {
            if (!estaDentrodoSet(Integer.parseInt(vinculo), alteracoesNovaAtividade)) {
                AtividadeFicha atividade = atividadeFichaDao.findById(ctx, Integer.parseInt(vinculo));
                atividade.setMetodoExecucao(null);
                atividade.setSetId("");

                atividadeFichaDao.update(ctx, atividade);
            }
        }
    }

    private boolean estaDentrodoSet(Integer atividadeId, AtividadeFichaEndpointTO atividadeFichaEndpointTO) {
        boolean estaDentro = false;
        for (Integer atvId : atividadeFichaEndpointTO.getSetAtividadesIds()) {
            if (atividadeId.equals(atvId) || atividadeId.equals(atividadeFichaEndpointTO.getId())) {
                estaDentro = true;
                break;
            }
        }
        return estaDentro;
    }

    @Override
    public void removerAtividadeFicha(Integer atividadeFichaId) throws ServiceException {
        Integer fichaId = null;
        String ctx = null;
        try {
            ctx = sessaoService.getUsuarioAtual().getChave();
            AtividadeFicha af = atividadeFichaDao.findById(ctx, atividadeFichaId);
            if (af == null) {
                throw new ServiceException(AtividadeFichaExcecoes.ATIVIDADE_FICHA_NAO_ENCONTRADA);
            }
            fichaId = af.getFicha().getCodigo();
            if (!StringUtils.isBlank(af.getSetId())) {
                removendoVinculoComAtividadeFicha(ctx, af, true);
            }

            atividadeFichaDao.delete(ctx, af);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AtividadeFichaExcecoes.ERRO_ATUALIZAR_ATIVIDADE_FICHA, e);
        } finally {
            if (!UteisValidacao.emptyNumber(fichaId) && StringUtils.isNotBlank(ctx)) {
                String username = usuarioService.obterUsernameAlunoPorFicha(ctx, fichaId);
                if (StringUtils.isNotBlank(username)) {
                    filaImpressaoService.enfileirar(ctx, TipoObjetoSincronizarEnum.PROGRAMA, username);
                }
            }
        }
    }

    @Override
    public void padronizarSeries(Integer fichaId, AtividadeFichaEndpointTO atividadeFichaEndpointTO) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            if (atividadeFichaEndpointTO.getAtividadesFichaId() != null && atividadeFichaEndpointTO.getAtividadesFichaId().size() > 0 && !UteisValidacao.emptyNumber(fichaId)) {
                List<AtividadeFicha> atividadesFicha = obterAtividadesFichaPorIds(ctx, atividadeFichaEndpointTO.getAtividadesFichaId());
                List<Integer> atvsSelecionadas = atividadeFichaEndpointTO.getAtividadesFichaId();
                //List<AtividadeFicha>  atividadesFicha = fichaService.obterAtividadesFicha(ctx, fichaId);
                Ficha ficha = fichaService.obterPorId(ctx, fichaId);
                for (AtividadeFicha atv : atividadesFicha) {
                    atv.setEscolhida(atvsSelecionadas.contains(atv.getCodigo()));
                }
                Serie serie = montarSerie(atividadeFichaEndpointTO);

                fichaService.aplicarPadraoSeries(ctx, ficha, atividadesFicha, atividadeFichaEndpointTO.getSeries(), serie, true);

            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AtividadeFichaExcecoes.ERRO_PADRONIZAR_SERIES, e);
        }
    }

    public DetalheTreinoAlunoDTO detalheTreinamentoCliente(Integer alunoId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            DetalheTreinoAlunoDTO detalheTreinoAluno = new DetalheTreinoAlunoDTO();

            ClienteSintetico cs = ((ClienteSinteticoService) UtilContext.getBean(ClienteSinteticoService.class)).obterPorId(ctx, alunoId);
            if (cs == null || UteisValidacao.emptyNumber(cs.getCodigo())) {
                throw new ServiceException(AlunoExcecoes.ERRO_OBTER_ALUNO);
            }
            ProgramaTreino pt = obterProgramaVigente(ctx, cs);
            if (pt == null) {
                pt = consultarUltimoTreinoDataBaseAluno(ctx, cs, null);
            }
            if (pt == null || UteisValidacao.emptyNumber(pt.getCodigo())) {
                throw new ServiceException(ProgramaTreinoExcecoes.ERRO_BUSCAR_PROGRAMA_TREINO);
            }
            detalheTreinoAluno.setNome(pt.getNome());
            detalheTreinoAluno.setTotalTreinoPrevisto(pt.getTotalAulasPrevistas());

            ProgramaTreinoAndamento programaTreinoAndamento = ((ProgramaTreinoAndamentoDao) UtilContext.getBean(ProgramaTreinoAndamentoDao.class))
                    .findObjectByAttributes(ctx, new String[]{"programa.codigo"}, new Object[]{pt.getCodigo()}, "codigo");

            Integer quantidadeExecucoes = obterQuantidadeExecucoesTreinoRealizados(ctx, pt.getCodigo());
            if (programaTreinoAndamento != null && !UteisValidacao.emptyNumber(programaTreinoAndamento.getCodigo())) {
                detalheTreinoAluno.setFrequencia(
                        programaTreinoAndamento.getPercentualExecucoesFrequencia(quantidadeExecucoes, pt.getTotalAulasPrevistas())
                );
                detalheTreinoAluno.setTotalTreinoRealizado(quantidadeExecucoes);
            }

            Date dataFim = Calendario.hoje();
            Calendar dataInicio = Calendar.getInstance();
            dataInicio.setTime(new Date());
            dataInicio.add(Calendar.DATE, -90);

            double quantSegundaEx = 0;
            double quantTercaEx = 0;
            double quantQuartaEx = 0;
            double quantQuintaEx = 0;
            double quantSextaEx = 0;
            double quantSabadoEx = 0;
            double quantDomingoEx = 0;

            Integer codigoProgramaTreinamentoAtual = programaTreinoAndamento != null && !UteisValidacao.emptyNumber(programaTreinoAndamento.getPrograma().getCodigo()) ? programaTreinoAndamento.getPrograma().getCodigo() :
                    pt == null ? null : pt.getCodigo();
            List<TreinoRealizado> treinosRealizados = obterTodosTreinosRealizadoPrograma(ctx, codigoProgramaTreinamentoAtual, dataInicio.getTime(), dataFim, alunoId, null, null);
            if (treinosRealizados.size() > 0) {
                double quantTreinosRealizados = treinosRealizados.size();
                for (TreinoRealizado treino : treinosRealizados) {
                    Calendar dia = Calendar.getInstance();
                    dia.setTime(treino.getDataInicio());
                    PorcentagemDiaSemana porcentagemDiaSemana = new PorcentagemDiaSemana();
                    if (dia.get(Calendar.DAY_OF_WEEK) == DiasSemana.SEGUNDA_FEIRA.getNumeral()) {
                        quantSegundaEx++;
                        porcentagemDiaSemana.setPorcentagemExecutada(quantSegundaEx / quantTreinosRealizados);
                        detalheTreinoAluno.setSegunda(porcentagemDiaSemana);
                        continue;
                    }
                    if (dia.get(Calendar.DAY_OF_WEEK) == DiasSemana.TERCA_FEIRA.getNumeral()) {
                        quantTercaEx++;
                        porcentagemDiaSemana.setPorcentagemExecutada(quantTercaEx / quantTreinosRealizados);
                        detalheTreinoAluno.setTerca(porcentagemDiaSemana);
                        continue;
                    }
                    if (dia.get(Calendar.DAY_OF_WEEK) == DiasSemana.QUARTA_FEIRA.getNumeral()) {
                        quantQuartaEx++;
                        porcentagemDiaSemana.setPorcentagemExecutada(quantQuartaEx / quantTreinosRealizados);
                        detalheTreinoAluno.setQuarta(porcentagemDiaSemana);
                        continue;
                    }
                    if (dia.get(Calendar.DAY_OF_WEEK) == DiasSemana.QUINTA_FEIRA.getNumeral()) {
                        quantQuintaEx++;
                        porcentagemDiaSemana.setPorcentagemExecutada(quantQuintaEx / quantTreinosRealizados);
                        detalheTreinoAluno.setQuinta(porcentagemDiaSemana);
                        continue;
                    }
                    if (dia.get(Calendar.DAY_OF_WEEK) == DiasSemana.SEXTA_FEIRA.getNumeral()) {
                        quantSextaEx++;
                        porcentagemDiaSemana.setPorcentagemExecutada(quantSextaEx / quantTreinosRealizados);
                        detalheTreinoAluno.setSexta(porcentagemDiaSemana);
                        continue;
                    }
                    if (dia.get(Calendar.DAY_OF_WEEK) == DiasSemana.SABADO.getNumeral()) {
                        quantSabadoEx++;
                        porcentagemDiaSemana.setPorcentagemExecutada(quantSabadoEx / quantTreinosRealizados);
                        detalheTreinoAluno.setSabado(porcentagemDiaSemana);
                        continue;
                    }
                    if (dia.get(Calendar.DAY_OF_WEEK) == DiasSemana.DOMINGO.getNumeral()) {
                        quantDomingoEx++;
                        porcentagemDiaSemana.setPorcentagemExecutada(quantDomingoEx / quantTreinosRealizados);
                        detalheTreinoAluno.setDomingo(porcentagemDiaSemana);
                        continue;
                    }
                }
            }

            List<TreinoRealizado> ultimoTreinoRealizado = obterUltimosTreinosRealizadosCliente(ctx, cs.getMatricula(), 1);
            if (!UteisValidacao.emptyList(ultimoTreinoRealizado)) {
                detalheTreinoAluno.setUltimaFichaExecutada(new FichaExecutada(ultimoTreinoRealizado.get(0).getProgramaTreinoFicha().getFicha()));
            }

            Integer fichaId = obterFichaAtual(ctx, pt.getCodigo(), null);
            Ficha fichaAtual = null;
            if (!UteisValidacao.emptyNumber(fichaId)) {
                fichaAtual = fichaService.obterPorId(ctx, fichaId);
                detalheTreinoAluno.setFichaAtual(new FichaExecutada(fichaAtual));
            }

            Integer fichaIdProxima = obterProximaFicha(ctx, pt.getCodigo(), fichaAtual);
            if (!UteisValidacao.emptyNumber(fichaIdProxima)) {
                Ficha fichaProxima = fichaService.obterPorId(ctx, fichaIdProxima);
                detalheTreinoAluno.setFichaProxima(new FichaExecutada(fichaProxima));
            }

            List<Atividade> atividades = new ArrayList<>();
            for (TreinoRealizado treino : treinosRealizados) {
                if (treino.getProgramaTreinoFicha() != null && !UteisValidacao.emptyList(treino.getProgramaTreinoFicha().getFicha().getAtividades())) {
                    for (AtividadeFicha atividadeFicha : treino.getProgramaTreinoFicha().getFicha().getAtividades()) {
                        atividades.add(atividadeFicha.getAtividade());
                    }
                }
            }
            if (!UteisValidacao.emptyList(atividades)) {
                detalheTreinoAluno.setDistribuicao(new ArrayList<DistribuicaoMusculoDTO>());
                detalheTreinoAluno.getDistribuicao().addAll(calculandoDistribuicaoMuscular(atividades));
            }
            return detalheTreinoAluno;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_BUSCAR_PROGRAMA_TREINO, e);
        }
    }

    @Override
    public Integer obterQuantidadeExecucoesTreinoRealizados(final String ctx, final Integer codigoPrograma) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT COUNT(codigo) as quantidadeexecucoes FROM treinorealizado WHERE programatreinoficha_codigo IN  \n");
            sql.append(" (SELECT codigo FROM programatreinoficha WHERE programa_codigo = ");
            sql.append(codigoPrograma);
            sql.append(" ) ");
            try (ResultSet rs = treinoRealizadoDao.createStatement(ctx, sql.toString())) {
                return rs.next() ? rs.getInt("quantidadeexecucoes") : 0;
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public List<DistribuicaoMusculoDTO> calculandoDistribuicaoMuscular(List<Atividade> atividades) {
        List<DistribuicaoMusculoDTO> distribuicao = new ArrayList<>();
        double quantAtividades = atividades.size();

        double quantAbdomenEx = 0;
        double quantAntebracoEx = 0;
        double quantBracoEx = 0;
        double quantCostaEx = 0;
        double quantCoxaDistalEx = 0;
        double quantCoxaMedialEx = 0;
        double quantCoxaProximalEx = 0;
        double quantGluteoEx = 0;
        double quantOmbroEx = 0;
        double quantPanturrilhaEx = 0;
        double quantToraxEx = 0;
        for (Atividade atividade : atividades) {
            if (!UteisValidacao.emptyList(atividade.getGruposMusculares())) {
                for (AtividadeGrupoMuscular grupoMuscular : atividade.getGruposMusculares()) {
                    if (!UteisValidacao.emptyList(grupoMuscular.getGrupoMuscular().getPontosMuscular())) {
                        for (PontosMuscularEnum ponto : grupoMuscular.getGrupoMuscular().getPontosMuscular()) {
                            switch (ponto) {
                                case ABDOMEN:
                                    quantAbdomenEx++;
                                    break;
                                case ANTEBRACO:
                                    quantAntebracoEx++;
                                    break;
                                case BRACO:
                                    quantBracoEx++;
                                    break;
                                case COSTAS:
                                    quantCostaEx++;
                                case COXA_DISTAL:
                                    quantCoxaDistalEx++;
                                    break;
                                case COXA_MEDIAL:
                                    quantCoxaMedialEx++;
                                    break;
                                case COXA_PROXIMAL:
                                    quantCoxaProximalEx++;
                                    break;
                                case GLUTEO:
                                    quantGluteoEx++;
                                    break;
                                case OMBRO:
                                    quantOmbroEx++;
                                    break;
                                case PANTURRILHA:
                                    quantPanturrilhaEx++;
                                    break;
                                case TORAX:
                                    quantToraxEx++;
                                    break;
                            }
                        }
                    }
                }
            }
        }

        DistribuicaoMusculoDTO abdomen = new DistribuicaoMusculoDTO(PontosMuscularEnum.ABDOMEN, quantAbdomenEx, quantAtividades);
        distribuicao.add(abdomen);
        DistribuicaoMusculoDTO antebraco = new DistribuicaoMusculoDTO(PontosMuscularEnum.ANTEBRACO, quantAntebracoEx, quantAtividades);
        distribuicao.add(antebraco);
        DistribuicaoMusculoDTO braco = new DistribuicaoMusculoDTO(PontosMuscularEnum.BRACO, quantBracoEx, quantAtividades);
        distribuicao.add(braco);
        DistribuicaoMusculoDTO costas = new DistribuicaoMusculoDTO(PontosMuscularEnum.COSTAS, quantCostaEx, quantAtividades);
        distribuicao.add(costas);
        DistribuicaoMusculoDTO coxaDistal = new DistribuicaoMusculoDTO(PontosMuscularEnum.COXA_DISTAL, quantCoxaDistalEx, quantAtividades);
        distribuicao.add(coxaDistal);
        DistribuicaoMusculoDTO coxaMedial = new DistribuicaoMusculoDTO(PontosMuscularEnum.COXA_MEDIAL, quantCoxaMedialEx, quantAtividades);
        distribuicao.add(coxaMedial);
        DistribuicaoMusculoDTO coxaProximal = new DistribuicaoMusculoDTO(PontosMuscularEnum.COXA_PROXIMAL, quantCoxaProximalEx, quantAtividades);
        distribuicao.add(coxaProximal);
        DistribuicaoMusculoDTO gluteo = new DistribuicaoMusculoDTO(PontosMuscularEnum.GLUTEO, quantGluteoEx, quantAtividades);
        distribuicao.add(gluteo);
        DistribuicaoMusculoDTO ombro = new DistribuicaoMusculoDTO(PontosMuscularEnum.OMBRO, quantOmbroEx, quantAtividades);
        distribuicao.add(ombro);
        DistribuicaoMusculoDTO panturrilha = new DistribuicaoMusculoDTO(PontosMuscularEnum.PANTURRILHA, quantPanturrilhaEx, quantAtividades);
        distribuicao.add(panturrilha);
        DistribuicaoMusculoDTO torax = new DistribuicaoMusculoDTO(PontosMuscularEnum.TORAX, quantToraxEx, quantAtividades);
        distribuicao.add(torax);

        return distribuicao;
    }

    protected void prepare(final String ctx) throws ServiceException {
        EntityManagerService ems = (EntityManagerService) UtilContext.getBean(EntityManagerService.class);
        try {
            ems.closeQuietly(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private class Filter implements Predicate {
        private final String setid;

        public Filter(String setid) {
            this.setid = setid;
        }

        @Override
        public boolean evaluate(Object object) {
            AtividadeFicha atividadeFicha = (AtividadeFicha) object;
            return null != atividadeFicha.getSetId() && atividadeFicha.getSetId().equalsIgnoreCase(setid);
        }
    }

    public JSONArray obterProgramasPorClienteJson(final String ctx) throws ServiceException {
        JSONArray array = new JSONArray();
        try {
            StringBuilder query = new StringBuilder("SELECT  distinct max(codigo) as codigoPrograma, cliente_codigo as codigoCliente, max(dataterminoprevisto) as dataPrevistaTermino ");
            query.append("from ProgramaTreino ");
            query.append("group by cliente_codigo ");
            query.append("order by cliente_codigo ");

            try (ResultSet rs = getProgramaTreinoDao().createStatement(ctx, query.toString())) {
                List<ProgramaTreino> findAll = new ArrayList<ProgramaTreino>();
                while (rs.next()) {
                    JSONObject obj = new JSONObject();
                    obj.put("codigocliente", rs.getInt("codigoCliente"));
                    obj.put("dataPrevistaTermino", rs.getString("dataPrevistaTermino"));
                    array.put(obj);
                }
            }

        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
        return array;
    }

    public void obterObjetivosDoProgramaApp(String ctx, ProgramaTreino programa) throws ServiceException {
        try {
            programa.setObjetivos(obterObjetivosPrograma(ctx, programa));
            List<ObjetivoPredefinido> objetivos = objetivoService.obterTodos(ctx);

            if (programa.getObjetivosPredef() == null) {
                programa.setObjetivosPredef(new ArrayList<GenericoTO>());
            }
            for (ObjetivoPredefinido objPred : objetivos) {
                GenericoTO generico = new GenericoTO(objPred.getCodigo(), objPred.getNome());
                generico.setEscolhido(false);

                if (!programa.getObjetivosPredef().contains(generico)) {
                    programa.getObjetivosPredef().add(generico);
                }
            }

        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public ProgramaTreino prepararPersistenciaAppJSON(final String ctx, final ProgramaWriteAppJSON pw, Integer origem)
            throws ServiceException {
        ProgramaTreino p = new ProgramaTreino();
        p.setOrigem(origem);
        if (pw.getCodigo() != null && !pw.getCodigo().equals(0)) {
            p = obterPorId(ctx, pw.getCodigo());
        }
        p.setCliente(cs.obterPorId(ctx, pw.getCliente()));
        if (p.getCliente() == null) {
            throw new ServiceException("Cliente invalido");
        }
        p.setDataInicio(new Date(pw.getDataInicio()));
        p.setDiasPorSemana(pw.getDiasPorSemana());
        p.setTotalAulasPrevistas(pw.getTotalAulasPrevistas());
        p.setDataLancamento(new Date(pw.getDataLancamento()));
        p.setDataProximaRevisao(new Date(pw.getDataProximaRevisao()));
        if (pw.getDataRenovacao() != null) {
            p.setDataRenovacao(new Date(pw.getDataRenovacao()));
        }
        p.setDataTerminoPrevisto(new Date(pw.getDataTerminoPrevisto()));
        p.setNome(pw.getNome());
        //professores
        p.setProfessorCarteira(professorService.obterPorId(ctx, pw.getProfessorCargeira()));
        p.setProfessorMontou(professorService.obterPorId(ctx, pw.getProfessorMontou()));
        if (p.getProfessorMontou() == null) {
            throw new ServiceException("Professor que montou o programa invalido");
        }
        //objetivos programa
        List<ObjetivoProgramaJSON> objsJSON = pw.getObjetivosPrograma();
        if (objsJSON != null) {
            obterRestricoesPrograma(ctx, p);
            obterObjetivosDoProgramaApp(ctx, p);
            for (ObjetivoProgramaJSON ob : objsJSON) {
                try {
                    final Integer codObjetivo = ob.getObjetivo();
                    GenericoTO objPredef = null;
                    if (codObjetivo != null && codObjetivo > 0) {
                        objPredef = (GenericoTO) ColecaoUtils.find(p.getObjetivosPredef(), new Predicate() {
                            @Override
                            public boolean evaluate(Object o) {
                                GenericoTO gen = (GenericoTO) o;
                                return gen.getCodigo().equals(codObjetivo);
                            }
                        });
                    }
                    if (objPredef != null) {
                        objPredef.setEscolhido(true);
                    }
                } catch (Exception ex) {
                    Logger.getLogger(ProgramaTreinoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
                    throw new ServiceException(ex);
                }
            }
        }
        p.setProgramaTreinoRenovacao(pw.getProgramaTreinoRenovacao());
        p.setProgramaTreinoRenovado(pw.getProgramaTreinoRenovado());
        p.setSituacao(ProgramaSituacaoEnum.valueOf(pw.getSituacao()));
        validacao.validarInsercao(p, ctx, true);
        return p;
    }

    @Override
    public Integer obterNumeroAtividadesRealizadas(final String ctx, final Integer codTreinoRealizado)
            throws ServiceException {
        try {
            StringBuilder query = new StringBuilder("select count(distinct sr.atividadeficha_codigo) ");
            query.append("from serierealizada sr where sr.treinorealizado_codigo = ");
            query.append(codTreinoRealizado);

            try (ResultSet rs = getSerieRealizadaDao().createStatement(ctx, query.toString())) {
                if (rs.next()) {
                    return rs.getInt("count");
                }
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
        return null;
    }

    @Override
    public void atualizarProfessorCarteiraProgramaVirgente(String ctx, String matricula) throws ServiceException {
        try {
            ClienteSintetico cliente = clienteSinteticoService.consultarPorMatricula(ctx, matricula);
            if (cliente != null) {
                ProgramaTreino pt = obterProgramaVigente(ctx, cliente);
                if (pt != null) {
                    pt.setProfessorCarteira(cliente.getProfessorSintetico());
                    programatreinoDao.update(ctx, pt);
                }
            }
        } catch (Exception e) {
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_ALTERAR_PROGRAMA_TREINO, e);
        }
    }

    @Override
    public void atualizarSituacaoProgramaPredefinido(Integer id, Integer situacao, HttpServletRequest request) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            programatreinoDao.atualizarSituacaoProgramaPredefinido(ctx, id, situacao);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_ALTERAR_PROGRAMA_TREINO, e);
        }
    }

    public ProgramaTreinoResponseTO criarProgramaPreDefinido(ProgramaTreinoTO programaTreinoTO, HttpServletRequest request) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        if (validarExisteProgramaPredefinidaComNome(ctx, null, programaTreinoTO.getNome(), true)) {
            throw new ServiceException(ProgramaTreinoExcecoes.VALIDACAO_PROGRAMA_PREDEFINIDO_JA_EXISTE);
        }
        ProgramaTreino pt = new ProgramaTreino();
        try {
            pt = montarProgramaPredefinido(programaTreinoTO, true, ctx);
            pt = inserir(ctx, pt, false);
            fichaService.criarFicha(null, pt.getCodigo(), request, null, null);

            pt.setProgramaFichas(programaTreinoFichaDao.obterPorProgramaTreino(ctx, pt.getCodigo()));
            return prepararProgramaTreinoResponseTO(ctx, pt);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_INCLUIR_PROGRAMA_TREINO, e);
        }
    }

    public boolean validarExisteProgramaPredefinidaComNome(String ctx, Integer codigo, String nome, boolean isPredefinido) throws ServiceException {
        try {
            if (isPredefinido) {
                StringBuilder where = new StringBuilder();
                where.append(" upper(nome) = '").append(nome.toUpperCase()).append("' ");
                where.append(" and predefinido ");
                if (!UteisValidacao.emptyNumber(codigo)) {
                    where.append(" and codigo <> ").append(codigo);
                }
                if (isPredefinido && programatreinoDao.existsWithParam(ctx, where)) {
                    return true;
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, ProgramaTreinoServiceImpl.class);
        }
        return false;
    }

    @Override
    public String importarProgramaPreDefinido(String ctxOrigem, String ctxDestino, HttpServletRequest request) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        if (UteisValidacao.emptyString(ctxDestino) || UteisValidacao.emptyString(ctxOrigem)) {
            return "ERRO: Uma das chaves não foram informadas, processo finalizado!";
        }
        if (!ctx.equals(ctxDestino)) {
            return "ERRO: A chave destino informada não é a mesma que está autenticada, processo finalizado!";
        }
        try {
            Integer countProgTotal = 0;
            Integer countProgExistentes = 0;
            Integer countProgImportSucesso = 0;
            Integer countProgImportErro = 0;
            JSONArray listProgramas = listaProgramasPredefinidosCtxOrigem(ctxOrigem);
            if (listProgramas != null && listProgramas.length() > 0) {
                countProgTotal = listProgramas.length();
                for (Object obj : listProgramas) {
                    try {
                        JSONObject json = (JSONObject) obj;
                        if (!existeProgramaPorNome(ctx, json.optString("nome"))) {
                            // PREPARAR ProgramaTreinoTO
                            Usuario usuario = usuarioService.consultarPorUserName(ctx, sessaoService.getUsuarioAtual().getUsername());
                            ProgramaTreinoTO ptTO = new ProgramaTreinoTO();
                            ptTO.setNome(json.optString("nome"));
                            ptTO.setTotalTreinos(json.optInt("totalTreinos"));
                            ptTO.setQtdDiasSemana(json.optInt("qtdDiasSemana"));
                            ptTO.setProfessorId(usuario.getProfessor().getCodigo());
                            ptTO.setGenero(null);

                            // CRIAR PROGRAMA
                            ProgramaTreino pt = montarProgramaPredefinido(ptTO, true, ctx);
                            pt = inserir(ctx, pt, false);
                            pt.setProgramaFichas(programaTreinoFichaDao.obterPorProgramaTreino(ctx, pt.getCodigo()));

                            // CRIAR FICHA
                            for (Object objF : json.optJSONArray("fichas")) {
                                JSONObject jsonFicha = (JSONObject) objF;
                                FichaResponseTO fichaTO = fichaService.criarFicha(null, pt.getCodigo(), request, null, jsonFicha.optString("nome"));
                                FichaDTO fichaDTO = new FichaDTO();
                                fichaDTO.setId(fichaTO.getId());
                                fichaDTO.setProgramaId(pt.getCodigo());
                                fichaDTO.setNome(jsonFicha.optString("nome"));
                                fichaDTO.setCategoriaId(jsonFicha.optJSONObject("categoria").optInt("id"));
                                fichaDTO.setTipo_execucao(jsonFicha.optString("tipo_execucao").equals("ALTERNADO") ? TipoExecucaoEnum.ALTERNADO : TipoExecucaoEnum.DIAS_SEMANA);
                                List<String> diasSemana = new ArrayList<>();
                                for (Object objDias : jsonFicha.optJSONArray("dias_semana")) {
                                    diasSemana.add(objDias.toString());
                                }
                                fichaDTO.setDias_semana(diasSemana);
                                fichaDTO.setMensagem(jsonFicha.optString("mensagem"));
                                List<AtividadeFichaDTO> atividadeFichaDTOS = new ArrayList<>();

                                // ATIVIDADES FICHA
                                int countSequencia = 1;
                                for (Object objAtv : jsonFicha.optJSONArray("atividades")) {
                                    JSONObject jsonAtv = (JSONObject) objAtv;
                                    Atividade atividade = obterAtividadeCtxDestino(ctx, jsonAtv.optJSONObject("atividade"));
                                    AtividadeFichaDTO atvFichaDTO = new AtividadeFichaDTO();
                                    atvFichaDTO.setSequencia(countSequencia);
                                    atvFichaDTO.setAtividadeId(atividade.getCodigo());
                                    atvFichaDTO.setEsforco(jsonAtv.optInt("esforco"));
                                    atvFichaDTO.setMetodoExecucao(jsonAtv.optString("metodoExecucao"));
                                    atvFichaDTO.setFichaId(null);

                                    // PREPARAR SERIES
                                    List<SerieEndpointTO> listSeries = new ArrayList<>();
                                    for (Object objSeries : jsonAtv.optJSONArray("series")) {
                                        JSONObject jsonSeries = (JSONObject) objSeries;
                                        SerieEndpointTO serieEndpointTO = new SerieEndpointTO();
                                        serieEndpointTO.setSequencia(jsonSeries.optInt("sequencia"));
                                        serieEndpointTO.setRepeticoes(jsonSeries.optString("repeticoes"));
                                        serieEndpointTO.setRepeticaoComp(jsonSeries.optInt("repeticaoComp"));
                                        serieEndpointTO.setVelocidade(jsonSeries.optDouble("velocidade"));
                                        serieEndpointTO.setCarga(jsonSeries.optString("carga"));
                                        serieEndpointTO.setDescanso(jsonSeries.optInt("descanso"));
                                        serieEndpointTO.setComplemento(jsonSeries.optString("complemento"));
                                        serieEndpointTO.setSerieRealizada(jsonSeries.optBoolean("serieRealizada"));
                                        serieEndpointTO.setCargaComp(jsonSeries.optDouble("cargaComp"));
                                        serieEndpointTO.setDistancia(jsonSeries.optInt("distancia"));
                                        serieEndpointTO.setDuracao(jsonSeries.optInt("duracao"));
                                        listSeries.add(serieEndpointTO);
                                    }
                                    atvFichaDTO.setSeries(listSeries);
                                    atividadeFichaDTOS.add(atvFichaDTO);
                                    countSequencia++;
                                }
                                fichaDTO.setAtividades(atividadeFichaDTOS);
                                fichaDTO.setAtivo(true);
                                fichaDTO.setPredefinida(false);
                                fichaService.editarFicha(fichaDTO, request);
                            }
                            countProgImportSucesso++;
                        } else {
                            countProgExistentes++;
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        countProgImportErro++;
                    }
                }
            }
            return "Existem " + countProgTotal + " programas predefinidos na chave de origem. Na chave destino já existiam " + countProgExistentes +
                    " com o mesmo nome e não foram importados. " + countProgImportSucesso + " programas foram importados com sucesso, e " +
                    countProgImportErro + " tiveram erro durante a importação";
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    private Atividade obterAtividadeCtxDestino(String ctx, JSONObject jsonAtv) throws Exception {
        try (ResultSet rs = atividadeDao.createStatement(ctx, "select codigo from atividade where nome ilike '" + jsonAtv.getString("nome") + "'")) {
            if (rs.next()) {
                return atividadeService.obterPorId(ctx, rs.getInt("codigo"));
            } else {
                Atividade atividade = new Atividade();
                atividade.setNome(jsonAtv.optString("nome"));
                atividade.setSeriesApenasDuracao(jsonAtv.optBoolean("serieApenasDuracao"));
                atividade.setAtivo(true);
                atividade.setTipo(jsonAtv.optString("tipo").toUpperCase().equals("NEUROMUSCULAR") ? TipoAtividadeEnum.ANAEROBICO : TipoAtividadeEnum.AEROBICO);
                return atividadeService.inserir(ctx, atividade);
            }
        }
    }

    private boolean existeProgramaPorNome(String ctx, String nome) throws Exception {
        StringBuilder where = new StringBuilder();
        where.append(" predefinido and  nome ilike '").append(nome).append("'");
        return programatreinoDao.existsWithParam(ctx, where);
    }

    private JSONArray listaProgramasPredefinidosCtxOrigem(String ctxOrigem) throws Exception {
        try {
            JSONObject result = Uteis.getJSON(Aplicacao.getProp(Aplicacao.discoveryUrls) + "/find/" + ctxOrigem);
            String treinoUrl = result.getJSONObject("content").getJSONObject("serviceUrls").getString("treinoUrl");
            CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpGet httpGet = new HttpGet(treinoUrl + "/prest/programa/listar-predefinidos/" + ctxOrigem);
            CloseableHttpResponse response = client.execute(httpGet);
            ResponseHandler<String> handler = new BasicResponseHandler();
            String body = handler.handleResponse(response);
            JSONObject json = new JSONObject(body);
            JSONArray jArray = new JSONArray();
            if (json.has("content")) {
                jArray = json.getJSONArray("content");
            }
            return jArray;
        } catch (ServiceException e) {
            throw new ServiceException("nao foi possivel obter os programas predefinidos da chave de origem");
        }
    }

    @Override
    public List<ProgramaTreinoResponseTO> obterProgramasPreDefinidosSlim(Boolean rede, String nome,
                                                                         String chaveRede,
                                                                         Boolean consultaFranqueadora) throws ServiceException {
        try {
            String ctx = consultaFranqueadora ? chaveRede :
                    sessaoService.getUsuarioAtual().getChave();
            List<ProgramaTreinoResponseTO> ret = new ArrayList<>();
            try (ResultSet rs = programatreinoDao.createStatement(ctx, "select codigo, nome from programatreino p " +
                    " where predefinido and situacao = 0 " +
                    (UteisValidacao.emptyString(nome) ? "" : (" and nome ilike '%" + nome + "%'")) +
                    " order by nome limit 50")) {
                while (rs.next()) {
                    ProgramaTreinoResponseTO prog = new ProgramaTreinoResponseTO();
                    prog.setId(rs.getInt("codigo"));
                    prog.setNome(rs.getString("nome"));
                    try (ResultSet rsFichas = programatreinoDao.createStatement(ctx, "select f.codigo, f.nome from ficha f\n" +
                            " inner join programatreinoficha p on p.ficha_codigo  = f.codigo\n" +
                            " and p.programa_codigo  = " + prog.getId())) {
                        while (rsFichas.next()) {
                            prog.getFichas().add(new FichaResponseTO(rsFichas.getInt("codigo"), rsFichas.getString("nome")));
                        }
                    }
                    ret.add(prog);
                }
            }
            if (rede && chaveRede != null && !chaveRede.equals(ctx) && !consultaFranqueadora) {
                ret.addAll(obterDaFranqueadora(nome, chaveRede));
                ret = Ordenacao.ordenarLista(ret, "nome");
            }
            return ret;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_BUSCAR_PROGRAMAS_TREINO_PREDEFINIDOS, e);
        }
    }

    private List<ProgramaTreinoResponseTO> obterDaFranqueadora(String nome, String chaveRede) {
        List<ProgramaTreinoResponseTO> ret = new ArrayList<>();
        try {
            JSONObject result = Uteis.getJSON(Aplicacao.getProp(Aplicacao.discoveryUrls) + "/find/" + chaveRede);
            String treinoUrl = result.getJSONObject("content").getJSONObject("serviceUrls").getString("treinoUrl");

            HttpGet httpGet = new HttpGet(treinoUrl + "/prest/programa/pre-definido/franqueadora");
            URI uri = new URIBuilder(httpGet.getURI())
                    .addParameter("nome", nome)
                    .addParameter("chaveRede", chaveRede)
                    .build();
            httpGet.setURI(uri);
            httpGet.setHeader("Content-Type", "application/json");
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpGet);
            String responseBody = EntityUtils.toString(response.getEntity());
            JSONObject json = new JSONObject(responseBody);
            if (json.has("content")) {
                JSONArray content = json.getJSONArray("content");
                ret = JSONMapper.getList(content, ProgramaTreinoResponseTO.class);
            }
            for (ProgramaTreinoResponseTO progTO : ret) {
                progTO.setChaveOrigem(chaveRede);
            }
        } catch (Exception e) {
            Uteis.logar(e, ProgramaTreinoServiceImpl.class);
        }
        return ret;
    }

    @Override
    public AtividadeFichaResponseTO addAtividadeFicha(Integer fichaId, Integer atividadeId) throws ServiceException {
        String ctx = null;
        try {
            ctx = sessaoService.getUsuarioAtual().getChave();
            prepare(ctx);
            Atividade atividade = atividadeDao.findById(ctx, atividadeId);
            if (atividade == null) {
                throw new ServiceException(AtividadeFichaExcecoes.ATIVIDADE_NAO_ENCONTRADA);
            }
            Ficha ficha = fichaDao.findById(ctx, fichaId);
            if (ficha == null) {
                throw new ServiceException(AtividadeFichaExcecoes.FICHA_NAO_ENCONTRADA);
            }
            Integer maiorOrdem = 0;
            if (ficha.getAtividades() != null && ficha.getAtividades().size() > 0) {
                Ordenacao.ordenarLista(ficha.getAtividades(), "ordem");
                maiorOrdem = ficha.getAtividades().get(ficha.getAtividades().size() - 1).getOrdem();
            }
            AtividadeFicha atividadeFicha = new AtividadeFicha(atividade);
            atividadeFicha.initAjustes();
            atividadeFicha.setFicha(ficha);
            atividadeFicha.setSeries(new ArrayList<>());
            //Adicionar uma Série vazia
            Serie s = new Serie(1, atividadeFicha);
            s.setRepeticao(ficha.getQtdRepeticoesSeriesInserir());
            s.setDescansoStr(ficha.getDescansoRepetir());
            atividadeFicha.getSeries().add(s);
            atividadeFicha.setOrdem(maiorOrdem == null ? 0 : maiorOrdem + 1);
            atividadeFicha = atividadeFichaDao.insert(ctx, atividadeFicha);
            return new AtividadeFichaResponseTO(atividadeFicha);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AtividadeFichaExcecoes.ERRO_INCLUIR_ATIVIDADE_FICHA, e);
        } finally {
            if (!UteisValidacao.emptyNumber(fichaId) && StringUtils.isNotBlank(ctx)) {
                String username = usuarioService.obterUsernameAlunoPorFicha(ctx, fichaId);
                if (StringUtils.isNotBlank(username)) {
                    filaImpressaoService.enfileirar(ctx, TipoObjetoSincronizarEnum.PROGRAMA, username);
                }
            }
        }
    }

    @Override
    public ClienteAcompanhamentoResponseTO consultarAcompanhamento(Integer codigoPessoa, HttpServletRequest request) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ClienteSintetico cliente = clienteSinteticoService.obterPorId(ctx, codigoPessoa);
            ProgramaTreino programaTreino = obterProgramaVigente(ctx, cliente);
            AlunoResponseTO responseTO = new AlunoResponseTO();

            ClienteAcompanhamentoResponseTO clienteAcompanhamento = new ClienteAcompanhamentoResponseTO();
            if (programaTreino != null) {
                ProgramaTreinoResponseTO programaReturn = prepararProgramaTreinoResponseTO(ctx, programaTreino);

                List<FichaResponseTO> lista = new ArrayList<>();
                for (FichaResponseTO ficha : programaReturn.getFichas()) {
                    List<AtividadeFichaResponseTO> atividades = new ArrayList<>();
                    List<AtividadeFichaResponseTO> atividadesOrenadas = ficha.getAtividades();
                    atividadesOrenadas.sort(Comparator.comparing(AtividadeFichaResponseTO::getSequencia));
                    for (AtividadeFichaResponseTO atividadeResponse : atividadesOrenadas) {
                        atividades.add(obterVinculosAtividadeFicha(ctx, atividadeResponse));
                    }
                    ficha.setAtividades(atividades);
                    lista.add(ficha);
                }
                try {
                    responseTO.setImageUri(Aplicacao.obterUrlFotoDaNuvem(cliente.getUrlFoto()));
                } catch (Exception e) {
                    Uteis.logar(e, ClienteSinteticoServiceImpl.class);
                }
                if (responseTO.getImageUri() != null) {
                    clienteAcompanhamento.setImagemUri(responseTO.getImageUri());
                }
                clienteAcompanhamento.setFichas(lista);
                removerRelacionamentosNaoNecessariosRetorno(programaTreino);
                clienteAcompanhamento.setPrograma(programaTreino);
                if (cliente.getNome() != null) {
                    clienteAcompanhamento.setNomeAluno(cliente.getNome());
                }
                if (cliente.getNivelAluno() != null && cliente.getNivelAluno().getNome() != null) {
                    clienteAcompanhamento.setNivelAluno(cliente.getNivelAluno().getNome());
                }
            }
            return clienteAcompanhamento;
        } catch (Exception ex) {
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_BUSCAR_ALUNO_ACOMPANHAMENTO, ex);
        }
    }

    private void removerRelacionamentosNaoNecessariosRetorno(ProgramaTreino programaTreino) {
        try {
            Pessoa pessoa = new Pessoa();
            if (programaTreino.getCliente() != null) {
                programaTreino.getCliente().setPessoa(pessoa);
                programaTreino.getCliente().getProfessorSintetico().setPessoa(pessoa);
            }
            if (programaTreino.getProfessorCarteira() != null) {
                programaTreino.getProfessorCarteira().setPessoa(pessoa);
            }
            if (programaTreino.getProfessorMontou() != null) {
                programaTreino.getProfessorMontou().setPessoa(pessoa);
            }
        } catch (Exception e) {
            Uteis.logar(e, ProgramaTreinoServiceImpl.class);
        }
    }

    public void acao(final ProgramaTreino programa, TipoRevisaoEnum tipo) {
        try {
            AuditUtilities.putAcaoFromCurrentThread(Thread.currentThread().getId(), (programa.getPreDefinido() != null && programa.getPreDefinido() ? "prpr" : "pral") +
                    "_" + programa.getNome().replaceAll("\\_", " ") +
                    "_" + (programa.getCliente() == null ? "-" : programa.getCliente().getNome().replaceAll("\\_", " ")) +
                    "_t" + tipo.getId() +
                    "_c" + (programa.getCliente() == null ? "0" : programa.getCliente().getCodigo()) +
                    "_" + (programa.getCodigo() == null ? "0" : programa.getCodigo()));
        } catch (Exception e) {
            Uteis.logar(e, ProgramaTreinoServiceImpl.class);
        }
    }

    public void leaveAcao() {
        try {
            AuditUtilities.leaveAcaoFromCurrentThread();
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
        }
    }

    @Override
    public List<ProgramaTreinoAlunoResponseDTO> obterProgramasAluno(FiltroProgramaTreinoJSON filtros,
                                                                    PaginadorDTO paginadorDTO,
                                                                    Integer matricula) throws ServiceException {
        List<ProgramaTreinoAlunoResponseDTO> programas = new ArrayList<>();
        String ctx = sessaoService.getUsuarioAtual().getChave();
        ClienteSintetico cs = clienteSinteticoService.consultarSimplesPorMatricula(ctx, matricula);

        List<ProgramaTreino> programasAluno = programatreinoDao.obterProgramasAluno(filtros, paginadorDTO, ctx, cs);

        programasAluno.forEach(item -> {
            try {
                ProgramaTreinoAndamento pta = ((ProgramaTreinoAndamentoDao) UtilContext.getBean(ProgramaTreinoAndamentoDao.class))
                        .findObjectByAttributes(ctx, new String[]{"programa.codigo"}, new Object[]{item.getCodigo()}, "codigo");
                Integer quantidadeExecucoes = obterQuantidadeExecucoesTreinoRealizados(ctx, item.getCodigo());

                ProgramaTreinoAlunoResponseDTO prog = new ProgramaTreinoAlunoResponseDTO();
                prog.setId(item.getCodigo());
                prog.setNome(item.getNome());
                prog.setInicio(item.getDataInicio());
                prog.setTermino(item.getDataTerminoPrevisto());
                prog.setQtFichas(obterQuantidadeFichasPorPrograma(ctx, item.getCodigo()));
                prog.setRevisadoProfessor(item.getEmRevisaoProfessor());
                prog.setGeradoPorIa(item.getGeradoPorIA());
                if (pta != null && !UteisValidacao.emptyNumber(pta.getCodigo())) {
                    quantidadeExecucoes = quantidadeExecucoes != 0 ? quantidadeExecucoes : pta.getNrTreinos();
                    prog.setPorcentagemCompleto(
                            pta.getPercentualExecucoesFrequencia(quantidadeExecucoes, item.getTotalAulasPrevistas()) + "%"
                    );
                } else {
                    prog.setPorcentagemCompleto("0.0%");
                }
                programas.add(prog);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        return programas;
    }

    @Override
    public Integer obterQuantidadeFichasPorPrograma(String ctx, Integer codPrograma) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(ptf.codigo) as qtFichas FROM programatreinoficha ptf  WHERE ptf.programa_codigo = ").append(codPrograma);

        Integer qtFichas = 0;
        try (ResultSet rs = programatreinoDao.createStatement(ctx, sql.toString())) {
            if (rs.next()) {
                qtFichas = rs.getInt("qtFichas");
            }
        }

        return qtFichas;
    }

    @Override
    public Integer obterQuantidadeExecucoesFichaAluno(String ctx, Integer codFicha) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(tr) as qtexecucoesficha \n");
        sql.append("FROM treinorealizado tr \n");
        sql.append("INNER JOIN programatreinoficha ptf ON ptf.codigo = tr.programatreinoficha_codigo \n");
        sql.append("WHERE ptf.ficha_codigo = ").append(codFicha);

        Integer qtExecucoesFicha = 0;
        try (ResultSet rs = treinoRealizadoDao.createStatement(ctx, sql.toString())) {
            if (rs.next()) {
                qtExecucoesFicha = rs.getInt("qtexecucoesficha");
            }
        }

        return qtExecucoesFicha;
    }

    @Override
    public HorariosQueTreinouProgramaAtual obterHorariosQueTreinouProgramaAtual(String ctx, Integer matricula) throws ServiceException {
        HorariosQueTreinouProgramaAtual horariosTreinos = new HorariosQueTreinouProgramaAtual();
        ClienteSintetico cs = clienteSinteticoService.consultarPorMatricula(ctx, matricula.toString());
        ProgramaTreino pt = obterProgramaVigente(ctx, cs);

        if (pt == null) {
            return horariosTreinos;
        }

        Map<String, Object> params = new HashMap<String, Object>();
        StringBuilder where = new StringBuilder();
        where.append("WHERE obj.programaTreinoFicha.programa.codigo = :codigoPrograma \n");
        params.put("codigoPrograma", pt.getCodigo());

        try {
            List<TreinoRealizado> listTreinos = getTreinoRealizadoDao().findByParam(ctx, where, params);
            Integer manha = 0;
            Integer tarde = 0;
            Integer noite = 0;
            for (TreinoRealizado tr : listTreinos) {
                if (tr.getDataInicio().getHours() >= 0 && tr.getDataInicio().getHours() < 12) {
                    manha++;
                } else if (tr.getDataInicio().getHours() >= 12 && tr.getDataInicio().getHours() < 18) {
                    tarde++;
                } else {
                    noite++;
                }
            }
            horariosTreinos.setManha(manha);
            horariosTreinos.setTarde(tarde);
            horariosTreinos.setNoite(noite);
        } catch (Exception e) {
            throw new ServiceException("Ocorreu um erro ao pesquisar os treinos realizados", e);
        }

        return horariosTreinos;
    }

    public EvolucaoFisicaDTO gruposMuscularesTrabalhadosPeriodo(String ctx, String dataInicial, String dataFinal, Integer codigoCliente) throws Exception {
        List<Map<String, Object>> grupos = new ArrayList<>();
        DateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
        EvolucaoFisicaDTO dto = new EvolucaoFisicaDTO();

        try {
            try (ResultSet rs = treinoRealizadoDao.createStatement(ctx, "select codigo from clientesintetico" +
                    " where codigocliente = " + codigoCliente)) {
                if (rs.next()) {
                    Date localDateInicial = formatter.parse(dataInicial);
                    Date localDateFinal = formatter.parse(dataFinal);

                    EvolucaoGrupoTrabalhadoDTO evolucaoGrupoTrabalhadoDTO = new EvolucaoGrupoTrabalhadoDTO();
                    grupos = treinoRealizadoDao.getGruposMuscularesApp(ctx, localDateInicial, localDateFinal, rs.getInt("codigo"));
                    evolucaoGrupoTrabalhadoDTO.setDurantePeriodo(preencherGruposMusculares(grupos));

                    try (ResultSet rsTreinoVigente = treinoRealizadoDao.createStatement(ctx, "select codigo from programatreino p\n" +
                            " where cliente_codigo = " + rs.getInt("codigo") +
                            " and p.datainicio < '" + Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd") +
                            "' order by p.datainicio desc limit 1 ")) {
                        if (rsTreinoVigente.next()) {
                            List<Map<String, Object>> gruposPrograma = treinoRealizadoDao.getGruposPrograma(ctx, rsTreinoVigente.getInt("codigo"));
                            evolucaoGrupoTrabalhadoDTO.setProgramaAtual(preencherGruposMusculares(gruposPrograma));
                        } else {
                            evolucaoGrupoTrabalhadoDTO.setProgramaAtual(new ArrayList<>());
                        }
                    }
                    dto.setGruposTrabalhados(evolucaoGrupoTrabalhadoDTO);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dto;
    }

    @Override
    public boolean temProgramaVigenteByMatricula(String chave, String matricula) throws ServiceException {
        StringBuilder query = new StringBuilder("select obj from ProgramaTreino obj join clientesintetico c on obj.cliente_codigo = c.codigo where c.matricula = ").
                append(matricula).append("\n");
        query.append("and ('").append(Calendario.getData(Calendario.hoje(), "yyyy-MM-dd HH:mm:ss")).append("'");
        query.append(" between ").append("dataInicio and dataTerminoPrevisto");
        query.append(" or cast(dataTerminoPrevisto as date) = '").append(
                Calendario.getData(Calendario.getDataComHoraZerada(Calendario.hoje()), "yyyy-MM-dd")).append("') ");
        try (ResultSet rs = getProgramaTreinoDao().createStatement(chave, query.toString());) {
            if (rs.next()) {
                return true;
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
        return false;
    }

    @Override
    public List<AulaAlunoDTO> consultarAulasAgendadasPorAluno(Integer matricula, String dataInicio, String dataFim, String ctx, Integer contrato) throws Exception {

        List<AulaAlunoDTO> aulaAluno = new ArrayList<>();
        Date dataComHoraZerada = Uteis.getDataComHoraZerada(Calendario.getDate(Calendario.MASC_DATA, dataInicio));
        Date dataHora2359 = Uteis.getDataHora2359(Calendario.getDate(Calendario.MASC_DATA, dataFim));

        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            boolean filtrarPorContrato = !UteisValidacao.emptyNumber(contrato);
            List<Integer> modalidadeFiltrar = new ArrayList<>();
            if (filtrarPorContrato) {
                AgendaModoBDServiceImpl agendaAulasService = new AgendaModoBDServiceImpl(conZW, ctx);
                modalidadeFiltrar = agendaAulasService.modalidadesContrato(ctx, contrato, matricula);
            }

            aulaAluno.addAll(consultaMapaTurmaPorPeriodoEMatricula(matricula, dataComHoraZerada, dataHora2359, conZW, filtrarPorContrato, modalidadeFiltrar));

            aulaAluno.addAll(consultarTurmasPorPeriodoEMatricula(matricula, dataComHoraZerada, dataHora2359, conZW));

            aulaAluno.addAll(consultaReposicoesPorPeriodoEmatricula(matricula, dataComHoraZerada, dataHora2359, conZW, filtrarPorContrato, modalidadeFiltrar));

            consultaAulasDesmarcadasPorPeriodoEMatricula(matricula, aulaAluno, dataComHoraZerada, dataHora2359, conZW, contrato);
        }

        AulaAlunoDTO.ordenarPorDataEHora(aulaAluno);

        return aulaAluno;
    }


    private List<AulaAlunoDTO> consultarAulasTotaisAgendadasPorAluno(Integer matricula, Connection conZW) {
        List<AulaAlunoDTO> aulaAluno = new ArrayList<>();
        try {
            aulaAluno.addAll(consultaMapaTurmaPorMatricula(matricula, conZW));

            aulaAluno.addAll(consultaTurmaPorMatricula(matricula, conZW));

            aulaAluno.addAll(consultaReposicoesPorMatricula(matricula, conZW));

            consultaAulasDesmarcadasPorMatricula(matricula, conZW, aulaAluno);
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
        }
        AulaAlunoDTO.ordenarPorData(aulaAluno);

        return aulaAluno;
    }

    private void consultaAulasDesmarcadasPorMatricula(Integer matricula, Connection conZW, List<AulaAlunoDTO> aulaAluno) {
        StringBuilder sql = new StringBuilder();
        sql.append("select cliente, dataorigem, datareposicao, horarioturma, contrato, justificativa\n");
        sql.append("from auladesmarcada ad\n");
        sql.append(" inner join contrato con on con.codigo = ad.contrato\n");
        sql.append(" inner join cliente c on ad.cliente = c.codigo\n");
        sql.append(" where c.codigomatricula = ").append(matricula);

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
            while (rs.next()) {
                for (AulaAlunoDTO aulaAlunoDTO : aulaAluno) {
                    if (aulaAlunoDTO.getDia().equals(Uteis.getDataAplicandoFormatacao(rs.getDate("dataorigem"), "yyyy-MM-dd")) && aulaAlunoDTO.getCodigoHorarioTurma() == rs.getInt("horarioturma")) {
                        aulaAlunoDTO.setDesmarcada(true);
                    }
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
        }
    }

    @Override
    public String criaProgramaTreinoGeradoPorIA(String ctx, ProgramaDeTreinoGeradoPorIADTO programaDeTreinoGeradoPorIADTO, String origem) throws Exception {
        ConfiguracaoSistema valorAntesConfigPermitirCriarTreinoIa = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA);
        boolean configAntesPermitirCriarTreinoIa = valorAntesConfigPermitirCriarTreinoIa != null && Boolean.parseBoolean(valorAntesConfigPermitirCriarTreinoIa.getValor());

        if (!configAntesPermitirCriarTreinoIa) {
            throw new ServiceException("A configuração 'Permitir criação de treino automatizado (I.A)' está desabilitada. Por isso, esta ação foi interrompida!");
        }

        List<ProgramaDeTreinoGeradoPorIAVO> programas = new ArrayList<>();
        ProgramaTreino programaTreino = new ProgramaTreino();
        List<Serie> series = new ArrayList<>();
        List<AtividadeFicha> atividadesFicha = new ArrayList<>();
        List<Ficha> fichas = new ArrayList<>();
        List<ProgramaTreinoFicha> programaTreinoFichas = new ArrayList<>();
        List<AtividadeAnimacao> atividadesAnimacao = new ArrayList<>();

        ClienteSintetico clienteSintetico = clienteSinteticoService.obterPorCodigo(ctx, programaDeTreinoGeradoPorIADTO.getAlunoId());

        String origemOperacao = "";
        Usuario usuarioLogado;
        if (origem != null && origem.equals("app")) {
            usuarioLogado = usuarioService.consultarPorCliente(ctx, programaDeTreinoGeradoPorIADTO.getAlunoId());
            origemOperacao = " [Origem: APP]";
        } else {
            usuarioLogado = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
        }

        if (clienteSintetico == null || clienteSintetico.getCodigo() == null || clienteSintetico.getCodigo() == 0) {
            incluirLog(ctx, null, null, "", "Cliente não encontrado",
                    "ERRO", "Erro ao buscar cliente" + origemOperacao, EntidadeLogEnum.ALUNO, "Cliente", treinoIaUsername, logDao, true, null, null);
            throw new ServiceException("Cliente não encontrado");
        }

        try {
            programaTreino.setNome(programaDeTreinoGeradoPorIADTO.getNome());
            programaTreino.setWorkoutIDIA(programaDeTreinoGeradoPorIADTO.getWorkoutID());
            programaTreino.setDataInicio(new Date(programaDeTreinoGeradoPorIADTO.getInicio()));
            programaTreino.setDataTerminoPrevisto(new Date(programaDeTreinoGeradoPorIADTO.getTermino()));
            programaTreino.setGeradoPorIA(true);
            programaTreino.setPreDefinido(false);
            programaTreino.setCliente(clienteSintetico);
            programaTreino.setDataLancamento(new Date(System.currentTimeMillis()));

            ConfiguracaoSistema configFluxoAprovacao = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.TEMPO_APROVACAO_AUTOMATICA);
            ConfiguracaoSistema configFluxoAprovacaoObrigatoria = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.HABILITAR_OBRIGATORIEDADE_APROVACAO_PROFESSOR);
            Integer tempoAprovacao = (configFluxoAprovacao != null && configFluxoAprovacao.getValor() != null && !UteisValidacao.emptyString(configFluxoAprovacao.getValor()))
                    ? Integer.valueOf(configFluxoAprovacao.getValor())
                    : 0;

            long diferencaEmMinutos = ChronoUnit.MINUTES.between(programaTreino.getDataLancamento().toInstant(), Calendario.hoje().toInstant());

            if ((tempoAprovacao == 0 || diferencaEmMinutos > tempoAprovacao) && (!configFluxoAprovacaoObrigatoria.getValorAsBoolean())) {
                programaTreino.setEmRevisaoProfessor(false);
            } else {
                programaTreino.setEmRevisaoProfessor(true);
            }

            Integer codigoEmpresaZW = clienteSintetico.getEmpresa();
            Empresa empresaCliente = empresaService.obterPorIdZW(ctx, codigoEmpresaZW);
            Integer codigoProfessor = professorService.consultarCodProfessorPorNomeECodigoEmpresa(ctx, "Treino por IA", empresaCliente.getCodigo());
            ProfessorSintetico professor = professorService.obterPorId(ctx, codigoProfessor);


            if (professor == null || professor.getCodigo() == null || professor.getCodigo() <= 0) {
                Usuario usuarioTreinoIa = usuarioService.consultarPorUserName(ctx, treinoIaUsername);
                if (usuarioTreinoIa != null && usuarioTreinoIa.getProfessor() != null) {
                    professor = usuarioTreinoIa.getProfessor();
                }
            }
            if (professor != null && professor.getCodigo() != null && professor.getCodigo() > 0) {
                programaTreino.setProfessorMontou(professor);
            }

            if (programaDeTreinoGeradoPorIADTO.getDiasPorSemana() != null) {
                programaTreino.setDiasPorSemana(programaDeTreinoGeradoPorIADTO.getDiasPorSemana());
                long diferenca = programaTreino.getDataTerminoPrevisto().getTime() - programaTreino.getDataInicio().getTime();
                Integer semanas = (int) (diferenca / Uteis.calcularMillisPorSemana());
                programaTreino.setTotalAulasPrevistas(semanas * programaTreino.getDiasPorSemana());
            }

            incluirLog(ctx, null, null, "", programaTreino.getDescricaoParaLog(null),
                    "INCLUSÃO", "Criação inicial do programa de treino" + origemOperacao, EntidadeLogEnum.PROGRAMA, "Programa", treinoIaUsername, logDao, true, null, null);

            List<ProgramaTreino> programaTreinosPreExistentes = obterProgramasPorCliente(ctx, clienteSintetico.getCodigo(),
                    programaTreino.getDataInicio(), programaTreino.getDataTerminoPrevisto(), 0, 1);
            if (!programaTreinosPreExistentes.isEmpty()) {
                String dataInicio = Uteis.getDataAplicandoFormatacao(programaTreino.getDataInicio(), "dd/MM/yyyy");
                String dataTermino = Uteis.getDataAplicandoFormatacao(programaTreino.getDataTerminoPrevisto(), "dd/MM/yyyy");

                incluirLog(ctx, null, null, "", "Cliente já possui programa no período de " + dataInicio + " a " + dataTermino,
                        "ERRO", "Conflito de programa de treino" + origemOperacao, EntidadeLogEnum.PROGRAMA, "Programa", treinoIaUsername, logDao, true, null, null);
                throw new ServiceException("Cliente já possui um programa de treino!");
            }

            for (FichaDeTreinoGeradaPorIADTO fichaDeTreinoGeradaPorIADTO : programaDeTreinoGeradoPorIADTO.getFichas()) {
                Ficha ficha = new Ficha();
                ficha.setNome("FICHA " + fichaDeTreinoGeradaPorIADTO.getNome());
                ficha.setVersao(fichaDeTreinoGeradaPorIADTO.getVersao());

                fichas.add(ficha);

                ProgramaTreinoFicha programaTreinoFicha = new ProgramaTreinoFicha();
                programaTreinoFicha.setFicha(ficha);
                programaTreinoFicha.setPrograma(programaTreino);
                try {
                    programaTreinoFicha.setTipoExecucao(TipoExecucaoEnum.valueOf(fichaDeTreinoGeradaPorIADTO.getTipo_execucao().toUpperCase()));
                } catch (Exception e) {
                    programaTreinoFicha.setTipoExecucao(TipoExecucaoEnum.ALTERNADO);
                }

                programaTreinoFichas.add(programaTreinoFicha);

                for (AtividadeTreinoGeradoPorIADTO atividadeTreinoGeradoPorIADTO : fichaDeTreinoGeradaPorIADTO.getAtividades()) {
                    AtividadeFicha atividadeFicha = new AtividadeFicha();
                    try {
                        atividadeFicha.setMetodoExecucao(MetodoExecucaoEnum.valueOf(atividadeTreinoGeradoPorIADTO.getMetodoExecucao()));
                    } catch (Exception e) {
                        atividadeFicha.setMetodoExecucao(MetodoExecucaoEnum.NAO_ATRIBUIDO);
                    }
                    atividadeFicha.setOrdem(atividadeTreinoGeradoPorIADTO.getSequencia());
                    atividadeFicha.setFicha(ficha);

                    Atividade atividade = null;

                    // Primeira tentativa: buscar por ID da IA
                    atividade = atividadeService.obterPorIdIAVersaoDois(ctx, atividadeTreinoGeradoPorIADTO.getAtividade().getId_ia());

                    // Fallback: buscar na API de IA e inserir no banco
                    if (atividade == null || UteisValidacao.emptyNumber(atividade.getCodigo())) {
                        String idIaAtv = String.format("%04d", atividadeTreinoGeradoPorIADTO.getAtividade().getId_ia());
                        atividade = atividadeService.importarAtividadeIa(ctx, idIaAtv);
                    }
                    if (atividade != null && !UteisValidacao.emptyNumber(atividade.getCodigo())) {
                        if (atividadeTreinoGeradoPorIADTO.getAtividadeAlternativa() != null && !atividadeTreinoGeradoPorIADTO.getAtividadeAlternativa().isEmpty()) {
                            for (AtividadeAlternativaDTO atividadeAltDto : atividadeTreinoGeradoPorIADTO.getAtividadeAlternativa()) {
                                if (atividadeAltDto != null && atividadeAltDto.getId() != null) {
                                    Atividade atividadeAlternativa = atividadeService.obterPorIdIAVersaoDois(ctx, atividadeAltDto.getId());
                                    String nomeSemAcentos = removerAcentos(atividadeAltDto.getNome());

                                    if (atividadeAlternativa == null || atividadeAlternativa.getCodigo() == null) {
                                        atividadeAlternativa = atividadeService.buscarPorNomeOriginalIA(ctx, nomeSemAcentos);
                                    }

                                    if (atividadeAlternativa == null || atividadeAlternativa.getCodigo() == null) {
                                        atividadeAlternativa = atividadeService.buscarPorNomeAtividade(ctx, nomeSemAcentos);
                                    }

                                    if (atividadeAlternativa != null && atividadeAlternativa.getCodigo() != null) {
                                        boolean jaExiste = atividadeAlternativaDao.existsByAtividadeAndAtividadeAlternativa(ctx,
                                                atividade.getCodigo(), atividadeAlternativa.getCodigo());

                                        if (!jaExiste) {
                                            AtividadeAlternativa relacaoAlternativa = new AtividadeAlternativa();
                                            relacaoAlternativa.setAtividade(atividade);
                                            relacaoAlternativa.setAtividadeAlternativa(atividadeAlternativa.getCodigo());
                                            atividadeAlternativaDao.insert(ctx, relacaoAlternativa);
                                        }
                                    } else {
                                        System.out.println("Atividade alternativa não encontrada: " + atividadeAltDto.getNome());
                                    }
                                }
                            }
                        }

                        incluirLog(ctx, null, null, "", atividadeFicha.getDescricaoParaLog(null),
                                "INCLUSÃO", "Criação da atividade ficha" + origemOperacao, EntidadeLogEnum.ATIVIDADE, "Atividade", treinoIaUsername, logDao, true, null, null);

                        for (SerieGeradaPorIADTO serieGerada : atividadeTreinoGeradoPorIADTO.getSeries()) {
                            Serie novaSerie = new Serie();
                            novaSerie.setAtividadeFicha(atividadeFicha);
                            novaSerie.setQuantidade(atividadeTreinoGeradoPorIADTO.getSeries().size());
                            novaSerie.setDescanso(serieGerada.getDescanso() != null && !serieGerada.getDescanso().isEmpty()
                                    ? Integer.parseInt(serieGerada.getDescanso())
                                    : 0);
                            novaSerie.setRepeticao(serieGerada.getRepeticoes());
                            novaSerie.setCarga(serieGerada.getCarga() != null ? serieGerada.getCarga() : 0.0);

                            atividadeFicha.getSeries().add(novaSerie);

                            incluirLog(ctx, null, null, "", novaSerie.getDescricaoParaLog(null),
                                    "INCLUSÃO", "Criação da série" + origemOperacao, EntidadeLogEnum.ATIVIDADE, "Série", treinoIaUsername, logDao, true, null, null);
                        }

                        atividadeFicha.setAtividade(atividade);
                        atividadesFicha.add(atividadeFicha);
                    } else {
                        Uteis.logarDebug("### ATENÇÃO, ATIVIDADE IA NÃO LOCALIZADA E NÃO IMPORTADA, FICHA GERADA SEM A ATIVIDADE! CODIGO IA_ATV: " + atividadeTreinoGeradoPorIADTO.getAtividade().getId_ia());
                    }
                }
            }
            programaTreino = programatreinoDao.insert(ctx, programaTreino);
            incluirLog(ctx, programaTreino.getCodigo().toString(), clienteSintetico.getCodigo().toString(), "",
                    programaTreino.getDescricaoParaLog(null), "INCLUSÃO", "INCLUSÃO DE PROGRAMA" + origemOperacao,
                    EntidadeLogEnum.PROGRAMA, "Programa", treinoIaUsername, logDao, true, null, null);

            ProgramaDeTreinoGeradoPorIAVO programaDeTreinoGeradoPorIAVO = new ProgramaDeTreinoGeradoPorIAVO();
            programaDeTreinoGeradoPorIAVO.setId(programaTreino.getCodigo());
            programaDeTreinoGeradoPorIAVO.setNome(programaTreino.getNome());

            // o valor contido em clienteSintetico.getEmpresa() é o codZw da empresa
            atualizarDadoSintetico(ctx, programaTreino, clienteSintetico.getEmpresa());

            for (AtividadeAnimacao atividadeAnimacao : atividadesAnimacao) {
                if (!UteisValidacao.emptyString(atividadeAnimacao.getFotoKey()) && atividadeAnimacao.getAtividade().getCodigo() != null) {
                    byte[] imagemBase64 = converteImagemParaBytes(atividadeAnimacao.getFotoKey());

                    List<AtividadeImagemUploadTO> atividadeImagemUploadTOList = new ArrayList<>();
                    AtividadeImagemUploadTO atividadeImagemUploadTO = new AtividadeImagemUploadTO();
                    atividadeImagemUploadTO.setData(imagemBase64);
                    atividadeImagemUploadTO.setNome("imagem-ia-" + atividadeAnimacao.getAtividade().getCodigo() + ".gif");
                    atividadeImagemUploadTOList.add(atividadeImagemUploadTO);

                    atividadeAnimacao.getAtividade().setAnimacoes(new ArrayList<>());

                    atividadeService.salvarMidiaNuvemEndpoint(ctx, atividadeImagemUploadTOList, atividadeAnimacao.getAtividade());
                }
            }

            List<FichaGeradaPorIAVO> fichasGeradas = new ArrayList<>();
            for (Ficha ficha : fichas) {
                ficha = fichaDao.insert(ctx, ficha);
                FichaGeradaPorIAVO fichaGerada = new FichaGeradaPorIAVO();
                fichaGerada.setId(ficha.getCodigo());
                fichaGerada.setNome(ficha.getNome());
                fichasGeradas.add(fichaGerada);
            }
            programaDeTreinoGeradoPorIAVO.setFichas(fichasGeradas);
            programas.add(programaDeTreinoGeradoPorIAVO);

            for (ProgramaTreinoFicha programaTreinoFicha : programaTreinoFichas) {
                programaTreinoFicha = programaTreinoFichaDao.insert(ctx, programaTreinoFicha);
                incluirLog(ctx, programaTreinoFicha.getFicha().getCodigo().toString(), null, "", programaTreinoFicha.getDescricaoParaLog(null),
                        "INCLUSÃO", "Criação da ficha" + origemOperacao, EntidadeLogEnum.FICHA, "Ficha", treinoIaUsername, logDao, true, null, null);
            }

            for (AtividadeFicha atividadeFicha : atividadesFicha) {
                atividadeFicha = atividadeFichaDao.insert(ctx, atividadeFicha);
            }

            ProfessorSintetico profAntesAlteracao = clienteSintetico.getProfessorSintetico();
            if (profAntesAlteracao == null || profAntesAlteracao.getCodigo() == null || profAntesAlteracao.getCodigo() <= 0) {
                if (professor != null && Boolean.TRUE.equals(professor.getProfessorTW())) {
                    IntegracaoCadastrosWSConsumer integracaoWs = UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
                    clienteSinteticoService.atualizarProfessorAluno(ctx, integracaoWs, professor.getCodigo(), clienteSintetico, usuarioLogado.getUsuarioZW(), null);

                    clienteSintetico.setProfessorSintetico(professor);
                    clienteSinteticoService.alterar(ctx, clienteSintetico);

                    incluirLog(ctx, clienteSintetico.getMatricula().toString(), "", "", professor.getNome(),
                            "INCLUSÃO", "INCLUSÃO DE PROFESSOR DO ALUNO", EntidadeLogEnum.ALUNO, "Professor do Aluno",
                            treinoIaUsername, logDao, true, null, null);
                }
            }
            return "Programa de treino gerado por IA criado com sucesso!";
        } catch (Exception e) {
            incluirLog(ctx, null, null, "", "Erro: " + e.getMessage(),
                    "ERRO", "Erro ao criar programa" + origemOperacao, EntidadeLogEnum.PROGRAMA, "Programa", treinoIaUsername, logDao, true, null, null);

            if (programaTreino.getCodigo() != null) {
                programatreinoDao.delete(ctx, programaTreino);
            }

            for (Ficha ficha : fichas) {
                if (ficha.getCodigo() != null) {
                    fichaDao.delete(ctx, ficha);
                }
            }

            for (ProgramaTreinoFicha programaTreinoFicha : programaTreinoFichas) {
                if (programaTreinoFicha.getCodigo() != null) {
                    programaTreinoFichaDao.delete(ctx, programaTreinoFicha);
                }
            }

            for (AtividadeFicha atividadeFicha : atividadesFicha) {
                if (atividadeFicha.getCodigo() != null) {
                    atividadeFichaDao.delete(ctx, atividadeFicha);
                }
            }

            for (Serie serie : series) {
                if (serie.getCodigo() != null) {
                    serieDao.delete(ctx, serie);
                }
            }
            e.printStackTrace();
            throw new ServiceException("Erro ao gerar programa de treino por IA: " + e.getMessage());
        }
    }

    public static String removerAcentos(String str) {
        if (str == null) {
            return null;
        }

        String normalized = Normalizer.normalize(str, Normalizer.Form.NFD);
        return normalized.replaceAll("\\p{M}", "");
    }

    public List<ConsultaTreinoDTO> obterProgramaTreinoGeradoPorIA(String ctx) throws ServiceException {
        try {
            List<ProgramaTreino> programas = programatreinoDao.obterProgramasPorRevisaoIA(ctx);

            return programas.stream()
                    .map(programa -> {
                        try {
                            return consultaTreinoToDTO(ctx, programa);
                        } catch (ServiceException e) {
                            throw new RuntimeException("Erro ao processar DTO: " + e.getMessage(), e);
                        }
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new ServiceException("Erro ao consultar programas de treino gerados por IA: " + e.getMessage(), e);
        }
    }

    @Override
    public HistoricoPresencasVO consultarAulasAgendadasPorAlunoTotaisEMesAtualEQtdAulasPorSemanaConsecutiva(Integer matricula, String ctx, Integer empresa) {
        HistoricoPresencasVO historicoPresencasVO = new HistoricoPresencasVO();

        try(Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            List<AulaAlunoDTO> aulasTotaisPorMatricula = consultarAulasTotaisAgendadasPorAluno(matricula, conZW);

            historicoPresencasVO.setTotalAulasRealizadas(aulasTotaisPorMatricula.size());
            Date primeiroDiaMes = Uteis.getDataHora00(Uteis.obterPrimeiroDiaMes(Calendario.hoje()));
            Date diaAtual = Uteis.getDataHora2359(Calendario.hoje());
            Set<LocalDate> semanasComAula = new HashSet<>();

            String timeZone;

            try {
                timeZone = UteisValidacao.emptyString(empresaService.obterFusoHorarioEmpresa(ctx, empresa)) ? TimeZoneEnum.Brazil_East.getId() : empresaService.obterFusoHorarioEmpresa(ctx, empresa);
                TimeZone.setDefault(TimeZone.getTimeZone(timeZone));
            } catch (Exception ex) {
                TimeZone.setDefault(TimeZone.getTimeZone(TimeZoneEnum.Brazil_East.getId()));
            }
            ZoneId zone = ZoneId.systemDefault();

            for (int i = aulasTotaisPorMatricula.size() - 1; i >= 0; i--) {
                AulaAlunoDTO aula = aulasTotaisPorMatricula.get(i);
                try {
                    String dataAulaString = aula.getDia().substring(0, 10); // Formato esperado: yyyy-MM-dd
                    LocalDate dataAula = UteisValidacao.emptyString(dataAulaString) ? null : LocalDate.parse(dataAulaString, DateTimeFormatter.ofPattern("yyyy-MM-dd"));

                    if (dataAula != null && !dataAula.isAfter(diaAtual.toInstant().atZone(zone).toLocalDate())) {
                        if (dataAula.isAfter(primeiroDiaMes.toInstant().atZone(zone).toLocalDate())) {
                            if(!aula.getDesmarcada()) {
                                historicoPresencasVO.setAulasMesAtual(historicoPresencasVO.getAulasMesAtual() + 1);
                            }
                        }
                        if (aula.getDesmarcada()) {
                            historicoPresencasVO.setTotalAulasRealizadas(historicoPresencasVO.getTotalAulasRealizadas() - 1);
                        } else {
                            semanasComAula.add(dataAula.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)));
                        }
                    } else {
                        //remover aula se a data for inválida ou futura
                        historicoPresencasVO.setTotalAulasRealizadas(historicoPresencasVO.getTotalAulasRealizadas() - 1);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            List<LocalDate> semanasOrdenadas = new ArrayList<>(semanasComAula);
            Collections.sort(semanasOrdenadas, Comparator.reverseOrder());

            LocalDate semanaAtual = LocalDate.now().with(TemporalAdjusters.previous(DayOfWeek.MONDAY));
            int semanasConsecutivas = 0;
            for (LocalDate semana : semanasOrdenadas) {
                if (semana.equals(semanaAtual)) {
                    semanasConsecutivas++;
                    semanaAtual = semanaAtual.minusWeeks(1);
                } else {
                    break;
                }
            }

            historicoPresencasVO.setSemanasConsecutivas(semanasConsecutivas);

        } catch (Exception e) {
            e.printStackTrace();
        }

        return historicoPresencasVO;
    }

    @Override
    public HistoricoTreinosVO dadosTreinos(String ctx, Integer codigoCliente, String periodoInicio, String periodoFinal) throws Exception {
        HistoricoTreinosVO historicoTreinosVO = new HistoricoTreinosVO();
        Date dataInicio = null;
        Date dataFinal = null;

        if (UteisValidacao.emptyString(periodoInicio) || UteisValidacao.emptyString(periodoFinal)) {
            dataInicio = Uteis.getDataHora00(Uteis.somarDias(Calendario.hoje(), -30));
            dataFinal = Uteis.getDataHora2359(Calendario.hoje());
        } else {
            try {
                dataInicio = Uteis.getDate(periodoInicio, "yyyy-MM-dd");
                dataFinal = Uteis.getDate(periodoFinal, "yyyy-MM-dd");

                if(dataFinal.before(dataInicio)) {
                    throw new ServiceException("Data final não pode ser menor que a data inicial");
                }

            } catch (Exception e) {
                throw new ServiceException("Erro ao converter datas. Informar no padrão yyyy-MM-dd");
            }
        }

        historicoTreinosVO.setPeriodoInicioReport(Uteis.getDataAplicandoFormatacao(dataInicio, "dd/MM/yyyy"));
        historicoTreinosVO.setPeriodoFinalReport(Uteis.getDataAplicandoFormatacao(dataFinal, "dd/MM/yyyy"));

        Usuario usuarioIA = usuarioService.consultarPorUserName(ctx, treinoIaUsername);

        if (usuarioIA != null) {
            String sql = "SELECT COUNT(CASE WHEN isgeradoporia AND professormontou_codigo IS NOT NULL AND professormontou_codigo <> ? THEN 1 END) AS revisados, "
                    + "COUNT(CASE WHEN emrevisaoprofessor THEN 1 END) AS pendentes, "
                    + "COUNT(CASE WHEN isgeradoporia AND professormontou_codigo = ? THEN 1 END) AS aprovadosAuto, "
                    + "COUNT(CASE WHEN isgeradoporia THEN 1 END) AS totalIA "
                    + "FROM programatreino WHERE datainicio >= ? AND dataterminoprevisto <= ?";
            boolean filtraCliente = !UteisValidacao.emptyNumber(codigoCliente);
            if(filtraCliente) {
                        sql += " AND cliente_codigo = ?";
                    }

            try (PreparedStatement ps = programatreinoDao.getConnection(ctx).prepareStatement(sql)) {
                ps.setInt(1, usuarioIA.getCodigo());
                ps.setInt(2, usuarioIA.getCodigo());
                ps.setDate(3, new java.sql.Date(dataInicio.getTime()));
                ps.setDate(4, new java.sql.Date(dataFinal.getTime()));
                if(filtraCliente) {
                    ps.setInt(5, codigoCliente);
                }

                ResultSet rs = ps.executeQuery();
                if (rs.next()) {
                    historicoTreinosVO.setNumeroTreinosRevisados(rs.getInt("revisados"));
                    historicoTreinosVO.setNumeroDeTreinosPendentes(rs.getInt("pendentes"));
                    historicoTreinosVO.setNumeroDeTreinosAprovadosAutomaticamente(rs.getInt("aprovadosAuto"));
                    historicoTreinosVO.setNumeroTotalDeTreinosPorIa(rs.getInt("totalIA"));
                }
            }
        }

        return historicoTreinosVO;
    }

    private List<AulaAlunoDTO> consultaReposicoesPorMatricula(Integer matricula, Connection conZW) {
        List<AulaAlunoDTO> aulaAluno = new ArrayList<>();

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT r.datareposicao, ht.codigo, ht.horainicial, ht.horafinal, \n");
        sql.append(" p.nome,\n");
        sql.append(" p2.nome as nomeprofessor,\n");
        sql.append(" t.descricao as descricaoturma, \n");
        sql.append(" m.codigo as modalidade \n");
        sql.append("FROM reposicao r\n");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = r.horarioturma\n");
        sql.append(" INNER JOIN turma t ON t.codigo = r.turmadestino\n");
        sql.append(" INNER JOIN modalidade m on m.codigo = t.modalidade\n");
        sql.append(" INNER JOIN cliente ON cliente.codigo = r.cliente\n");
        sql.append(" INNER JOIN situacaoclientesinteticodw sc ON sc.codigocliente = r.cliente\n");
        sql.append(" INNER JOIN pessoa p ON p.codigo = sc.codigopessoa\n");
        sql.append(" LEFT JOIN controlecreditotreino cc ON cc.reposicao = r.codigo\n");
        sql.append(" LEFT JOIN agenda ag ON ag.reposicao = r.codigo\n");
        sql.append(" LEFT JOIN periodoacessocliente pe on pe.codigo = (select max(codigo)\n");
        sql.append(" from periodoacessocliente\n");
        sql.append(" where pessoa = p.codigo\n");
        sql.append(" and tipoTotalPass is true)\n");
        sql.append(" JOIN colaborador professor on professor.codigo = ht.professor\n");
        sql.append(" JOIN pessoa p2 on p2.codigo = professor.pessoa\n");
        sql.append("WHERE cliente.codigomatricula = ").append(matricula).append("\n");
        sql.append(" ORDER BY r.datareposicao desc");

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
            while (rs.next()) {
                AulaAlunoDTO aulaAlunoDTO = new AulaAlunoDTO();
                aulaAlunoDTO.setNomeAula(rs.getString("descricaoturma"));
                aulaAlunoDTO.setNomeProfessor(rs.getString("nomeprofessor"));
                aulaAlunoDTO.setDia(rs.getString("datareposicao"));
                aulaAlunoDTO.setCodigoHorarioTurma(rs.getInt("codigo"));
                aulaAlunoDTO.setAulaTurma(true);
                aulaAluno.add(aulaAlunoDTO);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return aulaAluno;
    }

    private List<AulaAlunoDTO> consultaTurmaPorMatricula(Integer matricula, Connection conZW) throws Exception {
        List<AulaAlunoDTO> aulaAluno = new ArrayList<>();

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT professor.nome as nomeprofessor, ht.codigo, p.nome, t.descricao, aht.dia, ht.horainicial, ht.horafinal ");
        sql.append("FROM alunohorarioturma aht\n ");
        sql.append("INNER JOIN cliente c ON aht.cliente = c.codigo\n ");
        sql.append("INNER JOIN pessoa p ON c.pessoa = p.codigo\n ");
        sql.append("INNER JOIN horarioturma ht ON aht.horarioturma = ht.codigo\n ");
        sql.append("INNER JOIN turma t ON ht.turma = t.codigo\n ");
        sql.append("INNER JOIN colaborador col on ht.professor = col.codigo ");
        sql.append("INNER JOIN pessoa professor on professor.codigo = col.pessoa ");
        sql.append("WHERE c.codigomatricula = ").append(matricula);
        sql.append(" ORDER BY aht.dia desc");

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
            while (rs.next()) {
                AulaAlunoDTO aulaAlunoDTO = new AulaAlunoDTO();
                aulaAlunoDTO.setNomeAula(rs.getString("descricao"));
                aulaAlunoDTO.setNomeProfessor(rs.getString("nomeprofessor"));
                aulaAlunoDTO.setDia(rs.getString("dia").substring(0, 10));
                aulaAlunoDTO.setCodigoHorarioTurma(rs.getInt("codigo"));
                aulaAlunoDTO.setAulaTurma(false);
                aulaAluno.add(aulaAlunoDTO);
            }
        }
        return aulaAluno;
    }

    private List<AulaAlunoDTO> consultaMapaTurmaPorMatricula(Integer matricula, Connection conZW) throws Exception {
        List<AulaAlunoDTO> aulaAluno = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT t.descricao as descricaoturma, m.codigo as modalidade, ht.codigo, pessoa_colaborador.nome as nomeprofessor, diasemananumero, horainicial, horafinal, datainicio, datafim FROM matriculaalunohorarioturma ma\n ");
        sql.append("INNER JOIN pessoa p ON p.codigo = ma.pessoa\n ");
        sql.append("INNER JOIN cliente cli ON cli.pessoa = ma.pessoa\n ");
        sql.append("INNER JOIN horarioturma ht ON ht.codigo = ma.horarioturma\n ");
        sql.append("INNER JOIN turma t ON t.codigo = ht.turma\n ");
        sql.append("INNER JOIN modalidade m ON t.modalidade = m.codigo  AND m.utilizarturma \n ");
        sql.append("INNER JOIN colaborador c on ht.professor = c.codigo\n ");
        sql.append("INNER JOIN pessoa pessoa_colaborador on c.pessoa = pessoa_colaborador.codigo\n ");
        sql.append("WHERE cli.codigomatricula = ").append(matricula);
        sql.append(" ORDER BY datainicio");

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
            while (rs.next()) {
                AulaAlunoDTO aulaAlunoDTO = new AulaAlunoDTO();
                aulaAlunoDTO.setNomeAula(rs.getString("descricaoturma"));
                aulaAlunoDTO.setNomeProfessor(rs.getString("nomeprofessor"));
                aulaAlunoDTO.setCodigoHorarioTurma(rs.getInt("codigo"));

                Integer diaSemana = rs.getInt("diasemananumero");

                if (diaSemana >= 1 && diaSemana <= 7) { // 1 é domingo, 7 é sábado
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    Date dataInicioAulas = sdf.parse(rs.getString("datainicio"));
                    Date dataFimAulas = sdf.parse(rs.getString("datafim"));
                    Date dataFimContagemDeAulas = dataFimAulas.after(Calendario.hoje()) ? Calendario.hoje() : dataFimAulas;

                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(dataInicioAulas);

                    while (!calendar.getTime().after(dataFimContagemDeAulas)) {
                        if (calendar.get(Calendar.DAY_OF_WEEK) == diaSemana) {
                            AulaAlunoDTO alunoDTO = new AulaAlunoDTO();
                            alunoDTO.setNomeAula(aulaAlunoDTO.getNomeAula());
                            alunoDTO.setNomeProfessor(aulaAlunoDTO.getNomeProfessor());
                            alunoDTO.setCodigoHorarioTurma(aulaAlunoDTO.getCodigoHorarioTurma());
                            alunoDTO.setDia(sdf.format(calendar.getTime()));
                            alunoDTO.setAulaTurma(true);
                            aulaAluno.add(alunoDTO);

                            calendar.add(Calendar.DAY_OF_MONTH, 7);
                            continue;
                        }
                        calendar.add(Calendar.DAY_OF_MONTH, 1);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return aulaAluno;
    }

    private ConsultaTreinoDTO consultaTreinoToDTO(String ctx, ProgramaTreino programa) throws ServiceException {
        try {
            ConsultaTreinoDTO dto = new ConsultaTreinoDTO();

            dto.setCodEmpresa(programa.getCliente().getEmpresa());
            dto.setDataGerado(
                    programa.getDataLancamento() != null
                            ? programa.getDataLancamento().toString()
                            : null
            );
            dto.setMatricula(String.valueOf(programa.getCliente().getMatricula()));
            dto.setVaiSerAprovadoAutomaticamenteEm(
                    programa.getDataProximaRevisao() != null
                            ? programa.getDataProximaRevisao().toString()
                            : null
            );
            dto.setNomeAluno(programa.getCliente().getNome());
            dto.setFotoAluno(programa.getCliente().getPessoa().getFotoKey());
            dto.setIdAluno(programa.getCliente().getCodigo().longValue());

            ProgramaTreinoJSON programaJSON = ProgramaTreinoJSONControle.preencherProgramaJSON(programa, ctx, null, false, "", OrigemEnum.APP);

            dto.setProgramasGerados(Collections.singletonList(programaJSON));

            return dto;
        } catch (Exception e) {
            throw new ServiceException("Erro ao montar DTO de consulta de treino gerados por IA: " + e.getMessage(), e);
        }
    }


    @Override
    public synchronized String preparaProgramaTreinoPorIA(AnamneseTreinoPorIADTO anamneseTreinoPorIADTO, String ctx) throws Exception {

        String origem = null;
        if (ctx == null) {
            ctx = sessaoService.getUsuarioAtual().getChave();
        } else {
            origem = "app";
        }

        ConfiguracaoSistema valorAntesConfigPermitirCriarTreinoIa = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA);
        boolean configAntesPermitirCriarTreinoIa = valorAntesConfigPermitirCriarTreinoIa != null && Boolean.parseBoolean(valorAntesConfigPermitirCriarTreinoIa.getValor());

        if (!configAntesPermitirCriarTreinoIa) {
            throw new ServiceException("A configuração 'Permitir criação de treino automatizado (I.A)' está desabilitada. Por isso, esta ação foi interrompida!");
        }

        String apiUrl = Aplicacao.getProp(Aplicacao.urlTreinoIa) + "/generate-training-plan";
        CloseableHttpClient client = HttpClients.createDefault();
        HttpPost post = new HttpPost(apiUrl);
        post.setHeader("Content-Type", "application/json");
        post.setHeader("access-token", Aplicacao.getProp(Aplicacao.accessTokenTreinoIa));
        post.setHeader("zw-key", ctx);

        if (Uteis.valorVazioString(anamneseTreinoPorIADTO.getExperience_level())) {
            throw new ServiceException("Nível de experiência não informado.");
        } else if (Uteis.valorVazioString(anamneseTreinoPorIADTO.getCurrent_condition())) {
            throw new ServiceException("Situação atual não informada.");
        }

        if (anamneseTreinoPorIADTO.getAge() < 14 || anamneseTreinoPorIADTO.getAge() > 60) {
            throw new ServiceException("Idade fora dos limites permitidos (14 a 60 anos).");
        }

        if (anamneseTreinoPorIADTO.getHeight() < 100 || anamneseTreinoPorIADTO.getHeight() > 220) {
            throw new ServiceException("Altura fora dos limites permitidos (100 a 220 cm).");
        }

        if (anamneseTreinoPorIADTO.getWeight() < 40 || anamneseTreinoPorIADTO.getWeight() > 150) {
            throw new ServiceException("Peso fora dos limites permitidos (40 a 150 kg).");
        }

        if (anamneseTreinoPorIADTO.getTraining_days() < 1 || anamneseTreinoPorIADTO.getTraining_days() > 6) {
            throw new ServiceException("Número de dias de treino por semana deve estar entre 1 e 6.");
        }

        if (anamneseTreinoPorIADTO.getTraining_time() < 40 || anamneseTreinoPorIADTO.getTraining_time() > 120) {
            throw new ServiceException("Tempo de treino deve estar entre 40 e 120 minutos.");
        }

        String jsonBody = new Gson().toJson(anamneseTreinoPorIADTO);
        JSONObject jsonObject = new JSONObject(jsonBody);
        JSONObject orderedJson = new JSONObject();

        // A IMPLEMENTAÇÃO INICIAL FOI FEITA UTILIZANDO O CÓDIGO DE CLIENTE SINTETICO,
        // PORÉM PARA FACILITAR AS ANÁLISES NA IA O ANDRIEL PEDIU PARA ENVIAR A MATRÍCULA NO CAMPO "client_id"
        Integer matricula = null;
        Integer codigoZw = null;
        try {
            Integer codigoClienteSintetico = Integer.parseInt(jsonObject.get("client_id").toString());
            ClienteSintetico clienteSintetico = clienteSinteticoService.obterPorCodigo(ctx, codigoClienteSintetico);
            if (clienteSintetico != null) {
                matricula = clienteSintetico.getMatricula();
                codigoZw = clienteSintetico.getEmpresa();
            } else {
                throw new ServiceException("O aluno não foi localizado!");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("A geração do treino foi encerrada: " + e.getMessage(), e);
        }

        String zwKey = ctx;
        if (codigoZw == null && origem == null) {
            codigoZw = sessaoService.getUsuarioAtual().getEmpresaAtual();
        }

        if (codigoZw != null) {
            zwKey = ctx + "-" + codigoZw;
        }

        post.setHeader("zw-key", zwKey);


        if (UteisValidacao.emptyNumber(matricula)) {
            throw new ServiceException("O aluno não foi localizado!");
        }

        orderedJson.put("client_id", matricula);
        orderedJson.put("age", jsonObject.getInt("age")); // Deve estar entre 14 e 60 anos
        orderedJson.put("height", jsonObject.getInt("height")); // Deve estar entre 100 e 220 cm
        orderedJson.put("weight", jsonObject.getDouble("weight")); // Deve estar entre 40 e 150 kg
        orderedJson.put("body_type", jsonObject.getString("body_type")); // Valores válidos: Masculino, Feminino
        orderedJson.put("goal", jsonObject.getString("goal")); // Valores válidos: Hipertrofia, Emagrecimento, Força
        orderedJson.put("training_days", jsonObject.getInt("training_days")); // Deve estar entre 2 e 6 dias
        orderedJson.put("training_time", jsonObject.getInt("training_time")); // Deve estar entre 40 e 120 minutos
        orderedJson.put("experience_level", jsonObject.getString("experience_level")); // Valores válidos: Iniciante, Intermediário, Avançado
        orderedJson.put("current_condition", jsonObject.getString("current_condition")); // Valores válidos: ver documentação

        StringEntity entity = new StringEntity(orderedJson.toString(), StandardCharsets.UTF_8);
        post.setEntity(entity);

        try {
            HttpResponse response = client.execute(post);
            String responseString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                JSONObject json = new JSONObject(responseString);
                JSONObject jsonTreino = json.getJSONObject("training_plan");

                ProgramaDeTreinoGeradoPorIADTO programaDeTreinoGeradoPorIADTO = new ProgramaDeTreinoGeradoPorIADTO();
                programaDeTreinoGeradoPorIADTO.setNome("Treino por I.A.");
                programaDeTreinoGeradoPorIADTO.setWorkoutID(json.getString("workout_id"));
                programaDeTreinoGeradoPorIADTO.setInicio(Calendario.getDataComHoraZerada(Calendario.hoje()).getTime());
                programaDeTreinoGeradoPorIADTO.setTermino(Uteis.somarDias(Calendario.getDataComHoraZerada(Calendario.hoje()), 30).getTime());
                programaDeTreinoGeradoPorIADTO.setGeradoPorIA(true);
                programaDeTreinoGeradoPorIADTO.setAlunoId(anamneseTreinoPorIADTO.getClient_id());
                programaDeTreinoGeradoPorIADTO.setDiasPorSemana(jsonObject.getInt("training_days"));
                programaDeTreinoGeradoPorIADTO.setFichas(new ArrayList<>());

                JSONArray fichas = jsonTreino.getJSONArray("workouts");
                for (int i = 0; i < fichas.length(); i++) {
                    JSONObject ficha = fichas.getJSONObject(i);
                    FichaDeTreinoGeradaPorIADTO fichaGeradaPorIADTO = new FichaDeTreinoGeradaPorIADTO();
                    fichaGeradaPorIADTO.setNome(ficha.getString("workout_name"));
                    fichaGeradaPorIADTO.setTipo_execucao(TipoExecucaoEnum.ALTERNADO.getDescricao());

                    JSONArray atividades = ficha.getJSONArray("activities");
                    List<AtividadeTreinoGeradoPorIADTO> atividadesFicha = new ArrayList<>();
                    for (int j = 0; j < atividades.length(); j++) {
                        JSONObject atividade = atividades.getJSONObject(j);
                        AtividadeTreinoGeradoPorIADTO atividadeFicha = new AtividadeTreinoGeradoPorIADTO();
                        atividadeFicha.setSequencia(j + 1);

                        AtividadeGeradaPorIADTO atividadeGeradaPorIADTO = new AtividadeGeradaPorIADTO();
                        atividadeGeradaPorIADTO.setId_ia(Integer.parseInt(atividade.getString("exercise_id")));
                        atividadeGeradaPorIADTO.setNome(atividade.getString("exercise_name"));

                        atividadeFicha.setAtividade(atividadeGeradaPorIADTO);
                        atividadeFicha.setMetodoExecucao(MetodoExecucaoEnum.NAO_ATRIBUIDO.getDescricao());

                        Integer series = atividade.getInt("sets");
                        List<SerieGeradaPorIADTO> seriesFicha = new ArrayList<>();
                        for (int k = 0; k < series; k++) {
                            SerieGeradaPorIADTO serieFicha = new SerieGeradaPorIADTO();
                            serieFicha.setRepeticaoComp(atividade.getJSONObject("repetitions").getInt("max") + "");
                            serieFicha.setRepeticoes(atividade.getJSONObject("repetitions").getInt("max"));
                            serieFicha.setDescanso(atividade.getInt("rest_interval") + "");
                            serieFicha.setSequencia(k + 1);
                            seriesFicha.add(serieFicha);
                        }

                        atividadeFicha.setSeries(seriesFicha);

                        // Adicionando exercícios similares na atividade
                        JSONArray similarExercisesArray = atividade.getJSONArray("similar_exercises");
                        List<AtividadeAlternativaDTO> atividadesAlternativas = new ArrayList<>();
                        for (int s = 0; s < similarExercisesArray.length(); s++) {
                            JSONObject similarExercise = similarExercisesArray.getJSONObject(s);
                            Integer id = similarExercise.getInt("id");
                            String nome = similarExercise.getString("name");
                            atividadesAlternativas.add(new AtividadeAlternativaDTO(id, nome));
                        }
                        atividadeFicha.setAtividadeAlternativa(atividadesAlternativas);

                        // Adicionando ativação muscular na atividade
                        JSONObject muscleActivationObject = atividade.getJSONObject("muscle_activation");
                        List<String> gruposMusculares = new ArrayList<>();

                        // Mapeamento de nomes retornados pela API para nomes existentes no banco
                        Map<String, String> muscleMapping = new HashMap<>();
                        muscleMapping.put("peitorais", "PEITORAL");
                        muscleMapping.put("tríceps", "TRICEPS");
                        muscleMapping.put("deltoides", "DELTÓIDE");
                        muscleMapping.put("romboides", "DORSAL"); // Romboides associado ao grupo dorsal
                        muscleMapping.put("trapézio", "TRAPÉZIO");
                        muscleMapping.put("quadríceps", "QUADRICEPS");
                        muscleMapping.put("panturrilhas", "PANTURRILHA");
                        muscleMapping.put("glúteos", "GLÚTEO");
                        muscleMapping.put("isquiotibiais", "POSTERIOR DE COXA"); // Isquiotibiais associado ao grupo posterior de coxa
                        muscleMapping.put("adutores", "ADUTORES");
                        muscleMapping.put("latíssimo do dorso", "DORSAL"); // Latíssimo do dorso associado ao grupo dorsal
                        muscleMapping.put("reto abdominal", "ABDOMEN"); // Reto abdominal associado ao grupo abdomen
                        muscleMapping.put("oblíquos", "ABDOMEN"); // Oblíquos associado ao grupo abdomen
                        muscleMapping.put("transverso do abdome", "ABDOMEN"); // Transverso do abdome associado ao grupo abdomen
                        muscleMapping.put("tibial anterior", "TIBIAL ANTERIOR");
                        muscleMapping.put("bíceps", "BÍCEPS");

                        for (String key : muscleActivationObject.keySet()) {
                            String muscle = key.toLowerCase(Locale.ROOT);
                            muscle = Normalizer.normalize(muscle, Normalizer.Form.NFD).replaceAll("[^\\p{ASCII}]", "");

                            if (muscleMapping.containsKey(muscle)) {
                                gruposMusculares.add(muscleMapping.get(muscle));
                            }
                        }
                        atividadeFicha.setGrupoMuscular(gruposMusculares);

                        atividadesFicha.add(atividadeFicha);
                    }

                    fichaGeradaPorIADTO.setAtividades(atividadesFicha);
                    programaDeTreinoGeradoPorIADTO.getFichas().add(fichaGeradaPorIADTO);
                }

                // Verifica se existe um usuário TreinoIA, caso não existir crie do zero
                usuarioService.criarUsuarioTreinoIA(ctx);

                // Criar Programa de Treino
                criaProgramaTreinoGeradoPorIA(ctx, programaDeTreinoGeradoPorIADTO, origem);

                return "Programa de treino gerado por IA criado com sucesso!";
            } else {
                throw new RuntimeException("Falha ao gerar plano de treino: " + response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public Integer obterQuantidadeTreinosRealizadosPorAluno(String ctx, Integer id, Date dataInicio, Date dataAtual) {

        try {
            try (ResultSet rs = treinoRealizadoDao.createStatement(ctx, "SELECT COUNT(*) AS quantidade FROM treinorealizado WHERE cliente_codigo = " + id +
                    " AND datainicio >= '" + Uteis.getDataFormatoBD(dataInicio) + "'")) {

                if (rs.next()) {
                    return rs.getInt("quantidade");
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return 0;
    }

    @Override
    public List<AulaAlunoDTO> consultarAulasAgendadasPorAlunoV2(Integer matricula, String dataInicio, String dataFim, String ctx, Integer contrato) throws Exception {
        return alterarFormatoDia(consultarAulasAgendadasPorAluno(matricula, dataInicio, dataFim, ctx, contrato));
    }

    public List<AulaAlunoDTO> alterarFormatoDia(List<AulaAlunoDTO> lista) {
        SimpleDateFormat originalFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat targetFormat = new SimpleDateFormat("dd/MM/yyyy");

        for (AulaAlunoDTO aula : lista) {
            String diaOriginal = aula.getDia();
            if (diaOriginal != null && !diaOriginal.isEmpty()) {
                try {
                    // Converte o dia para o novo formato
                    Date data = originalFormat.parse(diaOriginal);
                    String diaFormatado = targetFormat.format(data);
                    aula.setDia(diaFormatado);
                } catch (ParseException e) {
                    System.err.println("Erro ao formatar a data: " + diaOriginal);
                    e.printStackTrace();
                }
            }
        }
        return lista;
    }

    private static byte[] converteImagemParaBytes(String imageUrl) {
        try {
            URL url = new URL(imageUrl);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            try (InputStream is = url.openStream()) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = is.read(buffer)) != -1) {
                    baos.write(buffer, 0, bytesRead);
                }
            }
            return baos.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private String trazURLReduzida(String fotoKeyPequena) {
        String fotoURL = "";
        if (fotoKeyPequena != null && !fotoKeyPequena.isEmpty()) {
            int gifIndex = fotoKeyPequena.indexOf(".gif");
            int jpgIndex = fotoKeyPequena.indexOf(".jpg");

            if (gifIndex != -1) {
                fotoURL = fotoKeyPequena.substring(0, gifIndex + 4);
            } else if (jpgIndex != -1) {
                fotoURL = fotoKeyPequena.substring(0, jpgIndex + 4);
            }
            return fotoURL;
        }
        return null;
    }

    private static void ordenaListaDeAulasPorDataEHorario(List<AulaAlunoDTO> aulaAluno) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");

        Collections.sort(aulaAluno, new Comparator<AulaAlunoDTO>() {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");

            @Override
            public int compare(AulaAlunoDTO aula1, AulaAlunoDTO aula2) {
                try {
                    Date diaHora1 = sdf.parse(aula1.getDia());
                    Date diaHora2 = sdf.parse(aula2.getDia());
                    return diaHora1.compareTo(diaHora2);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                return 0;
            }
        });
    }

    private static void consultaAulasDesmarcadasPorPeriodoEMatricula(Integer matricula, List<AulaAlunoDTO> aulaAluno, Date dataComHoraZerada, Date dataHora2359, Connection conZW, Integer contrato) throws Exception {
        StringBuilder sql;

        sql = new StringBuilder();
        sql.append("select cliente, dataorigem, datareposicao, horarioturma, contrato, justificativa\n");
        sql.append("from auladesmarcada ad\n");
        sql.append(" inner join contrato con on con.codigo = ad.contrato\n");
        sql.append(" inner join cliente c on ad.cliente = c.codigo\n");
        sql.append("where ad.dataorigem BETWEEN '").append(dataComHoraZerada).append("' and '").append(dataHora2359).append("'\n");
        sql.append("  and (ad.dataorigem::date <= con.vigenciaateajustada::date\n");
        sql.append("    or (contratoresponsavelrenovacaomatricula = 0 and contratoresponsavelrematriculamatricula = 0)) and c.codigomatricula = ").append(matricula);

        if (!UteisValidacao.emptyNumber(contrato)) {
            sql.append(" and ad.contrato = ").append(contrato);
        }

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
            while (rs.next()) {
                for (AulaAlunoDTO aulaAlunoDTO : aulaAluno) {
                    if (aulaAlunoDTO.getDia().equals(Uteis.getDataAplicandoFormatacao(rs.getDate("dataorigem"), "yyyy-MM-dd")) && aulaAlunoDTO.getCodigoHorarioTurma() == rs.getInt("horarioturma")) {
                        aulaAlunoDTO.setDesmarcada(true);
                    }
                }
            }
        }
    }

    private List<AulaAlunoDTO> consultaReposicoesPorPeriodoEmatricula(Integer matricula, Date dataComHoraZerada, Date dataHora2359, Connection conZW, Boolean filtraContrato, List<Integer> modalidadeFiltrar) throws Exception {
        List<AulaAlunoDTO> aulaAluno = new ArrayList<>();

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT r.datareposicao, ht.codigo, ht.horainicial, ht.horafinal, \n");
        sql.append(" p.nome,\n");
        sql.append(" p2.nome as nomeprofessor,\n");
        sql.append(" t.descricao as descricaoturma, \n");
        sql.append(" m.codigo as modalidade \n");
        sql.append("FROM reposicao r\n");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = r.horarioturma\n");
        sql.append(" INNER JOIN turma t ON t.codigo = r.turmadestino\n");
        sql.append(" INNER JOIN modalidade m on m.codigo = t.modalidade\n");
        sql.append(" INNER JOIN cliente ON cliente.codigo = r.cliente\n");
        sql.append(" INNER JOIN situacaoclientesinteticodw sc ON sc.codigocliente = r.cliente\n");
        sql.append(" INNER JOIN pessoa p ON p.codigo = sc.codigopessoa\n");
        sql.append(" LEFT JOIN controlecreditotreino cc ON cc.reposicao = r.codigo\n");
        sql.append(" LEFT JOIN agenda ag ON ag.reposicao = r.codigo\n");
        sql.append(" LEFT JOIN periodoacessocliente pe on pe.codigo = (select max(codigo)\n");
        sql.append(" from periodoacessocliente\n");
        sql.append(" where pessoa = p.codigo\n");
        sql.append(" and tipoTotalPass is true)\n");
        sql.append(" JOIN colaborador professor on professor.codigo = ht.professor\n");
        sql.append(" JOIN pessoa p2 on p2.codigo = professor.pessoa\n");
        sql.append("WHERE r.datareposicao >= '").append(dataComHoraZerada).append("'\n");
        sql.append(" AND r.datareposicao <= '").append(dataHora2359).append("'\n");
        sql.append(" AND cliente.codigomatricula = ").append(matricula).append(";");

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
            while (rs.next()) {
                if (filtraContrato && !modalidadeFiltrar.contains(rs.getInt("modalidade"))) {
                    continue;
                }
                AulaAlunoDTO aulaAlunoDTO = new AulaAlunoDTO();
                aulaAlunoDTO.setNomeAula(rs.getString("descricaoturma"));
                aulaAlunoDTO.setNomeProfessor(rs.getString("nomeprofessor"));
                aulaAlunoDTO.setDia(rs.getString("datareposicao"));
                aulaAlunoDTO.setHorario(rs.getString("horainicial") + " - " + rs.getString("horafinal"));
                aulaAlunoDTO.setCodigoHorarioTurma(rs.getInt("codigo"));
                aulaAlunoDTO.setAulaTurma(true);
                aulaAluno.add(aulaAlunoDTO);
            }
        }
        return aulaAluno;
    }

    private List<AulaAlunoDTO> consultarTurmasPorPeriodoEMatricula(Integer matricula, Date dataComHoraZerada, Date dataHora2359, Connection conZW) throws Exception {
        List<AulaAlunoDTO> aulaAluno = new ArrayList<>();

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT professor.nome as nomeprofessor, ht.codigo, p.nome, t.descricao, aht.dia, ht.horainicial, ht.horafinal, c.idselfloops ");
        sql.append("FROM alunohorarioturma aht\n ");
        sql.append("INNER JOIN cliente c ON aht.cliente = c.codigo\n ");
        sql.append("INNER JOIN pessoa p ON c.pessoa = p.codigo\n ");
        sql.append("INNER JOIN horarioturma ht ON aht.horarioturma = ht.codigo\n ");
        sql.append("INNER JOIN turma t ON ht.turma = t.codigo\n ");
        sql.append("INNER JOIN colaborador col on ht.professor = col.codigo ");
        sql.append("INNER JOIN pessoa professor on professor.codigo = col.pessoa ");
        sql.append("WHERE c.codigomatricula = ").append(matricula);
        sql.append(" AND dia >= '").append(dataComHoraZerada).append("' AND dia <= '").append(dataHora2359).append("';");

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
            while (rs.next()) {
                AulaAlunoDTO aulaAlunoDTO = new AulaAlunoDTO();
                aulaAlunoDTO.setNomeAula(rs.getString("descricao"));
                aulaAlunoDTO.setNomeProfessor(rs.getString("nomeprofessor"));
                aulaAlunoDTO.setDia(rs.getString("dia"));
                aulaAlunoDTO.setHorario(rs.getString("horainicial") + " - " + rs.getString("horafinal"));
                aulaAlunoDTO.setCodigoHorarioTurma(rs.getInt("codigo"));
                aulaAlunoDTO.setAulaTurma(false);
                if(!UteisValidacao.emptyString(rs.getString("idselfloops")) && temRegistroTurmaHorarioatividade(
                        conZW,
                        aulaAlunoDTO.getCodigoHorarioTurma(),
                        aulaAlunoDTO.getDia(),
                        rs.getString("idselfloops"))) {
                    aulaAlunoDTO.setTemIntegracaoSelfLoops(true);
                }

                aulaAluno.add(aulaAlunoDTO);
            }
        }
        return aulaAluno;
    }

    private Boolean temRegistroTurmaHorarioatividade(Connection connection, Integer horarioId, String diaAula, String userIdSelfloops) {
        String sql = "SELECT * FROM turmahorarioatividadesintegracaoselfloops WHERE horarioturma = ? AND diaAula::date = ? AND userIdSelfloops = ?";
        try (PreparedStatement select = connection.prepareStatement(sql)) {
            select.setInt(1, horarioId);
            select.setDate(2, Uteis.getDataJDBC(Calendario.getDate("yyyy-MM-dd HH:mm:ss", diaAula)));
            select.setString(3, userIdSelfloops);
            try (ResultSet dados = select.executeQuery()) {
                return dados.next();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    private List<AulaAlunoDTO> consultaMapaTurmaPorPeriodoEMatricula(Integer matricula, Date dataComHoraZerada, Date dataHora2359, Connection conZW, Boolean filtrarPorContrato, List<Integer> modalidadeFiltrar) throws Exception {
        List<AulaAlunoDTO> aulaAluno = new ArrayList<>();

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT t.descricao as descricaoturma, m.codigo as modalidade, ht.codigo, pessoa_colaborador.nome as nomeprofessor, diasemananumero, horainicial, horafinal, datainicio, datafim FROM matriculaalunohorarioturma ma\n ");
        sql.append("INNER JOIN pessoa p ON p.codigo = ma.pessoa\n ");
        sql.append("INNER JOIN cliente cli ON cli.pessoa = ma.pessoa\n ");
        sql.append("INNER JOIN horarioturma ht ON ht.codigo = ma.horarioturma\n ");
        sql.append("INNER JOIN turma t ON t.codigo = ht.turma\n ");
        sql.append("INNER JOIN modalidade m ON t.modalidade = m.codigo  AND m.utilizarturma \n ");
        sql.append("INNER JOIN colaborador c on ht.professor = c.codigo\n ");
        sql.append("INNER JOIN pessoa pessoa_colaborador on c.pessoa = pessoa_colaborador.codigo\n ");
        sql.append("WHERE cli.codigomatricula = ").append(matricula);
        sql.append(" AND  ((datafim >= '").append(dataComHoraZerada).append("' AND datafim <= '").append(dataHora2359);
        sql.append("') OR (datainicio >= '").append(dataComHoraZerada).append("' AND datainicio <= '").append(dataHora2359);
        sql.append("') OR (datainicio < '").append(dataComHoraZerada).append("' AND datafim > '").append(dataHora2359).append("'))");

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
            while (rs.next()) {
                if (filtrarPorContrato && !modalidadeFiltrar.contains(rs.getInt("modalidade"))) {
                    continue;
                }
                AulaAlunoDTO aulaAlunoDTO = new AulaAlunoDTO();
                aulaAlunoDTO.setNomeAula(rs.getString("descricaoturma"));
                aulaAlunoDTO.setNomeProfessor(rs.getString("nomeprofessor"));
                aulaAlunoDTO.setCodigoHorarioTurma(rs.getInt("codigo"));

                Integer diaSemana = rs.getInt("diasemananumero");
                String horario = rs.getString("horainicial") + " - " + rs.getString("horafinal");

                if (diaSemana >= 1 && diaSemana <= 7) { // 1 é domingo, 7 é sábado
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    Date dataInicioAulas = sdf.parse(rs.getString("datainicio"));
                    Date dataFimAulas = sdf.parse(rs.getString("datafim"));
                    Date dataFinalComparar = dataFimAulas.compareTo(dataHora2359) < 0 ? dataFimAulas : dataHora2359;

                    if (dataInicioAulas.compareTo(dataComHoraZerada) <= 0) {

                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(dataComHoraZerada);

                        while (!calendar.getTime().after(dataFinalComparar)) {
                            if (calendar.get(Calendar.DAY_OF_WEEK) == diaSemana) {
                                AulaAlunoDTO alunoDTO = new AulaAlunoDTO();
                                alunoDTO.setNomeAula(aulaAlunoDTO.getNomeAula());
                                alunoDTO.setNomeProfessor(aulaAlunoDTO.getNomeProfessor());
                                alunoDTO.setCodigoHorarioTurma(aulaAlunoDTO.getCodigoHorarioTurma());
                                alunoDTO.setDia(sdf.format(calendar.getTime()));
                                alunoDTO.setHorario(horario);
                                alunoDTO.setAulaTurma(true);
                                aulaAluno.add(alunoDTO);

                                calendar.add(Calendar.DAY_OF_MONTH, 7);
                                continue;
                            }

                            calendar.add(Calendar.DAY_OF_MONTH, 1);
                        }
                    }
                }
            }
        }
        return aulaAluno;
    }

    private Collection<GrupoTrabalhadoItemDTO> preencherGruposMusculares(List<Map<String, Object>> grupos) throws Exception {
        Collection<GrupoTrabalhadoItemDTO> collection = new ArrayList<GrupoTrabalhadoItemDTO>();
        int total = 0;
        for (Map<String, Object> map1 : grupos) {
            total = total + Integer.parseInt(map1.get("series").toString());
        }
        for (Map<String, Object> map : grupos) {

            collection.add(new GrupoTrabalhadoItemDTO(map.get("grupo").toString(), Double.parseDouble(map.get("series").toString()), total));
        }
        return collection;
    }


    @Override
    public void enviarProgramaTreino(Integer empresa,
                                     Integer codigoPrograma,
                                     List<Integer> clientes) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
        String idOperacaoEmMassa = "OP_" + usuario.getCodigo() + "_" + new Date().getTime();
        ProgramaTreino programaBase = obterPorId(ctx, codigoPrograma);
        if (programaBase == null) {
            throw new ServiceException(ProgramaTreinoExcecoes.PROGRAMA_TREINO_NAO_ENCONTRADO);
        }
        if(usuario != null && usuario.getIdPessoa() != null ){
            ProfessorSintetico p = professorSinteticoService.obterPorCodigoPessoaZW(ctx, usuario.getIdPessoa(), empresa);
            if(p != null){
                programaBase.setProfessorMontou(p);
            }
        }
        System.out.println("Começando a enviar o programa aos " + clientes.size() + " alunos");
        new Thread() {
            @Override
            public void run() {
                try {
                    for (Integer clienteCod : clientes) {
                        ClienteSintetico cliente = clienteSinteticoService.consultarPorMatricula(ctx, String.valueOf(clienteCod));
                        System.out.println("Enviar treino ao aluno " + cliente.getNome());
                        try {
                            ProgramaTreino novoPrograma = criarProximoProgramaTreino(ctx, usuario, cliente, programaBase);
                            novoPrograma.setIdOperacaoEmMassa(idOperacaoEmMassa);
                            novoPrograma = inserir(ctx, novoPrograma, false);

                            List<ProgramaTreinoFicha> fichas = obterFichaPorPrograma(ctx, programaBase.getCodigo(), null, true);
                            fichas = Ordenacao.ordenarLista(fichas, "codigo");
                            for (ProgramaTreinoFicha f : fichas) {
                                List<AtividadeFicha> atividadesFicha = fichaService.obterAtividadesFicha(ctx, f.getFicha().getCodigo());
                                Ficha ficha = new Ficha(f.getFicha(), atividadesFicha, false);
                                gravarFichaSemTratarExcecao(ctx, true,
                                        f.getTipoExecucao(),
                                        f.getDiaSemana(),
                                        novoPrograma,
                                        ficha,
                                        f.getFicha().getCategoria() == null ? null : f.getFicha().getCategoria().getCodigo(),
                                        f, true, usuario.getProfessor(), false);
                            }

                            novoPrograma.setProgramaFichas(programaTreinoFichaDao.obterPorProgramaTreino(ctx, novoPrograma.getCodigo()));
                            atualizarDadoSintetico(ctx, novoPrograma, empresa);
                            ProgramaTreino finalPt = novoPrograma;
                            novoPrograma.getProgramaFichas().forEach(pF -> {
                                if (!isNull(pF.getFicha())) {
                                    incluirLog(ctx, pF.getFicha().getCodigo().toString(), finalPt.getCodigo().toString(), "", pF.getFicha().getDescricaoParaLog(null), "INCLUSÃO", "INCLUSÃO DE FICHA",
                                            EntidadeLogEnum.FICHA, "Ficha", usuario.getUserName(), logDao, null, null);
                                }
                            });
                            if (novoPrograma.getCliente() != null) {
                                incluirLog(ctx, novoPrograma.getCodigo().toString(), novoPrograma.getCliente().getCodigo().toString(), "",
                                        novoPrograma.getDescricaoParaLog(null), "INCLUSÃO", "INCLUSÃO DE PROGRAMA VIA ENVIAR TREINO AO ALUNO",
                                        EntidadeLogEnum.PROGRAMA, "Programa", usuario.getUserName(), logDao, null, null);
                            }
                            System.out.println("Terminei de enviar treino");
                        } catch (Exception e) {
                            System.out.println("Erro ao enviar treino ao aluno " + cliente.getNome());
                            Uteis.logar(e, ProgramaTreinoServiceImpl.class);
                        }

                    }
                } catch (Exception e) {
                    Uteis.logar(e, this.getClass());
                }
            }
        }.start();
    }

    @Override
    public synchronized String atualizarBancoAtividadesIA(String ctx) throws Exception {
        if (UteisValidacao.emptyString(ctx)) {
            ctx = sessaoService.getUsuarioAtual().getChave();
        }

        ConfiguracaoSistema valorAntesConfigPermitirCriarTreinoIa = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA);
        boolean configAntesPermitirCriarTreinoIa = valorAntesConfigPermitirCriarTreinoIa != null && Boolean.parseBoolean(valorAntesConfigPermitirCriarTreinoIa.getValor());

        if (!configAntesPermitirCriarTreinoIa) {
            throw new ServiceException("A configuração 'Permitir criação de treino automatizado (I.A)' está desabilitada. Por isso, esta ação foi interrompida!");
        }

        if (UteisValidacao.emptyString(ctx)) {
            throw new ServiceException("A chave não foi localizada e não pode prosseguir sem, o processo será encerrado");
        }

        Uteis.logarDebug("#### INICIANDO A IMPORTAÇÃO DE ATIVIDADES DA IA PARA CTX: " + ctx);
        String exportUrl = Aplicacao.getProp(Aplicacao.urlTreinoIa) + "/export-exercises";
        CloseableHttpClient clientExport = HttpClients.createDefault();
        HttpGet getExport = new HttpGet(exportUrl);
        getExport.setHeader("Content-Type", "application/json");
        getExport.setHeader("access-token", Aplicacao.getProp(Aplicacao.accessTokenTreinoIa));
        getExport.setHeader("zw-key", ctx);

        HttpResponse responseExport = clientExport.execute(getExport);
        String responseExportString = EntityUtils.toString(responseExport.getEntity(), StandardCharsets.UTF_8);
        int statusExport = responseExport.getStatusLine().getStatusCode();
        if (statusExport != HttpStatus.SC_OK) {
            throw new RuntimeException("Erro ao exportar atividades: HTTP " + statusExport);
        }

        List<GrupoMuscular> grupoMuscularList = grupoMuscularDao.findAll(ctx);
        List<AtividadeGrupoMuscular> atividadeGrupoMuscularList = atividadeGrupoMuscularDao.findAll(ctx);
        Map<Atividade, Set<GrupoMuscular>> atividadeGrupoMap = atividadeGrupoMuscularList.stream()
                .collect(Collectors.groupingBy(
                        AtividadeGrupoMuscular::getAtividade,
                        Collectors.mapping(AtividadeGrupoMuscular::getGrupoMuscular, Collectors.toSet())
                ));

        JSONArray atividadesExportadas = new JSONArray(responseExportString);
        JSONArray atividadesIaComAtvAlternativas = new JSONArray();
        for (int i = 0; i < atividadesExportadas.length(); i++) {
            JSONObject atividadeJson = atividadesExportadas.getJSONObject(i);
            String idIA = atividadeJson.getString("exercise_id");
            String nomeAtividadeIa = atividadeJson.getString("exercise_name");
            try {
                JSONArray instructions = atividadeJson.getJSONArray("instructions");
                String descricaoAtividade = "";
                for (int j = 0; j < instructions.length(); j++) {
                    descricaoAtividade += instructions.getString(j) + " \n";
                }
                JSONArray atividadesAlternativa = atividadeJson.getJSONArray("similar_exercises");
                if (atividadesAlternativa != null && atividadesAlternativa.length() > 0) {
                    atividadesIaComAtvAlternativas.put(atividadeJson);
                }
                String tipoAtividade = atividadeJson.getString("system_targeted");
                String urlImg = atividadeJson.getString("gif_url");
                Atividade atividade = atividadeService.obterPorIdIAVersaoDois(ctx, Integer.parseInt(idIA));
                if (atividade == null || UteisValidacao.emptyNumber(atividade.getCodigo())) {
                    Atividade novaAtividade = new Atividade();
                    Atividade atividadePorNome = atividadeService.buscarPorNomeAtividade(ctx, nomeAtividadeIa);
                    if (atividadePorNome != null && atividadePorNome.getCodigo() != null) {
                        // alternativa para não conflitar devido nome de atividade ser único e não poder sobrescrever uma atividade do banco por uma da ia
                        novaAtividade.setNome(" " + nomeAtividadeIa);
                    } else {
                        novaAtividade.setNome(nomeAtividadeIa);
                    }
                    novaAtividade.setDescricao(descricaoAtividade);
                    novaAtividade.setNomeOriginalIA(nomeAtividadeIa);
                    novaAtividade.setTipo(TipoAtividadeEnum.getFromDescricao(tipoAtividade));
                    novaAtividade.setVersao(0);
                    novaAtividade.setIdIA2(Integer.parseInt(idIA));
                    atividade = atividadeService.inserir(ctx, novaAtividade);

                    incluirLog(ctx, atividade.getCodigo().toString(), "", "",
                            atividade.getDescricaoParaLog(null), "INCLUSÃO", "INCLUSÃO DE ATIVIDADE",
                            EntidadeLogEnum.ATIVIDADE, "Atividade", "Sincronizador Treino IA", logDao, true, null, null);
                } else {
                    Atividade atvAntesAlteracao = atividade.clone();
                    atividade.setNome(nomeAtividadeIa);
                    atividade.setNomeOriginalIA(nomeAtividadeIa);
                    atividade.setDescricao(descricaoAtividade);
                    atividade.setTipo(TipoAtividadeEnum.getFromDescricao(tipoAtividade));
                    atividade.setVersao(atividade.getVersao() + 1);
                    atividadeService.alterar(ctx, atividade);

                    incluirLog(ctx, atividade.getCodigo().toString(), "",
                            atvAntesAlteracao.getDescricaoParaLog(atividade),
                            atividade.getDescricaoParaLog(atvAntesAlteracao),
                            "ALTERAÇÃO", "ALTERAÇÃO DE ATIVIDADE", EntidadeLogEnum.ATIVIDADE, "Atividade",
                            "Sincronizador Treino IA", logDao, true, null, null);
                }

                // SINCRONIZAR IMAGENS
                if (!UteisValidacao.emptyString(urlImg) && atividade != null && !UteisValidacao.emptyNumber(atividade.getCodigo())) {
                    // para limpar qualquer fotokey incorreto já presente em produção
                    atividadeAnimacaoDao.deleteFotoKeyAtividade(ctx, atividade.getCodigo());
                    byte[] imagemBase64 = converteImagemParaBytes(urlImg);
                    List<AtividadeImagemUploadTO> atividadeImagemUploadTOList = new ArrayList<>();
                    AtividadeImagemUploadTO atividadeImagemUploadTO = new AtividadeImagemUploadTO();
                    atividadeImagemUploadTO.setData(imagemBase64);
                    atividadeImagemUploadTO.setNome("imagem-ia-" + atividade.getCodigo() + ".gif");
                    atividadeImagemUploadTOList.add(atividadeImagemUploadTO);
                    atividadeService.salvarMidiaNuvemEndpoint(ctx, atividadeImagemUploadTOList, atividade);
                }

                // SINCRONIZAR GRUPOS MUSCULARES
                if (atividade != null && !UteisValidacao.emptyNumber(atividade.getCodigo())) {
                    // NO MOMENTO DESTA IMPLEMENTAÇÃO A IA RETORNA APENAS UM GRUPO MUSCULAR POR ATIVIDADE COMO UMA STRING
                    String grupoMuscularAtvIa = atividadeJson.getString("mapped_target_muscle");
                    if (!UteisValidacao.emptyString(grupoMuscularAtvIa)) {
                        final String normalizedGrupoMuscularAtvIa = Normalizer.normalize(grupoMuscularAtvIa.trim(), Normalizer.Form.NFD)
                                .replaceAll("\\p{M}", "").toLowerCase();
                        Optional<GrupoMuscular> matchedGrupoMuscular = grupoMuscularList.stream()
                                .filter(grupoMuscular -> Normalizer.normalize(grupoMuscular.getNome().trim(), Normalizer.Form.NFD)
                                        .replaceAll("\\p{M}", "").toLowerCase()
                                        .equals(normalizedGrupoMuscularAtvIa))
                                .findFirst();
                        if (matchedGrupoMuscular.isPresent()) {
                            GrupoMuscular grupoEncontrado = matchedGrupoMuscular.get();
                            Set<GrupoMuscular> gruposAssociados = atividadeGrupoMap.getOrDefault(atividade, new HashSet<>());
                            // ATUALIZAR GRUPOS MUSCULARES APENAS QUANDO NÃO LOCALIZAR
                            if (!gruposAssociados.contains(grupoEncontrado)) {
                                AtividadeGrupoMuscular atividadeGrupoMuscular = new AtividadeGrupoMuscular();
                                atividadeGrupoMuscular.setAtividade(atividade);
                                atividadeGrupoMuscular.setGrupoMuscular(grupoEncontrado);
                                atividadeGrupoMuscularDao.insert(ctx, atividadeGrupoMuscular);
                                atividadeGrupoMap.computeIfAbsent(atividade, k -> new HashSet<>()).add(grupoEncontrado);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                Uteis.logarDebug("### [ProgramaTreinoServiceImpl.atualizarBancoAtividadesIA] ERRO AO ATUALIZAR BANCO DE ATIVIDADES DA IA ID: " + idIA + " - NOME: " + nomeAtividadeIa + " - ERRO:" + e.getMessage());
            }
        }

        // Após registrar todas atividades em banco para ter a atividade registrada e conseguir obter, atualizar as que possuem atividade alternativa
        if (atividadesIaComAtvAlternativas != null && atividadesIaComAtvAlternativas.length() > 0) {
            for (int i = 0; i < atividadesIaComAtvAlternativas.length(); i++) {
                JSONObject atividadeJson = atividadesIaComAtvAlternativas.getJSONObject(i);
                String idIA = atividadeJson.getString("exercise_id");
                String nomeAtividadeIa = atividadeJson.getString("exercise_name");
                try {
                    Atividade atividade = atividadeService.obterPorIdIAVersaoDois(ctx, Integer.parseInt(idIA));

                    if (atividade != null && !UteisValidacao.emptyNumber(atividade.getCodigo())) {
                        JSONArray atividadesAlternativa = atividadeJson.getJSONArray("similar_exercises");

                        if (atividadesAlternativa != null && atividadesAlternativa.length() > 0) {
                            for (int j = 0; j < atividadesAlternativa.length(); j++) {
                                Integer idIAAtividadeAlternativa = Integer.parseInt(atividadesAlternativa.getString(j));
                                Atividade atividadeAlternativa = atividadeService.obterPorIdIAVersaoDois(ctx, idIAAtividadeAlternativa);

                                if (atividadeAlternativa != null && !UteisValidacao.emptyNumber(atividadeAlternativa.getCodigo())) {
                                    boolean jaExiste = atividadeAlternativaDao.existsByAtividadeAndAtividadeAlternativa(
                                            ctx,
                                            atividade.getCodigo(),
                                            atividadeAlternativa.getCodigo()
                                    );

                                    if (!jaExiste) {
                                        AtividadeAlternativa relacaoAlternativa = new AtividadeAlternativa();
                                        relacaoAlternativa.setAtividade(atividade);
                                        relacaoAlternativa.setAtividadeAlternativa(atividadeAlternativa.getCodigo());
                                        atividadeAlternativaDao.insert(ctx, relacaoAlternativa);
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    Uteis.logarDebug("### [ProgramaTreinoServiceImpl.atualizarBancoAtividadesIA] ERRO AO REGISTRAR ATIVIDADE ALTERNATIVA: " + idIA + " - NOME: " + nomeAtividadeIa + " - ERRO:" + e.getMessage());
                }
            }
        }

        Uteis.logarDebug("#### TERMINANDO A IMPORTAÇÃO DE ATIVIDADES DA IA PARA CTX: " + ctx);
        return "OK";
    }

    @Override
    public synchronized void registrarWorkoutTreinoIa(String ctx, Integer nivelIniciante, Integer nivelIntermediario, Integer nivelAvancado) throws Exception {
        String apiUrl = Aplicacao.getProp(Aplicacao.urlTreinoIa) +  "/register-workout-split";

        List<String> allowedSplits = Arrays.asList("A", "AB", "ABC", "ABCD", "ABCDE", "ABCDEF");

        if (nivelIniciante == null || nivelIntermediario == null || nivelAvancado == null) {
            throw new IllegalArgumentException("Os níveis não podem ser nulos.");
        }
        if (nivelIniciante < 0 || nivelIntermediario < 0 || nivelAvancado < 0) {
            throw new IllegalArgumentException("Os níveis devem ser maiores ou iguais a zero.");
        }

        JSONArray beginnerArray = gerarSplits(allowedSplits, nivelIniciante);
        JSONArray intermediateArray = gerarSplits(allowedSplits, nivelIntermediario);
        JSONArray advancedArray = gerarSplits(allowedSplits, nivelAvancado);

        JSONObject body = new JSONObject();
        body.put("beginner", beginnerArray);
        body.put("intermediate", intermediateArray);
        body.put("advanced", advancedArray);

        String zwKey = ctx;
        Integer codigoZw = sessaoService.getUsuarioAtual().getEmpresaAtual();

        if (codigoZw != null && codigoZw > 0) {
            zwKey = ctx + "-" + codigoZw;
        }

        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPost post = new HttpPost(apiUrl);
            post.setHeader("Content-Type", "application/json");
            post.setHeader("access-token", Aplicacao.getProp(Aplicacao.accessTokenTreinoIa));
            post.setHeader("zw-key", zwKey);

            post.setEntity(new StringEntity(body.toString(), StandardCharsets.UTF_8));

            HttpResponse response = client.execute(post);
            String responseString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            if (response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
                throw new RuntimeException("Falha ao registrar workout split: " + response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("[ProgramaTreinoServiceImpl.registrarWorkoutTreinoIa] Erro ao tentar registrar workout treino ia: " + e.getMessage());
        }
    }


    private JSONArray gerarSplits(List<String> allowedSplits, int nivel) {
        JSONArray splitsArray = new JSONArray();
        int limite = Math.min(nivel, allowedSplits.size());
        for (int i = 0; i < limite; i++) {
            splitsArray.put(allowedSplits.get(i));
        }
        return splitsArray;
    }

}
