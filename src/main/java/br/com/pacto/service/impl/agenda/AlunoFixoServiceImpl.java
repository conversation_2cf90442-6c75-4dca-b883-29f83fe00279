package br.com.pacto.service.impl.agenda;

import br.com.pacto.bean.log.Log;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.AgendaExcecoes;
import br.com.pacto.service.intf.agenda.AlunoFixoService;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import br.com.pacto.util.enumeradores.OrigemSistemaEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Types;
import java.util.*;

@Service
public class AlunoFixoServiceImpl implements AlunoFixoService {

    private ConexaoZWService conexaoZWService;
    private SessaoService sessaoService;

    @Autowired
    public AlunoFixoServiceImpl(ConexaoZWService conexaoZWService, SessaoService sessaoService) {
        this.conexaoZWService = conexaoZWService;
        this.sessaoService = sessaoService;
    }

    public void desafixarAlunoTurma(Integer aulaHorario, String matricula, Long diaDesafixar) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            try (Connection conZW = conexaoZWService.conexaoZw(ctx);
                 ResultSet rsAlunoFixo = ConexaoZWServiceImpl.criarConsulta(
                         "select a.codigo, c.codigo as cliente, p.nome, p.codigo as pessoa from alunofixoaula a \n" +
                                 " inner join cliente c on a.cliente = c.codigo \n" +
                                 " inner join pessoa p on c.pessoa = p.codigo \n" +
                                 " where dataremovido is null \n" +
                                 " and c.codigomatricula = " + matricula +
                                 " and a.horarioturma = " + aulaHorario, conZW)) {
                boolean gerouLog = false;
                while (rsAlunoFixo.next()) {
                    Integer alunoFixoId = rsAlunoFixo.getInt("codigo");
                    Integer alunoId = rsAlunoFixo.getInt("cliente");
                    Integer pessoaId = rsAlunoFixo.getInt("pessoa");
                    String nome = rsAlunoFixo.getString("nome");
                    PreparedStatement stm = conZW.prepareStatement("update alunofixoaula set dataremovido = ?, " +
                            "usuarioremoveu = ? where codigo = ?");
                    stm.setTimestamp(1, new java.sql.Timestamp(Calendario.hoje().getTime()));
                    stm.setInt(2, sessaoService.getUsuarioAtual().getId());
                    stm.setInt(3, alunoFixoId);
                    stm.execute();

                    try (ResultSet rsAulasFixas = ConexaoZWServiceImpl.criarConsulta(
                            "select a.codigo, a.dia from alunohorarioturma a " +
                                    " where alunofixoaula = " + alunoFixoId +
                                    " and a.dia >= '" + Uteis.getDataAplicandoFormatacao(new Date(diaDesafixar), "yyyy-MM-dd") + "'", conZW)) {
                        while (rsAulasFixas.next()) {
                            gerarLogDesafixadoAula(conZW, aulaHorario, rsAulasFixas.getDate("dia"), matricula, nome);
                            conZW.prepareStatement("delete from alunoHorarioTurma where codigo = " + rsAulasFixas.getInt("codigo")).execute();
                        }
                    }

                    if (!gerouLog) {
                        gerarLogDesafixado(conZW,
                                aulaHorario,
                                alunoId,
                                pessoaId,
                                matricula,
                                nome);
                        gerouLog = true;
                    }
                }
            }
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_ATUALIZAR_ALUNO_AULA, e);
        }
    }

    public List<Date> validarAulasCheias(Integer aulaHorario, Integer codigoCliente,
                                           Date dataAula,
                                           Date ate,
                                           Connection conZW) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select h.nrmaximoaluno, cast (a.dia as date) as diaaula, count(a.codigo) as alunos from alunohorarioturma a ");
        sql.append(" inner join horarioturma h on h.codigo = a.horarioturma ");
        sql.append(" where dia between '");
        sql.append(Uteis.getDataAplicandoFormatacao(dataAula, "yyyy-MM-dd"));
        sql.append("' and '");
        sql.append(Uteis.getDataAplicandoFormatacao(ate, "yyyy-MM-dd"));
        sql.append("' and cliente <> ").append(codigoCliente);
        sql.append(" and horarioturma = ").append(aulaHorario);
        sql.append(" group by nrmaximoaluno, a.dia ");
        List<Date> aulasCheias = new ArrayList<>();
        ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW);
        while (rs.next()){
            int nrmaximoaluno = rs.getInt("nrmaximoaluno");
            int alunos = rs.getInt("alunos");
            if(nrmaximoaluno <= alunos){
                aulasCheias.add(rs.getDate("diaaula"));
            }
        }
        return aulasCheias;
    }
    public void fixarAlunoTurma(Integer aulaHorario, String matricula,
                                Long dataAula,
                                Long ate, TipoFixarAluno tipo, String origem, Boolean validarAulaCheia) throws ValidacaoException, ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            try (Connection conZW = conexaoZWService.conexaoZw(ctx);
                 ResultSet rsAluno = ConexaoZWServiceImpl.criarConsulta(
                         "select c.codigo, p.nome, p.codigo as pessoa from cliente c " +
                                 "inner join pessoa p on p.codigo = c.pessoa " +
                                 "where c.codigomatricula = " + matricula, conZW)) {

                Date dataLimite = null;
                String sqlContratoAluno = "select c.vigenciaateajustada, e.carenciarenovacao from contrato c\n" +
                        " inner join cliente cli on cli.pessoa = c.pessoa \n" +
                        " inner join empresa e on e.codigo = cli.empresa \n" +
                        " where cli.codigomatricula = " + matricula +
                        " order by c.vigenciaateajustada desc limit 1";
                try (ResultSet rsContrato = ConexaoZWServiceImpl.criarConsulta(sqlContratoAluno, conZW)) {
                    if (rsContrato.next()) {
                        int carenciaRenovacao = rsContrato.getInt("carenciarenovacao");
                        Date dataVigencia = rsContrato.getDate("vigenciaateajustada");
                        dataLimite = Uteis.somarDias(dataVigencia, carenciaRenovacao);
                    }

                }
                if (tipo.equals(TipoFixarAluno.DATA_DETERMINADA)
                        && dataLimite != null
                        && ate != null && Calendario.menor(dataLimite, new Date(ate))) {
                    throw new ServiceException("Data limite da fixação tem que ser menor ou igual ao término do contrato do aluno.");
                } else if (tipo.equals(TipoFixarAluno.DATA_DETERMINADA)) {
                    dataLimite = new Date(ate);
                }
                origem = origem == null ? "AG" : origem;
                if (rsAluno.next()) {
                    Integer alunoId = rsAluno.getInt("codigo");
                    Integer pessoaId = rsAluno.getInt("pessoa");
                    String nomeAluno = rsAluno.getString("nome");
                    List<Date> aulasCheias = validarAulasCheias(aulaHorario, alunoId,
                            new Date(dataAula),
                            dataLimite,
                            conZW);
                    if(validarAulaCheia){
                        Collections.sort(aulasCheias);
                        if(!aulasCheias.isEmpty()){
                            Set<String> dates = new HashSet<String>(){{
                                for(Date dia : aulasCheias){
                                    add(Uteis.getDataAplicandoFormatacao(dia, "dd/MM"));
                                }
                            }};
                            throw new ValidacaoException(new ArrayList<>(dates));
                        }
                        return;
                    }

                    PreparedStatement stm = conZW.prepareStatement("insert into alunofixoaula(dataregistro, datafinal, horarioturma," +
                            "cliente, usuario, origem, tipo) values (?,?,?,?,?,?,?) RETURNING codigo");
                    stm.setTimestamp(1, new java.sql.Timestamp(Calendario.hoje().getTime()));
                    stm.setDate(2, Uteis.getDataJDBC(dataLimite));
                    stm.setInt(3, aulaHorario);
                    stm.setInt(4, alunoId);
                    stm.setInt(5, sessaoService.getUsuarioAtual().getId());
                    stm.setString(6, origem);
                    stm.setInt(7, tipo.getCodigo());
                    Integer fixo;
                    try (ResultSet resultSet = stm.executeQuery()) {
                        fixo = null;
                        if (resultSet.next()) {
                            fixo = resultSet.getInt("codigo");
                        }
                    }

                    inserirAlunoAulas(conZW, alunoId,
                            aulaHorario,
                            matricula,
                            nomeAluno,
                            sessaoService.getUsuarioAtual().getId(),
                            new Date(dataAula),
                            dataLimite, fixo, origem, aulasCheias);

                    gerarLogFixado(conZW,
                            aulaHorario,
                            alunoId,
                            pessoaId,
                            dataLimite,
                            matricula,
                            nomeAluno,
                            origem);
                }
            }
        } catch (ValidacaoException ve) {
            throw ve;
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_ATUALIZAR_ALUNO_AULA, e);
        }
    }

    private void inserirAlunoAulas(Connection con,
                                   Integer cliente,
                                   Integer aulaHorario,
                                   String matricula,
                                   String nome,
                                   Integer usuario,
                                   Date aulaFixou,
                                   Date ate,
                                   Integer fixo, String origem, List<Date> aulasCheias) throws Exception {
        if (fixo == null) {
            return;
        }

        try (ResultSet rs = con.prepareStatement("select diasemana from horarioturma h where codigo = " + aulaHorario).executeQuery();
             ResultSet rsDiasComAula = con.prepareStatement("select dia, horarioturma from alunohorarioturma a where cliente =  " + cliente +
                     " and dia > '" + Uteis.getDataJDBC(Calendario.hoje()) + "' ").executeQuery()) {

            ArrayList<Map.Entry<Date, Integer>> diasAulas = new ArrayList<>();
            while (rsDiasComAula.next()) {
                diasAulas.add(new AbstractMap.SimpleEntry<>(rsDiasComAula.getDate("dia"), rsDiasComAula.getInt("horarioturma"))  );
            }

            if (rs.next()) {
                try {
                    con.setAutoCommit(false);
                    String diaSemana = rs.getString("diasemana");
                    Calendar calendar = Calendar.getInstance();
                    while (!calendar.getTime().after(ate)) {
                        Date data = Calendario.getDataComHoraZerada(calendar.getTime());
                        if (Calendario.maiorOuIgual(data, aulaFixou) && getDiaSemanaAbreviado(calendar).equals(diaSemana)) {
                            if (Calendario.igual(aulaFixou, data)) {
                                alterarAlunoAulaColetivaDia(con, cliente, aulaHorario, data, fixo);
                                gerarLogInsercaoAula(con, aulaHorario, data, matricula, nome, origem);
                            } else if (!diasAulas.contains(new AbstractMap.SimpleEntry<>(data,aulaHorario)) && !aulasCheias.contains(data)) {
                                incluirAlunoAulaColetivaDia(con, cliente, aulaHorario, usuario, data, fixo);
                                gerarLogInsercaoAula(con, aulaHorario, data, matricula, nome, origem);
                            }
                        }
                        calendar.add(Calendar.DAY_OF_MONTH, 1);
                    }
                    con.commit();
                } catch (Exception e) {
                    con.rollback();
                    throw new ServiceException(AgendaExcecoes.ERRO_FIXAR_ALUNO_AULA, e);
                } finally {
                    con.setAutoCommit(true);
                }

            }
        }

    }

    private void alterarAlunoAulaColetivaDia(Connection con,
                                             Integer cliente,
                                             Integer aulaHorario,
                                             Date dia,
                                             Integer fixo) throws Exception {
        String sql = "update AlunoHorarioTurma set alunofixoaula = ? where  horarioturma = ? and cliente = ? and dia = ?";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            int i = 0;
            stm.setInt(++i, fixo);
            stm.setInt(++i, aulaHorario);
            stm.setInt(++i, cliente);
            stm.setDate(++i, Uteis.getDataJDBC(dia));
            stm.execute();
        }
    }

    private void incluirAlunoAulaColetivaDia(Connection con,
                                             Integer cliente,
                                             Integer aulaHorario,
                                             Integer usuario,
                                             Date dia,
                                             Integer fixo) throws Exception {
        String sql = "INSERT INTO AlunoHorarioTurma( horarioturma, cliente, dia, " +
                "aulaexperimental, desafio,origemsistema,datalancamento,usuario, autorizado, alunofixoaula) "
                + "VALUES (?, ?, ?, ?, ?,?,?,?,?, ?)";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            int i = 0;
            stm.setInt(++i, aulaHorario);
            stm.setInt(++i, cliente);
            stm.setDate(++i, Uteis.getDataJDBC(dia));
            stm.setBoolean(++i, false);
            stm.setBoolean(++i, false);
            stm.setInt(++i, OrigemSistemaEnum.TREINO.getCodigo());
            stm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            stm.setInt(++i, usuario);
            stm.setNull(++i, 0);
            stm.setInt(++i, fixo);
            stm.execute();
        }
    }


    private static String getDiaSemanaAbreviado(Calendar calendar) {
        switch (calendar.get(Calendar.DAY_OF_WEEK)) {
            case Calendar.MONDAY:
                return "SG";
            case Calendar.TUESDAY:
                return "TR";
            case Calendar.WEDNESDAY:
                return "QA";
            case Calendar.THURSDAY:
                return "QI";
            case Calendar.FRIDAY:
                return "SX";
            case Calendar.SATURDAY:
                return "SB";
            case Calendar.SUNDAY:
                return "DM";
            default:
                return "";
        }
    }

    public void gerarLogInsercaoAula(Connection con,
                                     Integer aulaHorario,
                                     Date dia,
                                     String matricula,
                                     String nome, String origem) throws Exception {
        Log log = new Log();
        log.setNomeEntidade("ALUNO_AULA_COLETIVA");
        log.setNomeEntidadeDescricao("Aluno fixado em aula");
        log.setChavePrimaria(aulaHorario.toString().concat("_").concat(Uteis.getDataAplicandoFormatacao(dia, "dd/MM/yyyy")));
        log.setNomeCampo("Marcação de aula");
        log.setValorCampoAlterado("[matricula:" + matricula + "]<br/>[aluno:" + nome + "]<br/>[origem: " +
                (origem.equals("AG") ? "Fixar aluno na aula coletiva pela agenda" : "Fixar aluno na aula coletiva pela negociação") +
                "]");
        log.setOperacao("INCLUSÃO");
        log.setCliente(0);
        log.setPessoa(0);
        gravarLog(con, log);
    }

    public void gerarLogFixado(Connection con,
                                       Integer aulaHorario,
                                       Integer cliente,
                                       Integer pessoa,
                                       Date dataLimite,
                                       String matricula,
                                       String nome,
                                       String origem) throws Exception {

        try (ResultSet rs = con.prepareStatement("select t.identificador from horarioturma ht " +
                " inner join turma t on t.codigo = ht.turma " +
                " where ht.codigo = " + aulaHorario).executeQuery()){
            if(rs.next()){
                Log log = new Log();
                log.setNomeEntidade("ALUNO_AULA_COLETIVA");
                log.setNomeEntidadeDescricao("Aluno fixado em aula");
                log.setChavePrimaria(String.valueOf(cliente));
                log.setNomeCampo("Aluno fixado em aula");
                log.setValorCampoAlterado("[matricula:" + matricula + "]<br/>[aluno:" + nome + "]<br/>[origem: " +
                        (origem.equals("AG") ? "Fixar aluno na aula pela agenda " : "Fixar aluno na aula pela negociação ")
                        + rs.getString("identificador") + " até "
                        + Uteis.getDataAplicandoFormatacao(dataLimite, "dd/MM/yyyy") + "]");
                log.setOperacao("INCLUSÃO");
                log.setCliente(cliente);
                log.setPessoa(pessoa);
                gravarLog(con, log);
            }
        }
    }

    public void gerarLogDesafixado(Connection con,
                                       Integer aulaHorario,
                                       Integer cliente,
                                       Integer pessoa,
                                       String matricula,
                                       String nome) throws Exception {

        try (ResultSet rs = con.prepareStatement("select t.identificador from horarioturma ht " +
                " inner join turma t on t.codigo = ht.turma " +
                " where ht.codigo = " + aulaHorario).executeQuery()){
            if(rs.next()){
                Log log = new Log();
                log.setNomeEntidade("ALUNO_AULA_COLETIVA");
                log.setNomeEntidadeDescricao("Aluno desafixado em aula");
                log.setChavePrimaria(String.valueOf(cliente));
                log.setNomeCampo("Aluno desafixado em aula");
                log.setValorCampoAlterado("[matricula:" + matricula + "]<br/>[aluno:" + nome + "]<br/>[origem: Desafixar aluno na aula "
                        + rs.getString("identificador") + "]");
                log.setOperacao("EXCLUSÃO");
                log.setCliente(cliente);
                log.setPessoa(pessoa);
                gravarLog(con, log);
            }
        }
    }

    public void gerarLogDesafixadoAula(Connection con,
                                       Integer aulaHorario,
                                       Date dia,
                                       String matricula,
                                       String nome) throws Exception {
        Log log = new Log();
        log.setNomeEntidade("ALUNO_AULA_COLETIVA");
        log.setNomeEntidadeDescricao("Aluno desafixado em aula");
        log.setChavePrimaria(aulaHorario.toString().concat("_").concat(Uteis.getDataAplicandoFormatacao(dia, "dd/MM/yyyy")));
        log.setNomeCampo("Desmarcação de aula");
        log.setValorCampoAlterado("[matricula:" + matricula + "]<br/>[aluno:" + nome + "]<br/>[origem: Desafixar aluno na aula coletiva]");
        log.setOperacao("EXCLUSÃO");
        log.setCliente(0);
        log.setPessoa(0);
        gravarLog(con, log);
    }
    public void gravarLog(Connection con, Log log) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada,\n");
        sql.append(" nomecampo, valorcampoanterior, valorcampoalterado, \n");
        sql.append(" dataalteracao, responsavelalteracao, operacao, pessoa, cliente, nomecampodescricao, tags)  \n");
        sql.append(" VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            int i = 0;
            stm.setString(++i, log.getNomeEntidade());
            stm.setString(++i, log.getNomeEntidadeDescricao());
            stm.setString(++i, log.getChavePrimaria());
            stm.setString(++i, "");
            stm.setString(++i, log.getNomeCampo());
            stm.setString(++i, "");
            stm.setString(++i, log.getValorCampoAlterado());
            stm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            stm.setString(++i, sessaoService.getUsuarioAtual().getUsername());
            stm.setString(++i, log.getOperacao());
            stm.setInt(++i, log.getPessoa());
            stm.setInt(++i, log.getCliente());
            stm.setNull(++i, Types.NULL);
            stm.setNull(++i, Types.NULL);
            stm.execute();
        }

    }
}
