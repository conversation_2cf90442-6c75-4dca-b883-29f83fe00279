/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.aula;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.impl.EntityManagerService;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.aparelho.Aparelho;
import br.com.pacto.bean.aula.*;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.gympass.TipoThreadGympassEnum;
import br.com.pacto.bean.log.Log;
import br.com.pacto.bean.nivel.Nivel;
import br.com.pacto.bean.nivel.NivelTO;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.bean.selfloops.SelfLoopsConfiguracoes;
import br.com.pacto.bean.sincronizacao.TipoRevisaoEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.agendamento.AlunoVinculoAulaEnum;
import br.com.pacto.controller.json.aluno.AlunoResponseTO;
import br.com.pacto.controller.json.aluno.TipoAcessoAulaEnum;
import br.com.pacto.controller.json.ambiente.AmbienteResponseTO;
import br.com.pacto.controller.json.aulaDia.*;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.controller.json.modalidade.ModalidadeResponseTO;
import br.com.pacto.controller.json.modalidade.ModalidadeSimplesDTO;
import br.com.pacto.controller.json.selfloops.CourseScheduleDTO;
import br.com.pacto.controller.json.selfloops.CourseScheduleDetailsDTO;
import br.com.pacto.controller.json.turma.HorarioTurmaResponseDTO;
import br.com.pacto.controller.json.turma.TurmaVideoDTO;
import br.com.pacto.dao.intf.aula.*;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.dao.intf.professor.ProfessorSinteticoDao;
import br.com.pacto.dao.intf.turma.TurmaDao;
import br.com.pacto.dto.ColaboradorDTO;
import br.com.pacto.objeto.*;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.agenda.AulasColetivasModoBDServiceImpl;
import br.com.pacto.service.impl.avaliacao.ResizeImage;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.gympass.json.TurmaGymPassJSON;
import br.com.pacto.service.impl.log.AlteracoesTO;
import br.com.pacto.service.impl.log.LogTO;
import br.com.pacto.service.impl.notificacao.excecao.AmbienteExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.AulaExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.ModalidadeExcecoes;
import br.com.pacto.service.intf.agenda.AgendaService;
import br.com.pacto.service.intf.ambiente.AmbienteService;
import br.com.pacto.service.intf.aparelho.AparelhoService;
import br.com.pacto.service.intf.aula.AulaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.gympass.GymPassBookingService;
import br.com.pacto.service.intf.nivel.NivelService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.selfloops.SelfloopsConfiguracoesService;
import br.com.pacto.service.intf.usuario.FotoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.AgendadoJSON;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.enumeradores.DiasSemana;
import br.com.pacto.util.enumeradores.PaletaCoresEnum;
import br.com.pacto.util.impl.UtilReflection;
import br.com.pacto.util.json.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.json.JSONArray;
import org.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.ContextConfiguration;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;
import servicos.integracao.zw.IntegracaoTurmasWSConsumer;
import servicos.integracao.zw.json.AddClienteJSON;
import servicos.integracao.zw.json.AmbienteJSON;
import servicos.integracao.zw.json.ModalidadeJSON;

import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static br.com.pacto.objeto.Uteis.incluirLog;

import static br.com.pacto.objeto.Uteis.incluirLog;
import static org.apache.commons.lang3.StringUtils.isNotBlank;


/**
 *
 * <AUTHOR>
 */
@Service
@ContextConfiguration(locations = {"/applicationContext.xml"})
public class AulaServiceImpl implements AulaService {

    @Autowired
    private AulaDao aulaDao;
    @Autowired
    private ModalidadeDao modalidadeDao;
    @Autowired
    private AmbienteDao ambienteDao;
    @Autowired
    private AulaDiaDao aulaDiaDao;
    @Autowired
    private ProfessorSubstituidoDao subDao;
    @Autowired
    private AulaAlunoDao aulaAlunoDao;
    @Autowired
    private AulaHorarioDao aulaHorarioDao;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;
    @Autowired
    private AulaDiaExcecaoDao aulaDiaExcecaoDao;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private ProfessorSinteticoDao professorSinteticoDao;
    @Autowired
    private ProfessorSinteticoService professorSinteticoService;
    @Autowired
    private AulaDiaExclusaoDao aulaDiaExclusaoDao;
    @Autowired
    private AgendaService agendaService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private AmbienteService ambienteService;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private ProfessorSubstituidoDao professorSubstituidoDao;
    @Autowired
    private FotoService fotoService;
    @Autowired
    private GymPassBookingService gymPassBookingService;
    @Autowired
    private AulasColetivasModoBDServiceImpl modoBDService;
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    @Autowired
    private NivelService nivelService;
    @Autowired
    private LogDao logDao;
    @Autowired
    private TurmaDao turmaDao;
    @Autowired
    private AparelhoService aparelhoService;
    @Autowired
    private SelfloopsConfiguracoesService selfloopsConfiguracoesService;


    private List<String> horariosSelecionados = new ArrayList<String>();
    public boolean cadastroAula = false;
    private List<TurmaAulaCheiaJSON> turmas;
    List listaAulaFiltrada = new ArrayList();

    public ProfessorSinteticoDao getProfessorSinteticoDao() {
        return professorSinteticoDao;
    }

    public AulaDiaExcecaoDao getAulaDiaExcecaoDao() {
        return aulaDiaExcecaoDao;
    }

    public AulaHorarioDao getAulaHorarioDao() {
        return aulaHorarioDao;
    }

    public AulaAlunoDao getAulaAlunoDao() {
        return aulaAlunoDao;
    }

    public ClienteSinteticoService getClienteSinteticoService() {
        return clienteSinteticoService;
    }

    public AulaDiaExclusaoDao getAulaDiaExclusaoDao() {
        return aulaDiaExclusaoDao;
    }

    public AulaDao getAulaDao() {
        return aulaDao;
    }

    public AulaDiaDao getAulaDiaDao() {
        return aulaDiaDao;
    }

    public ModalidadeDao getModalidadeDao() {
        return modalidadeDao;
    }

    public AmbienteDao getAmbienteDao() {
        return ambienteDao;
    }

    @Override
    public Aula inserir(String ctx, Aula object) throws ServiceException {
        try {
            return getAulaDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Aula obterPorId(String ctx, Integer id) throws ServiceException {
        try {
            return getAulaDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Aula alterar(String ctx, Aula object) throws ServiceException {
        try {
            return getAulaDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluir(String ctx, Aula object) throws ServiceException {
        try {
            getAulaDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Aula> obterTodos(String ctx) throws ServiceException {
        try {
            return getAulaDao().findListByAttributes(ctx, null, null, null, 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Aula> obterPorParam(String ctx, String query, Map<String, Object> params) throws ServiceException {
        try {
            return getAulaDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Aula> obterPorParam(String ctx, String query, Map<String, Object> params, int max, int index) throws ServiceException {
        try {
            return getAulaDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Aula obterObjetoPorParam(String ctx, String query, Map<String, Object> params) throws ServiceException {
        try {
            return getAulaDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private void validarDadosAula(final Aula aula,
            final List<String> diasSelecionados,
            final List<String> horariosSelecionados) throws ValidacaoException {
        List<String> mensagens = new ArrayList<String>();

        if (diasSelecionados == null || diasSelecionados.isEmpty()) {
            mensagens.add("diaSemanaObrigatorios");
        }

        if (horariosSelecionados == null || horariosSelecionados.isEmpty()) {
            mensagens.add("horariosObrigatorios");
        }
        if (aula.getModalidade() == null
                || aula.getModalidade().getCodigo() == null
                || aula.getModalidade().getCodigo() == 0) {

            mensagens.add("modalidadeObrigatoria");
        }
        if (aula.getProfessor() == null
                || aula.getProfessor().getCodigo() == null
                || aula.getProfessor().getCodigo() == 0) {
            mensagens.add("professorObrigatorio");
        }
        if (aula.getAmbiente() == null
                || aula.getAmbiente().getCodigo() == null
                || aula.getAmbiente().getCodigo() == 0) {
            mensagens.add("ambienteObrigatorio");
        }
        if (aula.getDataInicio() == null) {
            mensagens.add("dataInicioObrigatorio");
        }
        if (aula.getDataFim() == null) {
            mensagens.add("dataInicioObrigatorio");
        }
        if (aula.getCapacidade() == null) {
            mensagens.add("capacidadeObrigatorio");
        }
        if (aula.getPontosBonus() == null) {
            mensagens.add("pontosObrigatorio");
        }
        if (aula.getBonificacao() == null) {
            mensagens.add("bonificacaoObrigatorio");
        }
        if (aula.getMeta() == null) {
            mensagens.add("metaObrigatorio");
        }
        if (!mensagens.isEmpty()) {
            throw new ValidacaoException(mensagens);
        }
    }

    private void validarDadosAulaNovo(final Aula aula,
                                  final List<String> diasSelecionados,
                                  final List<AulaHorario> horariosSelecionados) throws ValidacaoException {
        List<String> mensagens = new ArrayList<String>();

        if (diasSelecionados == null || diasSelecionados.isEmpty()) {
            mensagens.add("diaSemanaObrigatorios");
        }

        if (horariosSelecionados == null || horariosSelecionados.isEmpty()) {
            mensagens.add("horariosObrigatorios");
        }
        if (aula.getModalidade() == null
                || aula.getModalidade().getCodigo() == null
                || aula.getModalidade().getCodigo() == 0) {

            mensagens.add("modalidadeObrigatoria");
        }
        if (aula.getProfessor() == null
                || aula.getProfessor().getCodigo() == null
                || aula.getProfessor().getCodigo() == 0) {
            mensagens.add("professorObrigatorio");
        }
        if (aula.getAmbiente() == null
                || aula.getAmbiente().getCodigo() == null
                || aula.getAmbiente().getCodigo() == 0) {
            mensagens.add("ambienteObrigatorio");
        }
        if (aula.getDataInicio() == null) {
            mensagens.add("dataInicioObrigatorio");
        }
        if (aula.getDataFim() == null) {
            mensagens.add("dataFimObrigatorio");
        }
        if (aula.getCapacidade() == null) {
            mensagens.add("capacidadeObrigatorio");
        }
        if(aula.getMinutosTolerancia() == null){
            mensagens.add("minutosToleranciaObrigatorio");
        }

        if (!mensagens.isEmpty()) {
            throw new ValidacaoException(mensagens);
        }
    }

    @Override
    public void inserirAula(final String key, final Aula aula, final List<String> diasSelecionados,
            final List<AulaHorario> horariosSelecionados, final List<String> horariosExcluir) throws ValidacaoException, ServiceException {
        try {
            List<String> aulasGerar = new ArrayList<String>();
            validarDadosAulaNovo(aula, diasSelecionados, horariosSelecionados);
            if (aula.getCodigo() != null && aula.getCodigo() != 0) {
                List<AulaHorario> horarios = new ArrayList<AulaHorario>(aula.getHorarios());
                for(AulaHorario aulaHorario : horarios){
                    String periodo = aulaHorario.getInicio()+" - "+aulaHorario.getFim();
                    if(horariosExcluir.contains(periodo)){
                        aula.getHorarios().remove(aulaHorario);
                        aulaHorario.setAtivo(Boolean.FALSE);
                        getAulaHorarioDao().update(key, aulaHorario);
                    }
                }
            }
            aula.setarDias(diasSelecionados);
            if (aula.getCodigo() == null || aula.getCodigo() == 0) {
                getAulaDao().insert(key, aula);
            } else {
                getAulaDao().update(key, aula);
            }
            aula.setModalidade(getModalidadeDao().findById(key, aula.getModalidade().getCodigo()));
            aula.setAmbiente(getAmbienteDao().findById(key, aula.getAmbiente().getCodigo()));
            aula.setProfessor(getProfessorSinteticoDao().findById(key, aula.getProfessor().getCodigo()));
            aula.setHorarios(obterHorarios(key, aula));
//            gerarAulasDiarias(key, aula, diasSelecionados, aulasGerar);
            updateAulas(key, aula);
        } catch (ValidacaoException e) {
            throw new ValidacaoException(e.getMensagens());
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public void updateAulas(final String key, final Aula aula) throws ValidacaoException, ServiceException {
        try{
            if(aula.getCodigo() != null && aula.getCodigo() > 0){
                aulaDiaDao.executeNativeSQL(key, "UPDATE auladia set professor_codigo = "
                        + aula.getProfessor().getCodigo()
                        + " where aula_codigo = "
                        + aula.getCodigo()
                        + " and inicio > '"
                        + Uteis.getDataJDBCTimestamp(Calendario.hoje())
                        +"'");
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public void deleteAulas(final String key, final AulaHorario aula) throws ValidacaoException, ServiceException {
        try{
            if(aula.getCodigo() != null && aula.getCodigo() > 0){
                aulaDiaDao.executeNativeSQL(key, "DELETE FROM auladia WHERE aula_codigo = "
                        + aula.getCodigo()
                        + " AND codigo NOT IN (select aula_codigo from aulaaluno)  "
                        + " and inicio > '"
                        + Uteis.getDataJDBCTimestamp(Calendario.hoje())
                        +"'");
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public void excluirAula(final String key, final Aula aula) throws ServiceException {
        try {
            Integer codigoAula = aula.getCodigo();
            verificarAulaComAluno(key, aula);
            getAulaDiaDao().deleteComParam(key, new String[]{"aula.codigo"}, new Object[]{aula.getCodigo()});
            getAulaHorarioDao().deleteComParam(key, new String[]{"aula.codigo"}, new Object[]{aula.getCodigo()});

            professorSubstituidoDao.removerPorAula(key, aula.getCodigo());
            getAulaDao().delete(key, aula);
            agendaService.iniciaThreadGympass(key, codigoAula, 0, 0, TipoThreadGympassEnum.EXCLUIR_TURMA);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public void verificarAulaComAluno(final String ctx, final Aula aula) throws Exception {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("aula", aula.getCodigo());
        List<AulaAluno> objs = getAulaAlunoDao().findByParam(ctx, "SELECT obj FROM AulaAluno obj WHERE obj.horario.aula.codigo = :aula", params);
        if (objs != null && !objs.isEmpty()) {
            throw new Exception("aulaTemAlunoNaoExcluir");
        }
    }

    @Override
    public void verificarHorarioComAlunoFuturo(final String ctx, final Aula aula, final String periodo, final Date data) throws ValidacaoException, ServiceException {
        try {
            if (periodo != null && !periodo.isEmpty() && periodo.length() >= 13) {
                Map<String, Object> params = new HashMap<String, Object>();
                params.put("aula", aula.getCodigo());
                params.put("inicio", periodo.substring(0, 5));
                params.put("fim", periodo.substring(8, 13));
                AulaHorario aulaHorario = getAulaHorarioDao().findObjectByParam(ctx,
                        "SELECT obj FROM AulaHorario obj WHERE obj.aula.codigo = :aula "
                        + "AND obj.inicio = :inicio AND obj.fim = :fim", params);
                if (aulaHorario != null && aulaHorario.getCodigo() != null && !aula.getCodigo().equals(0)) {
                    Map<String, Object> paramsConsultaAluno = new HashMap<String, Object>();
                    paramsConsultaAluno.put("aulaHorario", aulaHorario.getCodigo());
                    paramsConsultaAluno.put("data", data);
                    List<AulaAluno> objs = getAulaAlunoDao().findByParam(ctx,
                            "SELECT obj FROM AulaAluno obj WHERE obj.aula.aulaHorario.codigo = :aulaHorario AND obj.aula.inicio >= :data", paramsConsultaAluno);
                    if (objs != null && !objs.isEmpty()) {
                        throw new ValidacaoException("horarioTemAlunosNaoExcluir");
                    }
                }
            }
        } catch (ValidacaoException e) {
            throw new ValidacaoException(e.getMessage());
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

    }

    public void gerarAulasDiarias(final String key, final Aula aula,
            final List<String> diasSelecionados,final List<String> aulasGerar) throws Exception {
        List<Date> dias = Uteis.getDiasEntreDatas(aula.getDataInicio(), aula.getDataFim());
        for (Date dia : dias) {
            DiasSemana diaSemana = DiasSemana.getDiaSemanaNumeral(Uteis.getDiaDaSemanaNumero(dia));
            if (diaSemana != null && diasSelecionados.contains(diaSemana.getCodigo())) {
                for (AulaHorario horario : aula.getHorarios()) {
                    if(aulasGerar.contains(horario.getInicio()+" - "+horario.getFim())){
                        AulaDia aulaDia = new AulaDia();
                        aulaDia.setAula(aula);
                        aulaDia.setAulaHorario(horario);
                        aulaDia.setInicio(Calendario.getDataComHora(Calendario.getDataComHoraZerada(dia), horario.getInicio()));
                        aulaDia.setFim(Calendario.getDataComHora(Calendario.getDataComHoraZerada(dia), horario.getFim()));
                        aulaDia.setProfessor(aula.getProfessor());
                        inserirAulaDia(key, aulaDia);
                    }
                }
            }
        }
    }

    @Override
    public AulaDia inserirAulaDia(String ctx, AulaDia aulaDia) throws Exception {
        return getAulaDiaDao().insert(ctx, aulaDia);
    }

    public Modalidade obterModalidadePorCodigoZW(String ctx, Integer codigoZW) throws Exception {
        getModalidadeDao().getCurrentSession(ctx).clear();
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("codigoZW", codigoZW);
        return getModalidadeDao().findObjectByParam(ctx, "SELECT obj FROM Modalidade obj WHERE obj.codigoZW = :codigoZW", params);
    }

    public Ambiente obterAmbientePorCodigoZW(String ctx, Integer codigoZW) throws Exception {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("codigoZW", codigoZW);
        return getAmbienteDao().findObjectByParam(ctx, "SELECT obj FROM Ambiente obj WHERE obj.codigoZW = :codigoZW", params);
    }

    public List<PaletaCoresEnum> obterCoresUsadas(String ctx) throws Exception {
        List<Modalidade> todos = getModalidadeDao().findAll(ctx);
        List<PaletaCoresEnum> cores = new ArrayList<PaletaCoresEnum>();
        for (Modalidade modalidade : todos) {
            cores.add(modalidade.getCor());
        }
        return cores;
    }

    public PaletaCoresEnum novaCor(List<PaletaCoresEnum> coresUsadas, boolean vaiVermelho) throws Exception {
        for (int i = 0; i < PaletaCoresEnum.values().length; i++) {
            PaletaCoresEnum random = PaletaCoresEnum.randomCor(vaiVermelho);
            if (!coresUsadas.contains(random)) {
                return random;
            }
        }
        return PaletaCoresEnum.randomCor(vaiVermelho);
    }

    @Override
    public List<Modalidade> obterModalidadesZW(String ctx, Integer empresa, Boolean turma) throws ServiceException {
        List<Modalidade> modalidades = new ArrayList<Modalidade>();
        try {
            final String url = Aplicacao.getPropOAMD(ctx, Aplicacao.urlZillyonWebInteg);
            try {
                List<PaletaCoresEnum> cores = null;
                IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
                List<ModalidadeJSON> modalidadesJSON = integracaoWS.consultarModalidades(url, ctx, empresa);
                try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                    for (ModalidadeJSON modJson : modalidadesJSON) {
                        if (turma != null && turma) {
                            try (ResultSet result = ConexaoZWServiceImpl.criarConsulta(
                                    "select utilizarTurma from modalidade where codigo = " + modJson.getCodigoModalidade(),
                                    conZW)) {
                                if (result.next()) {
                                    if (!result.getBoolean("utilizarTurma")) {
                                        continue;
                                    }
                                }
                            }
                        }
                        Modalidade modalidade = obterModalidadePorCodigoZW(ctx, modJson.getCodigoModalidade());
                        if (modalidade == null || modalidade.getCodigo() == null || modalidade.getCodigo().equals(0)) {
                            cores = cores == null ? obterCoresUsadas(ctx) : cores;
                            PaletaCoresEnum novaCor = novaCor(cores, false);
                            cores.add(novaCor);
                            modalidade = getModalidadeDao().insert(ctx, new Modalidade(modJson.getCodigoModalidade(),
                                    modJson.getNome(), novaCor));
                        } else {
                            boolean alterado = false;
                            if (modalidade.getCor() != null && modalidade.getCor().name().startsWith("VERMELHO")) {
                                cores = cores == null ? obterCoresUsadas(ctx) : cores;
                                PaletaCoresEnum novaCor = novaCor(cores, false);
                                cores.add(novaCor);
                                modalidade.setCor(novaCor);
                                alterado = true;
                            }
                            if (!modalidade.getNome().equals(modJson.getNome())) {
                                modalidade.setNome(modJson.getNome());
                                alterado = true;
                            }
                            if (alterado) {
                                modalidade = getModalidadeDao().update(ctx, modalidade);
                            }
                        }
                        modalidades.add(modalidade);
                    }
                }
            } catch (Exception e) {
                return getModalidadeDao().findAll(ctx);
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        return modalidades;
    }

    @Override
    public List<Ambiente> obterAmbientesZW(String ctx, Integer empresa) throws ServiceException {
        List<Ambiente> ambientes = new ArrayList<Ambiente>();
        try {
            final String url = Aplicacao.getPropOAMD(ctx, Aplicacao.urlZillyonWebInteg);
            try {
                IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
                List<AmbienteJSON> ambientesJSON = integracaoWS.consultarAmbientes(url, ctx);
                for (AmbienteJSON ambJson : ambientesJSON) {
                    Ambiente ambiente = obterAmbientePorCodigoZW(ctx, ambJson.getCodigoAmbiente());
                    if (ambiente == null || ambiente.getCodigo() == null || ambiente.getCodigo().equals(0)) {
                        ambiente = getAmbienteDao().insert(ctx, new Ambiente(ambJson.getCodigoAmbiente(), ambJson.getNome(), ambJson.getCapacidade()));
                    } else {
                        if (!ambiente.getNome().equals(ambJson.getNome())
                                || (ambJson.getCapacidade() != null && ambiente.getCapacidade() == null)
                                || (ambJson.getCapacidade() == null && ambiente.getCapacidade() != null)
                                || (ambJson.getCapacidade().equals(ambiente.getCapacidade()))) {
                            ambiente.setNome(ambJson.getNome());
                            ambiente.setCapacidade(ambJson.getCapacidade());
                            ambiente = getAmbienteDao().update(ctx, ambiente);
                        }
                    }
                    ambientes.add(ambiente);
                }
            } catch (Exception e) {
                return getAmbienteDao().findAll(ctx);
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        return ambientes;
    }

    @Override
    public List<AulaHorario> obterHorarios(final String ctx, final Aula aula) throws ServiceException {
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("aula", aula.getCodigo());
            return getAulaHorarioDao().findByParam(ctx, "SELECT obj FROM AulaHorario obj WHERE obj.aula.codigo = :aula AND (obj.ativo is true OR obj.ativo is NULL)", params);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public Modalidade alteraModalidade(String ctx, Modalidade modalidade) throws ServiceException {
        try {
            return getModalidadeDao().update(ctx, modalidade);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<Modalidade> todasModalidades(String ctx) throws ServiceException {
        try {
            return getModalidadeDao().findAll(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<Ambiente> todosAmbientes(String ctx) throws ServiceException {
        try {
            return getAmbienteDao().findAll(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public AulaDia editarAulaDia(final String ctx, final AulaDia aulaDia) throws ServiceException {
        try {
            return getAulaDiaDao().update(ctx, aulaDia);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void removerAluno(final String ctx, final AulaAluno aluno, final Usuario usuario) throws ServiceException {
        try {
            getAulaDiaExcecaoDao().insert(ctx, new AulaDiaExcecao(Calendario.hoje(), aluno.getAula(), aluno.getCliente(), usuario));
            getAulaAlunoDao().deleteComParam(ctx, new String[]{"cliente.codigo", "aula.codigo"},
                    new Object[]{aluno.getCliente().getCodigo(), aluno.getAula().getCodigo()});
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void excluirAulaDia(final String ctx, final Integer codigoHorarioTurma, final Date dia,
    final Usuario usuario, final String justificativa) throws ServiceException {
        try {
            AulaDiaExclusao exclusao = new AulaDiaExclusao();
            exclusao.setCodigoHorarioTurma(codigoHorarioTurma);
            exclusao.setDataAulaDia(dia);
            exclusao.setDataExclusao(Calendario.hoje());
            exclusao.setJustificativa(justificativa);
            exclusao.setUsuario_codigo(usuario.getCodigo());
            getAulaDiaExclusaoDao().insert(ctx, exclusao);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<AulaDia> obterAulasDia(final String ctx, final Date dia, final Date diaFim, boolean desconsiderarCfg,
                        boolean substituidos) throws ServiceException {
        try {
            ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema cfgLimiteMostrar = css.consultarPorTipo(ctx, ConfiguracoesEnum.DIAS_MOSTRAR_TOTEM);
            Date dataLimite = Uteis.somarCampoData(Calendario.hoje(), Calendar.MINUTE, cfgLimiteMostrar.getValorAsInteger());
            if(cfgLimiteMostrar.getValorAsInteger() >= (24*60)){
                dataLimite = Calendario.getDataComHora(dataLimite, "23:59");
            }
            if(!desconsiderarCfg
                    && Calendario.maior(diaFim, dataLimite)){
                throw new Exception("Só serão mostradas as aulas que acontecerão em até "+
                        cfgLimiteMostrar.getConfiguracao().getLabelCfgSelect(cfgLimiteMostrar.getValorAsInteger()));
            }
            StringBuilder query = new StringBuilder();
            HashMap<String, Object> p = new HashMap<String, Object>();
            query.append(" where obj.inicio >= :inicio and obj.inicio <= :fim ");
            p.put("inicio", Calendario.getDataComHoraZerada(dia));
            p.put("fim", cfgLimiteMostrar.getValorAsInteger() < (24*60) ?
                    Uteis.somarCampoData(Calendario.hoje(), Calendar.MINUTE, cfgLimiteMostrar.getValorAsInteger()) :
                    Calendario.getDataComHora(diaFim, "23:59"));
            if(substituidos){
                query.append(" and obj.substituido is true ");
            }

            query.append(" order by obj.inicio ");
            return getAulaDiaDao().findByParam(ctx, query, p);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<AulaAluno> obterAulaAlunos(final String ctx, final Integer codigoAula, final Integer codigoAluno) throws ServiceException {
        try {
            StringBuilder query = new StringBuilder();
            HashMap<String, Object> p = new HashMap<String, Object>();
            query.append(" where obj.aula.codigo = :aula ");
            if (codigoAluno != null && codigoAluno > 0) {
                query.append(" and obj.cliente.codigo = :aluno ");
                p.put("aluno", codigoAluno);
            }
            p.put("aula", codigoAula);
            query.append(" order by obj.cliente.nome ");
            return getAulaAlunoDao().findByParam(ctx, query, p);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<AulaAluno> obterMatriculasDaAulaNoDia(final String ctx, final Integer codigoAula, final Date dia) throws ServiceException {
        try {
            StringBuilder query = new StringBuilder();
            HashMap<String, Object> p = new HashMap<String, Object>();
            query.append(" where obj.aula.codigo = :aula ");
            p.put("aula", codigoAula);

            if (dia != null) {
                query.append(" and obj.aula.inicio >= :inicio and obj.aula.inicio <= :fim ");

                p.put("inicio", Calendario.getDataComHoraZerada(dia));
                p.put("fim", Calendario.getDataComHora(dia,"23:59"));
            }

            query.append(" order by obj.cliente.nome ");
            return getAulaAlunoDao().findByParam(ctx, query, p);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }


    public AulaAluno verficarAlunoNaAula(final String ctx, final Integer codigoAula, final Integer codigoAluno) throws ServiceException {
        List<AulaAluno> aulaAlunos = obterAulaAlunos(ctx, codigoAula, codigoAluno);
        if (aulaAlunos == null || aulaAlunos.isEmpty()) {
            return null;
        }
        return aulaAlunos.get(0);
    }

    public ClienteSintetico addAlunoAutomaticamente(final String ctx, final String matricula) throws Exception {
        ClienteSinteticoService cs = (ClienteSinteticoService) UtilContext.getBean(ClienteSinteticoService.class);
        IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
        final String url = Aplicacao.getPropOAMD(ctx, Aplicacao.urlZillyonWebInteg);
        List<AddClienteJSON> clientes = integracaoWS.consultarClientesZW(url, ctx, 0, "", "", Integer.valueOf(matricula));
        if (clientes == null || clientes.isEmpty()) {
            throw new Exception("Seu cadastro não foi encontrado, procure a administração!");
        }
        ClienteSintetico cliente = integracaoWS.consultarClienteSintetico(url, ctx, clientes.get(0).getCodigoCliente());
        return cs.inserir(ctx, cliente);
    }

    @Deprecated
    public AulaAluno marcarPresenca(final String ctx, final String matricula, final Integer codigoAula, final Boolean aulaExperimental)
            throws ServiceException, ValidacaoException {
        try {
            ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema cfgValidarModalidade = css.consultarPorTipo(ctx, ConfiguracoesEnum.VALIDAR_MODALIDADE);
            ConfiguracaoSistema cfgLimiteMarcar = css.consultarPorTipo(ctx, ConfiguracoesEnum.MINUTOS_AGENDAR_COM_ANTECEDENCIA);

            AulaDia aulaDia = getAulaDiaDao().findById(ctx, codigoAula);
            Long diferencaEmMinutos = Uteis.minutosEntreDatas(Calendario.hoje(), aulaDia.getInicio());
            if(diferencaEmMinutos > cfgLimiteMarcar.getValorAsInteger() && cfgLimiteMarcar.getValorAsInteger()!=0){
                throw new ServiceException("Você só poder marcar a aula com "
                        +cfgLimiteMarcar.getValorAsInteger()+" minutos"
                        +" de antecedência ou menos");
            }
            Long tolerancia = Uteis.minutosEntreDatas(aulaDia.getInicio(),Calendario.hoje());
            if(tolerancia > aulaDia.getAula().getMinutosTolerancia() && aulaDia.getAula().getMinutosTolerancia() > 0){
                throw new ServiceException("Está aula já atingiu o limite de tolerância.");
            }
            if (aulaDia.getAulaCheia()) {
                throw new ServiceException("naopodeaulaCheia");
            }
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("matricula", Integer.valueOf(matricula));
            ClienteSintetico cliente = clienteSinteticoService.obterObjetoPorParam(ctx,
                    " SELECT obj FROM ClienteSintetico obj WHERE obj.matricula = :matricula", param);
            if (cliente == null) {
                cliente = addAlunoAutomaticamente(ctx, matricula);
            }
            AulaAluno alunoNaAula = verficarAlunoNaAula(ctx, codigoAula, cliente.getCodigo());
            if (alunoNaAula != null) {
                throw new Exception("Você já se matriculou nessa aula!");
            }

            if (Calendario.menor(aulaDia.getInicio(), Calendario.hoje())) {
                throw new Exception("Você não pode se matricular numa aula que aconteceu ontem ou antes!");
            }
            List<AulaAluno> registrosAlunoAula = consultarAlunoEmAula(ctx, aulaDia, cliente.getCodigo());
            if(registrosAlunoAula != null && !registrosAlunoAula.isEmpty()){
                throw new Exception("Você não pode se matricular nesta aula pois está matriculado em outra nesse período!");
            }

            if (cfgValidarModalidade.getValorAsBoolean()
                    && !cliente.getCodigosModalidades().contains(aulaDia.getAula().getModalidade().getCodigoZW())
                    && cliente.getNrAulasExperimentais() < 1) {
                getAulaDiaExcecaoDao().insert(ctx, new AulaDiaExcecao(Calendario.hoje(), aulaDia, cliente));
                throw new Exception("Você não possui essa modalidade e não tem mais aulas experimentais");
            }
            if (cfgValidarModalidade.getValorAsBoolean()
                    && !aulaExperimental && !cliente.getCodigosModalidades().contains(aulaDia.getAula().getModalidade().getCodigoZW())) {
                throw new ValidacaoException("Você não tem essa modalidade, deseja usar uma de suas " + cliente.getNrAulasExperimentais() + " aulas experimentais?");
            }
            AulaAluno alunoAula = new AulaAluno();
            alunoAula.setAula(aulaDia);
            alunoAula.setProfessor(aulaDia.getAula().getProfessor());
            alunoAula.setAulaExperimental(aulaExperimental);
            if (aulaExperimental) {
                cliente = clienteSinteticoService.alterarAlgunsCampos(
                        ctx, cliente,
                        new String[]{"nrAulasExperimentais"},
                        new Object[]{cliente.getNrAulasExperimentais() - 1});
                cliente.setNrAulasExperimentais(cliente.getNrAulasExperimentais() - 1);
            }
            alunoAula.setCliente(cliente);
            return getAulaAlunoDao().insert(ctx, alunoAula);
        } catch (ValidacaoException e) {
            throw new ValidacaoException(e.getMessage());
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void excluirAulasGeradas(String key, Aula aula) throws ServiceException {
        try {
            getAulaDiaDao().deleteComParam(key, new String[]{"aula.codigo"}, new Object[]{aula.getCodigo()});
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public Modalidade obterModalidadePorIdZW(final String key, final Integer id) throws ServiceException {
        try {
            return getModalidadeDao().findObjectByAttribute(key, "codigoZW", id);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public Ambiente obterAmbientePorId(final String key, final Integer id) throws ServiceException {
        try {
            return getAmbienteDao().findById(key, id);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public Modalidade obterModalidadePorNome(final String key, final String nome) throws ServiceException {
        try {
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("nome", nome);
            List<Modalidade> findByParam = getModalidadeDao().findByParam(key, "SELECT obj FROM Modalidade obj WHERE obj.nome = :nome", param);
            if (findByParam == null || findByParam.isEmpty()) {
                return new Modalidade(nome);
            }
            return findByParam.get(0);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<AgendaTotalJSON> obterAulasAluno(final String key, final Integer matricula) throws ServiceException {
        try {
            if(Aplicacao.independente(key)){
                List<AgendaTotalJSON> aulas = new ArrayList<AgendaTotalJSON>();
                List<AulaAluno> aulaAlunos = agendaService.aulasAluno(key, matricula, Calendario.hoje(), Uteis.somarDias(Calendario.hoje(), 7));
                for(AulaAluno a : aulaAlunos){
                    aulas.add(new AgendaTotalJSON(agendaService.aulaHorarioEvento(a.getDia(), a.getHorario().getCodigo(), key),
                            agendaService.nrAlunosHorarioDia(key, a.getHorario().getCodigo(), a.getDia())));
                }
                return aulas;
            }else{
                IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                return integracaoWS.consultarProximasAulaCheia( key, matricula);
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<AgendaTotalJSON> obterAulasAluno(final String key, final Integer matricula, final String dia) throws ServiceException {
        // TODO: JOÃO ALCIDES
        return obterAulasAluno(key, matricula);
    }


    @Override
    public List<AgendaTotalJSON> obterAulasAlunoAPartirDoDia(final String key, final Integer matricula, final String dia) throws ServiceException {
        // TODO: JOÃO ALCIDES
        return obterAulasAluno(key, matricula);
    }

    @Override
    public Integer obterPontosAluno(final String key, final Integer matricula) throws ServiceException {
        try
        {
            Map<String, String> param = new HashMap<String, String>();
            param.put("operacao", "obterPontosAluno");
            param.put("matricula", matricula.toString());
            param.put("key", key);
            final String url = String.format("%s/prest/aulacheia", new Object[]{
                    Aplicacao.getPropOAMD(key, Aplicacao.urlZillyonWeb),
                    key});
            String json = HttpRequestUtil.executeRequestInner(url, param, 10000);
            return new JSONObject(json).optInt("pontos");
        } catch(Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
        public void substituirProfessor(final String key, final Integer codigoHorarioTurma, final Integer codigoProfessorOrigem,
        final Integer codigoProfessorSubstituto,
            final Date diaAula, final Usuario usuario,
            final String justificativa) throws ServiceException{
        try {
            ProfessorSubstituido sub = new ProfessorSubstituido();
            sub.setCodigoProfessorOriginal(codigoProfessorOrigem);
            sub.setCodigoProfessorSubstituto(codigoProfessorSubstituto);
            sub.setDataSubstituicao(Calendario.hoje());
            sub.setDiaAula(diaAula);
            sub.setJustificativa(justificativa);
            sub.setCodigoHorarioTurma(codigoHorarioTurma);
            sub.setUsuarioSubstituiu_codigo(usuario.getCodigo());
            subDao.insert(key, sub);

            String nomeProfessorOrigem = "";
            String nomeProfessorSubstituto = "";

            try (Connection conZW = conexaoZWService.conexaoZw(key)) {
                String queryOrigem = String.format("SELECT p.nome FROM colaborador c JOIN pessoa p ON c.pessoa = p.codigo WHERE c.codigo = %s", codigoProfessorOrigem);
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(queryOrigem, conZW)) {
                    while (rs.next()) {
                        nomeProfessorOrigem = rs.getString("nome");
                    }
                }

                String querySubstituto = String.format("SELECT p.nome FROM colaborador c JOIN pessoa p ON c.pessoa = p.codigo WHERE c.codigo = %s", codigoProfessorSubstituto);
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(querySubstituto, conZW)) {
                    while (rs.next()) {
                        nomeProfessorSubstituto = rs.getString("nome");
                    }
                }
            }

            // Converte o timestamp para LocalDate
            LocalDate data = Instant.ofEpochMilli(diaAula.getTime())
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();

            // Formata no padrão dd/MM/yyyy
            String dataParaChave = data.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"));

            // Formata com microssegundos
            String ultimaAlteracao = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            try (Connection conZW = conexaoZWService.conexaoZw(key)) {
                ConexaoZWServiceImpl.executarConsulta(
                        String.format(
                                "INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, nomecampo, valorcampoanterior, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, pessoa, cliente) VALUES ('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')", "ALUNO_AULA_COLETIVA", "Alteração de professor", sub.getCodigoHorarioTurma().toString() + "_" + dataParaChave, "PROFESSOR", nomeProfessorOrigem, nomeProfessorSubstituto, ultimaAlteracao, usuario.getNome(), "ALTERAÇÃO", usuario.getProfessor().getCodigoPessoa(), usuario.getProfessor().getCodigoPessoa()), conZW);
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException(e.getMessage());
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }


    @Override
    public List<AulaAluno> consultarAlunoEmAula(final String ctx, final AulaDia aulaDia, final Integer aluno) throws ServiceException {
        try {
            HashMap<String, Object> p = new HashMap<String, Object>();
            StringBuilder query = new StringBuilder();
            query.append(" select obj from AulaAluno obj where ((obj.aula.inicio >= :inicio and obj.aula.inicio <= :fim) \n");
            query.append(" or (obj.aula.fim >= :inicio and obj.aula.fim <= :fim)\n");
            query.append(" or (:inicio >= obj.aula.inicio and :inicio <= obj.aula.fim)\n");
            query.append(" or (:fim >= obj.aula.inicio and :fim <= obj.aula.fim))\n");
            query.append(" and obj.cliente.codigo = :cliente ");

            p.put("cliente", aluno);
            p.put("inicio", aulaDia.getInicio());
            p.put("fim", aulaDia.getFim());

            return getAulaAlunoDao().findByParam(ctx, query.toString(), p);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public String inserirZW(String ctx, TurmaAulaCheiaJSON turma, AulaDTO aulaDTO) throws ServiceException {
        try {
            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            String retorno = integracaoWS.gravarTurmaAulaCheia(ctx, turma);
            if (retorno.equalsIgnoreCase("ok")) {
                TurmaGymPassJSON turmaGymPassJSON = null;
                try {
                    turmaGymPassJSON = agendaService.obterDadosTurmaTurmaGymPassJSON(ctx, turma.getCodigo(), turma.getProdutoGymPass());
                } catch (Exception ex) {
                    turmaGymPassJSON = null;
                }
                if (turmaGymPassJSON != null) {
                    agendaService.iniciaThreadGympass(ctx, turmaGymPassJSON.getCodigo(), turma.getEmpresa(), 0,
                            TipoThreadGympassEnum.SINCRONIZAR_TURMA);
                }

                Integer codigoTurma;
                if(turma.getCodigo() != null){
                    codigoTurma = turma.getCodigo();
                }else{
                    codigoTurma = turmaDao.consultarUltimaTurmaSalva(ctx);
                }
                List<TurmaVideoDTO> listaLinkVideosSalvos =  turmaDao.obterListaTurmaVideo(ctx,codigoTurma);

                for(TurmaVideoDTO t :listaLinkVideosSalvos){
                    if(!aulaDTO.getLinkVideos().contains( new TurmaVideoDTO(t))){
                        turmaDao.excluirTurmaVideo(ctx,t);
                    }
                }

                if(aulaDTO.getLinkVideos() != null && aulaDTO.getLinkVideos().size() > 0){
                    for(TurmaVideoDTO t :aulaDTO.getLinkVideos()){
                        t.setTurma_codigo(codigoTurma);
                        TurmaVideoDTO turmaDto = turmaDao.obterTurmaVideo(ctx, t.getId());
                        if(turmaDto != null){
                            turmaDao.updateTurmaVideo(ctx, t);
                        }else{
                            t = turmaDao.saveTurmaVideo(ctx, t);
                        }
                    }
                }
                if (!UteisValidacao.emptyNumber(codigoTurma)) {
                    gravarTurmaMapaEquipamentoAparelho(ctx, codigoTurma, aulaDTO.getTurmaMapaEquipamentoAparelho());
                }
            }
            return retorno;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private void gerenciamentoAulaIntegracaoSelfloops(String ctx, AulaColetivaResponseDTO aulaDTO) {
        try {
            if (aulaDTO.getAulaIntegracaoSelfloops()) {
                aulaDTO.setHorarios(turmaDao.listarHorariosTurma(ctx, new JSONObject(), new PaginadorDTO(), aulaDTO.getCodigo()));
                Empresa empTreino = empresaService.obterPorIdZW(ctx, aulaDTO.getEmpresa());
                if (empTreino != null && !UteisValidacao.emptyNumber(empTreino.getCodigo())) {
                    SelfLoopsConfiguracoes configsSelf = selfloopsConfiguracoesService.obterPorEmpresa(ctx, empTreino.getCodigo());
                    if (configsSelf != null && configsSelf.isIntegracaoRelizadaSucesso()) {

                        for (HorarioTurmaResponseDTO horario : aulaDTO.getHorarios()) {
                            CourseScheduleDTO courseScheduleDTO = new CourseScheduleDTO();
                            courseScheduleDTO.setTeam(configsSelf.getEmpresaSelfloops());
                            courseScheduleDTO.setCourse_schedule(new CourseScheduleDetailsDTO());
                            courseScheduleDTO.getCourse_schedule().setCourse_name(horario.getCodigo()+"-"+aulaDTO.getDescricao());
                            courseScheduleDTO.getCourse_schedule().setCourse_type("External");
                            courseScheduleDTO.getCourse_schedule().setCourse_description(UteisValidacao.emptyString(aulaDTO.getMensagem()) ? "" : aulaDTO.getMensagem());
                            courseScheduleDTO.getCourse_schedule().setFrom_date(Uteis.getDataAplicandoFormatacao(new Date(aulaDTO.getDataInicio()), "yyyy-MM-dd"));
                            courseScheduleDTO.getCourse_schedule().setTo_date(Uteis.getDataAplicandoFormatacao(new Date(aulaDTO.getDataFinal()), "yyyy-MM-dd"));
                            courseScheduleDTO.getCourse_schedule().setStart_time(horario.getHoraInicial().trim() + ":00-03:00");
                            courseScheduleDTO.getCourse_schedule().setEnd_time(horario.getHoraFinal().trim() + ":00-03:00");

                            DiasSemana diaSem = DiasSemana.getDiaSemana(horario.getDia());
                            if (diaSem == DiasSemana.DOMINGO) {
                                courseScheduleDTO.getCourse_schedule().setDay_sunday(true);
                            } else if (diaSem == DiasSemana.SEGUNDA_FEIRA) {
                                courseScheduleDTO.getCourse_schedule().setDay_monday(true);
                            } else if (diaSem == DiasSemana.TERCA_FEIRA) {
                                courseScheduleDTO.getCourse_schedule().setDay_tuesday(true);
                            } else if (diaSem == DiasSemana.QUARTA_FEIRA) {
                                courseScheduleDTO.getCourse_schedule().setDay_wednesday(true);
                            } else if (diaSem == DiasSemana.QUINTA_FEIRA) {
                                courseScheduleDTO.getCourse_schedule().setDay_thursday(true);
                            } else if (diaSem == DiasSemana.SEXTA_FEIRA) {
                                courseScheduleDTO.getCourse_schedule().setDay_friday(true);
                            } else if (diaSem == DiasSemana.SABADO) {
                                courseScheduleDTO.getCourse_schedule().setDay_saturday(true);
                            }

                            Ambiente ambiente = ambienteService.consultarPorAmbienteZW(ctx, horario.getAmbienteId());
                            if (ambiente != null && !UteisValidacao.emptyString(ambiente.getNome())) {
                                courseScheduleDTO.getCourse_schedule().setRoom(ambiente.getNome());
                            } else {
                                courseScheduleDTO.getCourse_schedule().setRoom("");
                            }
                            ProfessorSintetico professor = professorSinteticoService.consultarPorCodigoColaborador(ctx, horario.getProfessorId());
                            if (professor != null && !UteisValidacao.emptyString(professor.getNome())) {
                                courseScheduleDTO.getCourse_schedule().setInstructor(professor.getNome());
                            } else {
                                courseScheduleDTO.getCourse_schedule().setInstructor("");
                            }
                            courseScheduleDTO.getCourse_schedule().setMax_capacity(horario.getMaxAlunos());
                            List<String> sensors = new ArrayList<>();
                            for (TurmaMapaEquipamentoAparelhoDTO tmea : aulaDTO.getTurmaMapaEquipamentoAparelho()) {
                                Aparelho aparelho = aparelhoService.obterPorId(ctx, tmea.getCodigo_aparelhotreino());
                                sensors.add(aparelho.getSensorSelfloops());
                            }
                            courseScheduleDTO.getCourse_schedule().setSensors(sensors);

                            if (UteisValidacao.emptyNumber(aulaDTO.getCodigo())) {
                                JSONObject aulaSelfloops = selfloopsConfiguracoesService.criarAulaIntegracaoSelfloops(configsSelf.getCodeSelfloops(), configsSelf.getRefreshToken(), courseScheduleDTO, ctx, configsSelf.getEmpresa().getCodigo());
                                registrarInclusaoTurmaHorarioSelfloops(ctx, aulaDTO.getCodigo(), horario, new JSONObject(courseScheduleDTO), aulaSelfloops);
                            } else {
                                alterarAulaSelfloops(ctx, aulaDTO, aulaDTO.getCodigo(), configsSelf, horario, courseScheduleDTO);
                            }


                        }
                        if (!UteisValidacao.emptyNumber(aulaDTO.getCodigo())) {
                            inativarAulaRemovidasSelfloops(ctx, aulaDTO.getCodigo(), configsSelf, aulaDTO);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Logger.getLogger(AulaServiceImpl.class.getName()).log(Level.SEVERE, "Erro ao incluir aula na integração selfloops -> ", e);
        }
    }

    private void registrarInclusaoTurmaHorarioSelfloops(String ctx, Integer codigoTurma, HorarioTurmaResponseDTO horario, JSONObject jsonEnvio, JSONObject jsonRetorno) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (PreparedStatement insert = conZW.prepareStatement("insert into turmahorariointegracaoselfloops (turma, datacriacao, courseScheduleId, horarioIdentificador, jsonEnvio, jsonRetorno) values (?, ?, ?, ?, ?, ?) returning codigo;")) {
                insert.setInt(1, codigoTurma);
                insert.setDate(2, Uteis.getDataJDBC(Calendario.hoje()));
                insert.setString(3, jsonRetorno.getJSONObject("course_schedule").getString("id"));
                insert.setString(4, horario.getCodigo()+"-"+horario.getHoraInicial().trim()+"_"+ horario.getHoraFinal().trim());
                insert.setString(5, jsonEnvio.toString());
                insert.setString(6, jsonRetorno.toString());
                try (ResultSet rs = insert.executeQuery()) {

                }
            }
        }
    }

    private void alterarAulaSelfloops(String ctx, AulaColetivaResponseDTO aulaDTO, Integer codigoTurma, SelfLoopsConfiguracoes configsSelf, HorarioTurmaResponseDTO horario, CourseScheduleDTO courseScheduleDTO) throws Exception {
        String horarioIdentificador = horario.getCodigo()+"-"+horario.getHoraInicial().trim() + "_" + horario.getHoraFinal().trim();
        String courseScheduleId = "";
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (PreparedStatement select = conZW.prepareStatement("select courseScheduleId from turmahorariointegracaoselfloops where turma = ? and horarioIdentificador = ?;")) {
                select.setInt(1, aulaDTO.getCodigo());
                select.setString(2, horarioIdentificador);
                try (ResultSet rs = select.executeQuery()) {
                    while (rs.next()) {
                        courseScheduleId = rs.getString("courseScheduleId");
                    }
                }
            }

            if (!courseScheduleId.isEmpty()) {
                JSONObject retorno = selfloopsConfiguracoesService.alterarAulaIntegracaoSelfloops(configsSelf.getCodeSelfloops(), configsSelf.getRefreshToken(), courseScheduleDTO, courseScheduleId, ctx, configsSelf.getEmpresa().getCodigo());
                registrarAlteracaoTurmaHorarioSelfloops(aulaDTO.getCodigo(), conZW, new JSONObject(courseScheduleDTO), retorno, courseScheduleId);
            } else {
                JSONObject aulaSelfloops = selfloopsConfiguracoesService.criarAulaIntegracaoSelfloops(configsSelf.getCodeSelfloops(), configsSelf.getRefreshToken(), courseScheduleDTO, ctx, configsSelf.getEmpresa().getCodigo());
                registrarInclusaoTurmaHorarioSelfloops(ctx, codigoTurma, horario, new JSONObject(courseScheduleDTO), aulaSelfloops);
            }
        }
    }

    private void registrarAlteracaoTurmaHorarioSelfloops(Integer turma, Connection conZW, JSONObject envio, JSONObject retorno, String courseScheduleId) throws Exception {
        try (PreparedStatement update = conZW.prepareStatement("update turmahorariointegracaoselfloops set jsonRetorno = ?, jsonEnvio = ?, dataalteracao = ? where turma = ? and courseScheduleId = ?;")) {
            update.setString(1, retorno.toString());
            update.setString(2, envio.toString());
            update.setDate(3, Uteis.getDataJDBC(Calendario.hoje()));
            update.setInt(4, turma);
            update.setString(5, courseScheduleId);
            update.execute();
        }
    }

    private void deleteTurmaHorarioSelfloops(Integer turma, Connection conZW, String courseScheduleId) throws Exception {
        try (PreparedStatement update = conZW.prepareStatement("delete from turmahorariointegracaoselfloops where turma = ? and courseScheduleId = ?;")) {
            update.setInt(1, turma);
            update.setString(2, courseScheduleId);
            update.execute();
        }
    }

    private void inativarAulaRemovidasSelfloops(String ctx, Integer turma, SelfLoopsConfiguracoes configsSelf, AulaColetivaResponseDTO aulaDTO) throws Exception {
        String sql = "select courseScheduleId, jsonEnvio from turmahorariointegracaoselfloops where turma = ?";
        if (aulaDTO != null) {
            StringBuilder horariosAtuais = new StringBuilder();
            for (HorarioTurmaResponseDTO horario : aulaDTO.getHorarios()) {
                if (horariosAtuais.length() > 0) {
                    horariosAtuais.append("','");
                }
                horariosAtuais.append(horario.getCodigo()).append("-").append(horario.getHoraInicial().trim()).append("_").append(horario.getHoraFinal().trim());
            }
            sql += " and horarioidentificador not in ('" + horariosAtuais + "')";
        }
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (PreparedStatement select = conZW.prepareStatement(sql)) {
                select.setInt(1, turma);
                try (ResultSet rs = select.executeQuery()) {
                    while (rs.next()) {
                        String courseScheduleId = rs.getString("courseScheduleId");
                        JSONObject json = new JSONObject(rs.getString("jsonEnvio"));
                        CourseScheduleDTO courseSchedule = JSONMapper.getObject(json, CourseScheduleDTO.class);
                        if (Calendario.maiorOuIgual(Calendario.getDate("yyyy-MM-dd", courseSchedule.getCourse_schedule().getFrom_date()), Calendario.hoje())) {
                            JSONObject retorno = selfloopsConfiguracoesService.deleteAulaIntegracaoSelfloops(configsSelf.getCodeSelfloops(), configsSelf.getRefreshToken(), courseScheduleId, ctx, configsSelf.getEmpresa().getCodigo());
                            if (retorno.has("status") && retorno.getInt("status") == 204) {
                                deleteTurmaHorarioSelfloops(turma, conZW, courseScheduleId);
                            } else {
                                registrarAlteracaoTurmaHorarioSelfloops(turma, conZW, json, retorno, courseScheduleId);
                            }
                        } else {
                            courseSchedule.getCourse_schedule().setTo_date(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"));
                            JSONObject retorno = selfloopsConfiguracoesService.alterarAulaIntegracaoSelfloops(configsSelf.getCodeSelfloops(), configsSelf.getRefreshToken(), courseSchedule, courseScheduleId, ctx, configsSelf.getEmpresa().getCodigo());
                            registrarAlteracaoTurmaHorarioSelfloops(turma, conZW, json, retorno, courseScheduleId);
                        }
                    }
                }
            }
        }
    }

    private void gravarTurmaMapaEquipamentoAparelho(String ctx, Integer codigoTurma, List<TurmaMapaEquipamentoAparelhoDTO> novosTMEA) {
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                StringBuilder sql = new StringBuilder();
                sql.append("select codigo, codigo_aparelhotreino, turma, mapaequipamento \n");
                sql.append("from turmamapaequipamentoaparelho \n");
                sql.append("where turma = ").append(codigoTurma);

                List<TurmaMapaEquipamentoAparelhoDTO> antigosTMEA = new ArrayList<>();
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                    while (rs.next()) {
                        antigosTMEA.add(new TurmaMapaEquipamentoAparelhoDTO(
                                rs.getInt("codigo"),
                                rs.getInt("codigo_aparelhotreino"),
                                rs.getInt("turma"),
                                rs.getString("mapaequipamento")
                        ));
                    }
                }

                Map<Integer, TurmaMapaEquipamentoAparelhoDTO> antigosMap = antigosTMEA.stream()
                        .collect(Collectors.toMap(TurmaMapaEquipamentoAparelhoDTO::getCodigo_aparelhotreino, dto -> dto));
                Map<Integer, TurmaMapaEquipamentoAparelhoDTO> novosMap = novosTMEA.stream()
                        .collect(Collectors.toMap(TurmaMapaEquipamentoAparelhoDTO::getCodigo_aparelhotreino, dto -> dto));

                // caso o registro existente em banco não esteja presente no DTO enviado pelo front, o do banco deverá ser removido
                antigosMap.keySet().stream()
                        .filter(codigo -> !novosMap.containsKey(codigo))
                        .forEach(codigo -> {
                            try {
                                deletarTurmaMapaEquipamentoAparelho(ctx, antigosMap.get(codigo).getCodigo());
                            } catch (ServiceException e) {
                                throw new RuntimeException(e);
                            }
                        });

                for (TurmaMapaEquipamentoAparelhoDTO novo : novosTMEA) {
                    TurmaMapaEquipamentoAparelhoDTO antigo = antigosMap.get(novo.getCodigo_aparelhotreino());
                    // se estiver igual não precisa alterar
                    if (antigo == null) {
                        inserirTurmaMapaEquipamentoAparelho(ctx, novo.getCodigo_aparelhotreino(), codigoTurma, novo.getMapaequipamento());
                    } else if (!novo.getMapaequipamento().equals(antigo.getMapaequipamento())) {
                        alterarTurmaMapaEquipamentoAparelho(ctx, antigo.getCodigo(), novo.getMapaequipamento());
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logar(e, AulaServiceImpl.class);
        }
    }

    public void inserirTurmaMapaEquipamentoAparelho(String ctx, Integer codigoAparelho, Integer codigoTurma, String mapaEquipamento) throws ServiceException {
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                try (PreparedStatement insert = conZW.prepareStatement("insert into turmamapaequipamentoaparelho (codigo_aparelhotreino, turma, mapaequipamento) values (?, ?, ?) returning codigo;")) {
                    insert.setInt(1, codigoAparelho);
                    insert.setInt(2, codigoTurma);
                    insert.setString(3, mapaEquipamento);
                    try (ResultSet rs = insert.executeQuery()) {

                    }
                }
            }
        } catch (Exception e) {
            throw new ServiceException("Erro ao tentar inserir TurmaMapaEquipamentoAparelho", e);
        }
    }

    public void alterarTurmaMapaEquipamentoAparelho(String ctx, Integer codigo, String mapaEquipamento) throws ServiceException {
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                try (PreparedStatement update = conZW.prepareStatement("update turmamapaequipamentoaparelho set mapaequipamento = ? where codigo = ?;")) {
                    update.setString(1, mapaEquipamento);
                    update.setInt(2, codigo);
                    update.execute();
                }
            }
        }
        catch (Exception e) {
            throw new ServiceException("Erro ao tentar alterar TurmaMapaEquipamentoAparelho", e);
        }
    }

    public void deletarTurmaMapaEquipamentoAparelho(String ctx, Integer codigo) throws ServiceException {
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                try (PreparedStatement delete = conZW.prepareStatement("delete from turmamapaequipamentoaparelho where codigo = " + codigo)) {
                    delete.execute();
                }
            }
        } catch (Exception e) {
            throw new ServiceException("Erro ao tentar deletar TurmaMapaEquipamentoAparelho", e);
        }
    }


    @Override
    public List<TurmaAulaCheiaJSON> obterAulasColetivas(String ctx, Integer codigo, Integer empresa, PaginadorDTO paginadorDTO, JSONObject filtroAulasJSON) throws Exception {
        JSONObject jsonObject = chamadaZW(ctx, "/prest/aulacheia/aulas", codigo, paginadorDTO, filtroAulasJSON, empresa);
        if(paginadorDTO != null){
            paginadorDTO.setQuantidadeTotalElementos(new Long(jsonObject.optInt("total")));
        }
        return JSONMapper.getList(new JSONArray(jsonObject.getString("content")), TurmaAulaCheiaJSON.class);
    }

    @Override
    public String excluirAulaCheia(String ctx, Integer codigo, Integer usuario) throws Exception {
        TurmaGymPassJSON turmaGymPassJSON = null;
        try {
            turmaGymPassJSON = agendaService.obterDadosTurmaTurmaGymPassJSON(ctx, codigo, null);
        } catch (Exception ex) {
            turmaGymPassJSON = null;
        }

        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        String retorno = integracaoWS.excluirAulaCheia(ctx, codigo, usuario);
        if (turmaGymPassJSON != null && retorno.equalsIgnoreCase("ok")) {
            agendaService.iniciaThreadGympass(ctx, codigo, turmaGymPassJSON.getEmpresaZW(), 0, TipoThreadGympassEnum.EXCLUIR_TURMA);
        }
        return retorno;
    }

    @Override
    public ResultAlunoClienteSinteticoJSON obterAlunosDeUmaAula(final String ctx, final Integer codigoHorario, Date dia) throws Exception{
        return modoBDService.obterAlunosDeUmaAula(ctx, codigoHorario, dia);
    }

    @Override
    public ResultAlunoClienteSinteticoJSON obterAlunosAulaNaoColetiva(final String ctx, final Integer codigoHorario, Date dia) throws Exception {
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        return integracaoWS.obterAlunosAulaNaoColetiva(ctx, codigoHorario, dia);
    }

    @Override
    public List<AgendadoJSON> obterAlunosDeUmaTurma(final String ctx, final Integer empresa,
                                                    final Integer codigoHorario, Date dia) throws Exception {
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        return integracaoWS.obterAlunosDeUmaTurma(ctx, empresa, codigoHorario, dia);
    }

    @Override
    public String confirmarAlunoAula(final String key, final Integer cliente, final Integer horarioTurma,
                                     final String dia, final Integer usuario) throws Exception {

        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        final ProfessorSintetico professorSintetico = professorSinteticoDao.findById(key, usuario);
        if(professorSintetico == null){
            throw new ServiceException("Professor não encontrado. O campo 'usuario' deve ser o código do professor do treino.");
        }
        final ClienteSintetico clienteSintetico = getClienteSinteticoService().consultarPorMatricula(key, cliente.toString());
        if(clienteSintetico == null){
            throw new ServiceException("Cliente não encontrado. O campo 'cliente' deve ser o código do cliente do treino.");
        }

        return integracaoWS.confirmarAlunoAula(key, clienteSintetico.getCodigoCliente(), horarioTurma, dia, professorSintetico.getCodigoColaborador());
    }

    @Override
    public Boolean reposicaoNestaAula(String contexto, Integer horarioTurma, Integer cliente, Date dia) throws Exception {
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        return integracaoWS.existeReposicaoAlunoDia(contexto, horarioTurma, cliente, dia);
    }

    private String professorSelecionado (List<Integer> professorIds) {
        String professorSelecionado = "";
        for (Integer professorId : professorIds) {
            professorSelecionado += "," + professorId.toString();
        }
        return professorSelecionado;
    }

    private String ambienteSelecionado (List<Integer> ambienteIds) {
        String ambienteSelecionado = "";
        for (Integer ambienteId : ambienteIds) {
            ambienteSelecionado += "," + ambienteId.toString();
        }
        return ambienteSelecionado;
    }

    @Override
    public List<AulaResponseDTO> listarAulas(JSONObject filtros, PaginadorDTO paginadorDTO, Integer empresaId) throws ServiceException{
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<AulaResponseDTO> listaRet = new ArrayList<AulaResponseDTO>();
            if (SuperControle.independente(ctx)) {
                if (!StringUtils.isBlank(paginadorDTO.getSort())) {
                    paginadorDTO.setSort(paginadorDTO.getSort());
                } else {
                    paginadorDTO.setSort("nome,ASC");
                }
                FiltroAulasJSON filtroAulasJSON = new FiltroAulasJSON(filtros);
                String professorSelecionado = filtroAulasJSON.getProfessorIds() != null
                        ? professorSelecionado(filtroAulasJSON.getProfessorIds()).replaceFirst(",", "") : "";

                String ambienteSelecionado = filtroAulasJSON.getAmbienteIds() != null
                        ? ambienteSelecionado(filtroAulasJSON.getAmbienteIds()).replaceFirst(",", "") : "";

                List<Aula> lista = aulaDao.listarAulas(ctx, filtroAulasJSON, professorSelecionado, ambienteSelecionado, paginadorDTO);
                if (lista != null) {
                    for (Aula a : lista) {
                        listaRet.add(new AulaResponseDTO(a, SuperControle.independente(ctx)));
                    }
                }
            } else {
                if (!StringUtils.isBlank(paginadorDTO.getSort())) {
                    paginadorDTO.setSort(paginadorDTO.getSort());
                } else {
                    paginadorDTO.setSort("nome,ASC");
                }

                listaRet = listarAulasZW(ctx, filtros, empresaId, paginadorDTO);
            }
            return listaRet;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AulaExcecoes.ERRO_lISTAR, e);
        }
    }

    private List<AulaResponseDTO> listarAulasZW(String ctx, JSONObject filtroAulasJSON, Integer empresaId, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<TurmaAulaCheiaJSON> aulas = obterAulasColetivas(ctx, null, empresaId, paginadorDTO, filtroAulasJSON);
            List<AulaResponseDTO> aulaRet = new ArrayList<>();
            for (TurmaAulaCheiaJSON aula : aulas) {
                AulaResponseDTO aulaResponse = getAulaResponse(ctx, aula);
                aulaResponse.setDias(obterDiasSemanaHorariosAtivos(ctx, aula.getCodigo()));
                aulaRet.add(aulaResponse);
            }
            return aulaRet;
        } catch (Exception e) {
            throw new ServiceException(AulaExcecoes.ERRO_lISTAR);
        }
    }

    private String obterDiasSemanaHorariosAtivos(String ctx, Integer codigoAula) throws Exception {
        try {
            return turmaDao.obterDiasSemanaHorarioPorTurma(ctx, codigoAula);
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao obter dias semana horários ativos da aula, erro: " + e.getMessage());
            return "";
        }
    }

    private boolean contemIdFiltro(FiltroAulasJSON filtroAulasJSON, Integer valueAmbiente, Integer valueProfessor, Integer valueModalide) {
        boolean resultAmbiente = false;
        boolean resultProfessor = false;
        boolean resultModalidade = false;

        boolean filtroAmbiente = false;
        boolean filtroProfessor = false;
        boolean filtroModalidade = false;
        boolean retorno = false;
        if (!UteisValidacao.emptyList(filtroAulasJSON.getAmbienteIds())) {
            filtroAmbiente = true;
            for (Integer id : filtroAulasJSON.getAmbienteIds()) {
                if (valueAmbiente.equals(id)) {
                    resultAmbiente = true;
                    break;
                }
            }
        }
        if (!UteisValidacao.emptyList(filtroAulasJSON.getProfessorIds())) {
            filtroProfessor = true;
            for (Integer id : filtroAulasJSON.getProfessorIds()) {
                if (valueProfessor.equals(id)) {
                    resultProfessor = true;
                    break;
                }
            }
        }

        if (!UteisValidacao.emptyList(filtroAulasJSON.getModalidadeIds())) {
            filtroModalidade = true;
            for (Integer id : filtroAulasJSON.getModalidadeIds()) {
                if (valueModalide.equals(id)) {
                    resultModalidade = true;
                    break;
                }
            }
        }
        if (filtroAmbiente && filtroProfessor && filtroModalidade) {
            retorno = resultAmbiente && resultProfessor && resultModalidade;
        } else if (filtroAmbiente && filtroProfessor) {
            retorno = resultAmbiente && resultProfessor;
        } else if (filtroAmbiente && filtroModalidade) {
            retorno = resultAmbiente && resultModalidade;
        } else if (filtroProfessor && filtroModalidade) {
            retorno = resultProfessor && resultModalidade;
        } else if (filtroAmbiente) {
            retorno = resultAmbiente;
        } else if (filtroProfessor) {
            retorno = resultProfessor;
        } else if (filtroModalidade) {
            retorno = resultModalidade;
        }
        return retorno;
    }

    private AulaResponseDTO getAulaResponse(String ctx, TurmaAulaCheiaJSON aula) throws ServiceException {
        try {
            AulaResponseDTO aulaResponse = new AulaResponseDTO();
            aulaResponse.setId(aula.getCodigo());
            aulaResponse.setNome(aula.getNome());
            aulaResponse.setMensagem(aula.getMensagem());
            aulaResponse.setBonificacao(aula.getBonificacao().intValue());
            aulaResponse.setCapacidade(aula.getCapacidade());
            aulaResponse.setLimiteVagasAgregados(aula.getLimiteVagasAgregados() != null ? aula.getLimiteVagasAgregados() : 0);
            aulaResponse.setMeta(aula.getMeta().intValue());
            aulaResponse.setOcupacao(!UteisValidacao.emptyNumber(aula.getOcupacao()) ? FrequenciaEnum.getFromOrdinal(aula.getOcupacao()).name() : "");
            aulaResponse.setPontuacaoBonus(aula.getPontosBonus());
            aulaResponse.setDataInicio(Calendario.getDate("dd/MM/yyyy", aula.getInicio()).getTime());
            aulaResponse.setDataFinal(Calendario.getDate("dd/MM/yyyy", aula.getFim()).getTime());
            aulaResponse.setToleranciaMin(aula.getTolerancia());
            aulaResponse.setTipoTolerancia(aula.getTipoTolerancia());
            Ambiente ambiente = obterAmbientePorCodigoZW(ctx, aula.getAmbiente());
            if (ambiente != null) {
                aulaResponse.setAmbiente(new AmbienteResponseTO(ambiente, SuperControle.independente(ctx), false));
            } else {
                aulaResponse.setAmbiente(new AmbienteResponseTO());
            }

            Modalidade modalidade = obterModalidadePorCodigoZW(ctx, aula.getModalidade());
            aulaResponse.setModalidade(new ModalidadeResponseTO(modalidade, SuperControle.independente(ctx)));

            List<HorarioDTO> horarios = new ArrayList<>();
            String[] groupyHorarios = aula.getHorarios().split(";");
            if (!UteisValidacao.emptyString(aula.getHorarios()) && groupyHorarios != null && groupyHorarios.length > 0) {
                for (String groupyHorario : groupyHorarios) {
                    HorarioDTO horarioDTO = new HorarioDTO();

                    String[] horario = groupyHorario.split("-");
                    horarioDTO.setInicio(horario[0]);
                    horarioDTO.setFim(horario[1]);

                    horarios.add(horarioDTO);
                }
            }
            HorarioDTO[] horarioArray = new HorarioDTO[horarios.size()];
            aulaResponse.setHorarios(horarios.toArray(horarioArray));

            List<String> dias = new ArrayList<>();
            for (String dia : aula.getDias().split(";")) {
                DiasSemana diaEnum = DiasSemana.getDiaSemana(dia);
                if (diaEnum != null) {
                    dias.add(diaEnum.getChave());
                }
            }
            aulaResponse.setDias(aula.getDias().replace(';', ','));
            String[] diasArray = new String[dias.size()];
            aulaResponse.setDiasSemana(dias.toArray(diasArray));

            ProfessorSintetico professor = new ProfessorSintetico();
            professor.setCodigoColaborador(aula.getProfessor());
            professor.setNome(aula.getNomeProfessor());
            Usuario u = new Usuario();
            u.setProfessor(professor);
            aulaResponse.setProfessor(new ProfessorResponseTO(u, SuperControle.independente(ctx)));
            aulaResponse.setValidarRestricoesMarcacao(aula.isValidarRestricoesMarcacao());
            aulaResponse.setNaoValidarModalidadeContrato(aula.isNaoValidarModalidadeContrato());
            aulaResponse.setProdutoGymPass(aula.getProdutoGymPass() == null ? 0 : aula.getProdutoGymPass());
            aulaResponse.setIdClasseGymPass(aula.getIdClasseGymPass());
            aulaResponse.setUrlTurmaVirtual(aula.getUrlTurmaVirtual() == null ? "" : aula.getUrlTurmaVirtual());
            aulaResponse.setUrlVideoYoutube(aula.getUrlVideoYoutube());
            aulaResponse.setImageUrl(aula.getImageUrl());
            aulaResponse.setVisualizarProdutosGympass(aula.getVisualizarProdutosGympass());
            aulaResponse.setVisualizarProdutosTotalpass(aula.getVisualizarProdutosTotalpass());
            aulaResponse.setPermiteFixar(aula.getPermiteFixar());
            aulaResponse.setAulaIntegracaoSelfloops(aula.getAulaIntegracaoSelfloops());

            aulaResponse.setIdadeMaximaAnos(aula.getIdadeMaximaAnos());
            aulaResponse.setIdadeMinimaAnos(aula.getIdadeMinimaAnos());
            aulaResponse.setIdadeMaximaMeses(aula.getIdadeMaximaMeses());
            aulaResponse.setIdadeMinimaMeses(aula.getIdadeMinimaMeses());
            aulaResponse.setTipoReservaEquipamento(aula.getTipoReservaEquipamento());
            aulaResponse.setMapaEquipamentos(aula.getMapaEquipamentos());
            aulaResponse.setListaMapaEquipamentoAparelho(montarListaEquipamentoAparelho(ctx, aula.getCodigo(), aula.getMapaEquipamentos()));
            montarNiveisAula(ctx, aulaResponse, aula);

            return aulaResponse;
        } catch (Exception e) {
            throw new ServiceException(AulaExcecoes.ERRO_BUSCAR);
        }
    }

    @Override
    public List<MapaEquipamentoAparelhoDTO> montarListaEquipamentoAparelho(String ctx, Integer codigoAula, String mapaEquipamentos) throws Exception {
        if (UteisValidacao.emptyString(mapaEquipamentos)) {
            return new ArrayList<>();
        }

        Map<String, MapaEquipamentoAparelhoDTO> map = new HashMap<>();
        String[] posicoesMapa = mapaEquipamentos.startsWith(";") ? mapaEquipamentos.substring(1).split(";") : mapaEquipamentos.split(";");
        for (String posicao : posicoesMapa) {
            map.put(posicao, new MapaEquipamentoAparelhoDTO(posicao));
        }

        List<MapaEquipamentoAparelhoDTO> listaEquipamentoAparelho = new ArrayList<>();
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                StringBuilder sql = new StringBuilder();
                sql.append("select codigo, codigo_aparelhotreino, turma, mapaequipamento \n");
                sql.append("from turmamapaequipamentoaparelho \n");
                sql.append("where turma = ").append(codigoAula);
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                    while (rs.next()) {
                        Aparelho aparelho = aparelhoService.obterPorId(ctx, rs.getInt("codigo_aparelhotreino"));
                        if (aparelho != null && !UteisValidacao.emptyNumber(aparelho.getCodigo()) && aparelho.getUsarEmReservaEquipamentos()) {
                            String[] mapa = rs.getString("mapaequipamento").split(";");
                            for (int i = 0; i < mapa.length; i++) {
                                map.put(mapa[i],
                                        new MapaEquipamentoAparelhoDTO(
                                            mapa[i],
                                            0,
                                            aparelho.getCodigo(),
                                            aparelho.getNome(),
                                            aparelho.getSigla(),
                                            aparelho.getIcone(),
                                            false
                                        )
                                );
                            }
                        }
                    }
                }
            }

            Integer numeroPosicao = 1;
            for (String posicao : posicoesMapa) {
                MapaEquipamentoAparelhoDTO obj = map.get(posicao);
                listaEquipamentoAparelho.add(new MapaEquipamentoAparelhoDTO(
                        obj.getPosicaoMapa(),
                        numeroPosicao,
                        obj.getCodigoAparelho(),
                        obj.getNomeAparelho(),
                        obj.getSiglaAparelho(),
                        obj.getIconeAparelho(),
                        false
                ));
                numeroPosicao++;
            }

            return listaEquipamentoAparelho;
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    private void montarNiveisAula(String ctx, AulaResponseDTO aulaResponse, TurmaAulaCheiaJSON aula) throws Exception{
        if (aula.getNiveis() != null && !aula.getNiveis().isEmpty()) {
            String[] ids = aula.getNiveis().split(",");
            aulaResponse.setNiveis(new ArrayList<>());
            for (String id : ids) {
                Nivel nivel = this.nivelService.obterPorId(ctx, Integer.valueOf(id));
                if (nivel != null) {
                    aulaResponse.getNiveis().add(new NivelTO(nivel));
                }
            }
        }
    }

    @Override
    public AulaColetivaResponseDTO cadastroAulaV2(AulaColetivaResponseDTO aulaDTO, Integer codigoAula, Integer empresaZwId) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();

        if (SuperControle.independente(ctx)) {
            throw new ServiceException("Fluxo não permitido para treino independente");
        }

        try {
            boolean logInclusao = true;
            AulaColetivaResponseDTO aulaAntes = new AulaColetivaResponseDTO();
            if (UteisValidacao.emptyNumber(aulaDTO.getEmpresa())) {
                aulaDTO.setEmpresa(empresaZwId);
            }

            if (UteisValidacao.emptyNumber(codigoAula)) {
                aulaDTO = turmaDao.saveAulaColetiva(ctx, aulaDTO);
                if (aulaDTO != null && aulaDTO.getHorarios() != null) {
                    for (HorarioTurmaResponseDTO horarioDTO : aulaDTO.getHorarios()) {
                        horarioDTO.setTurma(aulaDTO.getCodigo());
                        horarioDTO.setDataEntrouTurma(Uteis.getDataAplicandoFormatacao(new Date(aulaDTO.getDataInicio()), "dd/MM/yyyy HH:mm:ss"));
                        horarioDTO.setIdentificadorTurma(aulaDTO.getDescricao());
                    }
                    List<HorarioTurmaResponseDTO> horarios = saveOrUpdateHorarios(aulaDTO.getHorarios());
                }
            } else {
                logInclusao = false;
                aulaAntes = turmaDao.obterAulaColetiva(ctx, codigoAula);
                aulaDTO = turmaDao.updateAulaColetiva(ctx, aulaDTO);
            }

            if (aulaDTO != null && !UteisValidacao.emptyNumber(aulaDTO.getCodigo())) {
                if (!UteisValidacao.emptyString(aulaDTO.getImageDataUpload())) {
                    gravarFotoAulaColetiva(ctx, aulaDTO);
                }
                else if (aulaDTO.getImageUrl() != null && aulaDTO.getImageUrl().isEmpty()) {
                    turmaDao.updateFotoKey(ctx, aulaDTO.getCodigo(), null);
                }

                if (!UteisValidacao.emptyNumber(aulaDTO.getCodigo())) {
                    gravarTurmaMapaEquipamentoAparelho(ctx, aulaDTO.getCodigo(), aulaDTO.getTurmaMapaEquipamentoAparelho());
                    gerenciamentoAulaIntegracaoSelfloops(ctx, aulaDTO);
                }

                if (aulaDTO.getLinkVideos() != null && aulaDTO.getLinkVideos().size() > 0) {
                    gravarLinkVideos(ctx, aulaDTO.getLinkVideos(), aulaDTO.getCodigo());
                }

                iniciarSincronizacaoGympass(ctx, aulaDTO.getCodigo(), aulaDTO.getProdutoGymPass(), aulaDTO.getEmpresa());
            }

            if (logInclusao) {
                incluirLog(
                        ctx,
                        aulaDTO.getCodigo().toString(),
                        "",
                        "",
                        getDescricaoAulaParaLog(aulaDTO, null),
                        "INCLUSÃO",
                        "INCLUSÃO DE AULA COLETIVA",
                        EntidadeLogEnum.AULA_COLETIVA,
                        "Aula Coletiva",
                        sessaoService,
                        logDao,
                        null, null);
            } else {
                incluirLog(
                        ctx,
                        aulaDTO.getCodigo().toString(),
                        "",
                        getDescricaoAulaParaLog(aulaAntes, aulaDTO),
                        getDescricaoAulaParaLog(aulaDTO, aulaAntes),
                        "ALTERAÇÃO",
                        "ALTERAÇÃO DE AULA COLETIVA",
                        EntidadeLogEnum.AULA_COLETIVA,
                        "Aula Coletiva",
                        sessaoService,
                        logDao,
                        null, null);
            }

            return aulaDTO;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AulaExcecoes.ERRO_INCLUIR, e);
        }
    }

    private void gravarFotoAulaColetiva(String ctx, AulaColetivaResponseDTO aulaDTO)  throws ServiceException {
        try {
            if (isNotBlank(aulaDTO.getImageDataUpload())) {
                String identificador = Calendario.getData(Calendario.hoje(), "ddMMyyyyhhMMss") + "-FotoAulaColetiva-" + aulaDTO.getCodigo();
                String fotoKeyAula = MidiaService.getInstance().uploadObjectFromByteArray(
                        ctx,
                        MidiaEntidadeEnum.FOTO_AULA_COLETIVA,
                        identificador,
                        Base64.decodeBase64(aulaDTO.getImageDataUpload().split(",")[1])
                );
                if (!UteisValidacao.emptyString(fotoKeyAula)) {
                    fotoKeyAula = fotoKeyAula  + "?time=" + new Date().getTime();
                }
                turmaDao.updateFotoKey(ctx, aulaDTO.getCodigo(), fotoKeyAula);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void gravarLinkVideos(String ctx, List<TurmaVideoDTO> linkVideos, Integer codigoTurma) throws Exception {
        try {
            List<TurmaVideoDTO> videosAntes = turmaDao.obterListaTurmaVideo(ctx, codigoTurma);
            for (TurmaVideoDTO t : videosAntes) {
                if (!linkVideos.contains(new TurmaVideoDTO(t))) {
                    turmaDao.excluirTurmaVideo(ctx, t);
                }
            }

            for (TurmaVideoDTO t : linkVideos) {
                t.setTurma_codigo(codigoTurma);
                TurmaVideoDTO turmaDto = turmaDao.obterTurmaVideo(ctx, t.getId());
                if (turmaDto != null) {
                    turmaDao.updateTurmaVideo(ctx, t);
                } else {
                    t = turmaDao.saveTurmaVideo(ctx, t);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(AulaExcecoes.ERRO_INCLUIR, ex);
        }
    }

    private void iniciarSincronizacaoGympass(String ctx, Integer codigoAula, Integer produtoGympass, Integer empresaAula) throws Exception {
        TurmaGymPassJSON turmaGymPassJSON = null;
        try {
            try {
                turmaGymPassJSON = agendaService.obterDadosTurmaTurmaGymPassJSON(ctx, codigoAula, produtoGympass);
            } catch (Exception ex) {
                turmaGymPassJSON = null;
            }
            if (turmaGymPassJSON != null) {
                agendaService.iniciaThreadGympass(
                        ctx,
                        turmaGymPassJSON.getCodigo(),
                        empresaAula,
                        0,
                        TipoThreadGympassEnum.SINCRONIZAR_TURMA
                );
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro ao iniciar sincronização booking da aula " + codigoAula + ", erro: " + ex.getMessage());
        }
    }

    private String getDescricaoAulaParaLog(AulaColetivaResponseDTO v1, AulaColetivaResponseDTO v2) {
        try {
            StringBuilder log = new StringBuilder();
            if (v2 == null) {
                log.append(UtilReflection.difference(v1, null));
            } else {
                log.append(UtilReflection.difference(v1, v2));
            }
            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log aula coletiva";
        }
    }

    @Override
    public List<HorarioTurmaResponseDTO> saveOrUpdateHorarios(List<HorarioTurmaResponseDTO> horarioDTO) {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        List<HorarioTurmaResponseDTO> horariosSalvos = new ArrayList<>();
        Integer codigoNivelTurmaSN = turmaDao.obterCodigoNivelTurmaSN(ctx);
        horarioDTO.forEach(h -> {
            try {
                if (h != null && (h.getCodigo() == null || h.getCodigo() == 0)) {
                    h.setNivelTurmaId(codigoNivelTurmaSN);
                    if (UteisValidacao.emptyString(h.getDataEntrouTurma())) {
                        h.setDataEntrouTurma(Uteis.getDataAplicandoFormatacao(new Date(), "dd/MM/yyyy HH:mm:ss"));
                    }
                    h.setHorarioDisponivelVenda(true);// utilizado para setar coluna ativo do horarioturma
                    DiasSemana ds = DiasSemana.getDiaSemana(h.getDia());
                    h.setDiaSemanaNumero(ds.getNumeral());
                    h.setSituacao("AT");
                    horariosSalvos.add(turmaDao.saveHorario(ctx, h));
                    incluirLog(
                            ctx,
                            h.getCodigo().toString(),
                            h.getTurma().toString(),
                            "",
                            getDescricaoHorarioParaLog(h, null),
                            "INCLUSÃO",
                            "INCLUSÃO DE HORARIO TURMA",
                            EntidadeLogEnum.HORARIO_TURMA,
                            "Horario Turma",
                            sessaoService,
                            logDao,
                            null, null);

                } else {
                    HorarioTurmaResponseDTO horarioAntes = turmaDao.obterHorarioTurma(ctx, h.getCodigo());
                    DiasSemana ds = DiasSemana.getDiaSemana(h.getDia());
                    h.setDiaSemanaNumero(ds.getNumeral());
                    h.setNivelTurmaId(horarioAntes.getNivelTurmaId());
                    h.setDataEntrouTurma(horarioAntes.getDataEntrouTurma());
                    HorarioTurmaResponseDTO horario = turmaDao.updateHorarioAulaColetiva(ctx, h);
                    incluirLog(
                            ctx,
                            horario.getCodigo().toString(),
                            horario.getTurma().toString(),
                            getDescricaoHorarioParaLog(horarioAntes, horario),
                            getDescricaoHorarioParaLog(horario, horarioAntes),
                            "ALTERAÇÃO",
                            "ALTERAÇÃO DE HORARIO TURMA",
                            EntidadeLogEnum.HORARIO_TURMA,
                            "Horario Turma",
                            sessaoService,
                            logDao,
                            null, null);

                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        });
        return horariosSalvos;
    }

    @Override
    public String removerHorarioAula(Integer codigoHorario, Integer empresaZwId) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        HorarioTurmaResponseDTO dto = turmaDao.obterHorarioTurma(ctx, codigoHorario);

        if (existeAlunosHorarioAulaColetivaFutura(codigoHorario)) {
            throw new ServiceException("Há alunos inseridos no horário da aula de " + dto.getDiaSemana_Apresentar() + " das " + dto.getHoraInicial() + " às " + dto.getHoraFinal() + " com o professor(a) " + dto.getProfessor() + ".");
        }

        dto.setSituacao("IN");
        dto.setHorarioDisponivelVenda(false);
        dto.setDataSaiuTurma(Calendario.getData("dd/MM/yyyy HH:mm:ss"));
        turmaDao.desativarHorarioAula(ctx, dto);

        incluirLog(
                ctx,
                dto.getCodigo().toString(),
                dto.getTurma().toString(),
                getDescricaoHorarioParaLog(dto, null),
                null,
                "EXCLUSÃO",
                "EXCLUSÃO DE HORARIO TURMA",
                EntidadeLogEnum.HORARIO_TURMA,
                "Horario Turma",
                sessaoService,
                logDao,
                null, null
        );

        // remover slot gympass caso exista **************
        TurmaGymPassJSON turmaGymPassJSON = null;
        try {
            turmaGymPassJSON = agendaService.obterDadosTurmaTurmaGymPassJSON(ctx, dto.getTurma(), null);
        } catch (Exception ex) {
            turmaGymPassJSON = null;
        }

        if (turmaGymPassJSON != null) {
            agendaService.iniciaThreadGympassRemoverSlot(ctx, dto.getTurma(), codigoHorario, turmaGymPassJSON.getEmpresaZW(), 0);
        }

        return "sucesso";
    }

    @Override
    public Boolean existeAlunosHorarioAulaColetivaFutura(Integer codigoHorario) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        HorarioTurmaResponseDTO dto = turmaDao.obterHorarioTurma(ctx, codigoHorario);
        Long alunosEmHorarioFuturo = turmaDao.existemAlunosEmHorarioFuturo(ctx, dto.getCodigo());

        return alunosEmHorarioFuturo.intValue() != 0;
    }

    private String getDescricaoHorarioParaLog(HorarioTurmaResponseDTO v1, HorarioTurmaResponseDTO v2) {
        try {
            StringBuilder log = new StringBuilder();
            if (v2 == null) {
                log.append(UtilReflection.difference(v1, null));
            } else {
                log.append(UtilReflection.difference(v1, v2));
            }
            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }

    @Override
    public AulaResponseDTO cadastroAula(AulaDTO aulaDTO, Integer id, Integer empresaId) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            AulaResponseDTO alunoRetorno = new AulaResponseDTO();
            if(SuperControle.independente(ctx)) {
              alunoRetorno = gravarAula(aulaDTO, id);
            }else{
                String retorno = cadastroAulaZw(aulaDTO, id, empresaId);
                if(!retorno.contains("ok")){
                    if (retorno.contains("horarioturma") && retorno.contains("aulamarcada_horarioturma_fkey")) {
                        throw new ServiceException(AulaExcecoes.HORARIO_NAO_PODE_SER_EXCLUIDO);
                    }else if(retorno.contains("alunos matriculados")){
                        throw new ServiceException("erro_aluno_matriculado", retorno);
                    }else if(retorno.contains("Existe aula marcada no dia")){
                        throw new ServiceException( " " +retorno.toString() , " ");
                    }else{
                        throw new ServiceException(AulaExcecoes.ERRO_INCLUIR);
                    }
                }
            }
            return alunoRetorno;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AulaExcecoes.ERRO_INCLUIR, e);
        }
    }

    @Override
    public EdicaoAulaTemporaria editarAulaTemporariamente(AulaDTO aulaDTO, Integer id, String dataAula) throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            Date dia = Uteis.getDate(dataAula, "yyyyMMdd");
            EdicaoAulaTemporaria edicaoAulaTemporaria = new EdicaoAulaTemporaria(aulaDTO);
            edicaoAulaTemporaria.setTipoAlteracao(TipoAlteracaoAulaEnum.SOMENTE_AULA.name());
            edicaoAulaTemporaria.setDiaAula(dia);
            edicaoAulaTemporaria.setLimite(dia);
            EdicaoAulaTemporaria edicaoBD = consultarEdicoesAulaTemporaria(ctx, id, dia);
            if(edicaoBD == null){
                inserirEdicaoAulaTemporaria(ctx, id + "_" + dataAula,
                        sessaoService.getUsuarioAtual().getId(),
                        id,
                        edicaoAulaTemporaria);
            } else {
                edicaoAulaTemporaria.setCodigo(edicaoBD.getCodigo());
                edicaoAulaTemporaria.setHorarioTurma(id);
                editarEdicaoAulaTemporaria(edicaoAulaTemporaria, edicaoBD);
            }
            return edicaoAulaTemporaria;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AulaExcecoes.ERRO_INCLUIR, e);
        }
    }

    @Override
    public AulaResponseDTO clonarAula(Integer codigoAulaOriginal, Integer empresaIdZw) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            AulaResponseDTO aulaRetorno = new AulaResponseDTO();
            Date hoje = new Date();
            if (SuperControle.independente(ctx)) {
                AulaResponseDTO aulaOriginal = new AulaResponseDTO(obterPorId(ctx, codigoAulaOriginal), SuperControle.independente(ctx));
                AulaDTO aulaDTO = aulaResponseDTOToAulaDTOClonar(aulaOriginal);
                aulaDTO.setNome(aulaDTO.getNome() + " - Clone");
                aulaDTO.setDataInicio(hoje.getTime());
                aulaDTO.setDataFinal(Uteis.somarDias(hoje, 1).getTime());
                aulaRetorno = gravarAula(aulaDTO, null);
            } else {
                AulaResponseDTO aulaOriginal = detalhesAula(codigoAulaOriginal, empresaIdZw, null);
                AulaColetivaResponseDTO aulaDTO = aulaResponseDTOToAulaDTOClonarV2(ctx, aulaOriginal);
                AulaColetivaResponseDTO aulaColetivaResponseDTO = cadastroAulaV2(aulaDTO, null, empresaIdZw);
                try {
                    List<HorarioTurmaResponseDTO> horarioTurmaResponseDTOS = turmaDao.listarTodosHorariosAtivosAulaColetiva(ctx, codigoAulaOriginal);
                    if (horarioTurmaResponseDTOS != null && horarioTurmaResponseDTOS.size() > 0) {
                        List<HorarioTurmaResponseDTO> cloneHorarios = new ArrayList<>();
                        for (HorarioTurmaResponseDTO horarioTurmaResponseDTO : horarioTurmaResponseDTOS) {
                            HorarioTurmaResponseDTO cloneHorario = new HorarioTurmaResponseDTO();
                            BeanUtils.copyProperties(horarioTurmaResponseDTO, cloneHorario);
                            cloneHorario.setCodigo(null);
                            cloneHorario.setTurma(aulaColetivaResponseDTO.getCodigo());
                            cloneHorario.setDataEntrouTurma(null);
                            cloneHorarios.add(cloneHorario);
                        }
                        saveOrUpdateHorarios(cloneHorarios);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    Uteis.logarDebug("#### [AulaServiceImpl.clonarAula] Erro ao tentar listar e clonar horários de aula coletiva: " + e.getMessage());
                }
            }
            return aulaRetorno;
        } catch (ServiceException ex) {
            throw new ServiceException(AulaExcecoes.ERRO_INCLUIR, ex);
        }
    }

    private AulaDTO aulaResponseDTOToAulaDTOClonar(AulaResponseDTO aulaResponseDTO) {
        AulaDTO aulaDTO = new AulaDTO();
        aulaDTO.setAmbienteId(aulaResponseDTO.getAmbiente() != null ? aulaResponseDTO.getAmbiente().getId().toString() : null);
        aulaDTO.setBonificacao(aulaResponseDTO.getBonificacao() != null ? aulaResponseDTO.getBonificacao().toString() : null);
        aulaDTO.setCapacidade(aulaResponseDTO.getCapacidade() != null ? aulaResponseDTO.getCapacidade().toString() : "0");
        aulaDTO.setDataFinal(aulaResponseDTO.getDataFinal());
        aulaDTO.setDataInicio(aulaResponseDTO.getDataInicio());
        aulaDTO.setDiasSemana(aulaResponseDTO.getDiasSemana());
        aulaDTO.setHorarios(aulaResponseDTO.getHorarios());
        aulaDTO.setMensagem(aulaResponseDTO.getMensagem());
        aulaDTO.setUrlVideoYoutube(aulaResponseDTO.getUrlVideoYoutube());
        aulaDTO.setMeta(aulaResponseDTO.getMeta() != null ? aulaResponseDTO.getMeta().toString() : null);
        aulaDTO.setModalidadeId(aulaResponseDTO.getModalidade() != null ? aulaResponseDTO.getModalidade().getId().toString() : null);
        aulaDTO.setNome(aulaResponseDTO.getNome());
        aulaDTO.setOcupacao(aulaResponseDTO.getOcupacao());
        aulaDTO.setPontuacaoBonus(aulaResponseDTO.getPontuacaoBonus() != null ? aulaResponseDTO.getPontuacaoBonus().toString() : null);
        aulaDTO.setProfessorId(aulaResponseDTO.getProfessor() != null ? aulaResponseDTO.getProfessor().getId().toString() : null);
        aulaDTO.setToleranciaMin(aulaResponseDTO.getToleranciaMin() != null ? aulaResponseDTO.getToleranciaMin().toString() : null);
        aulaDTO.setTipoTolerancia(aulaResponseDTO.getTipoTolerancia() != null ? aulaResponseDTO.getTipoTolerancia().toString() : null);
        aulaDTO.setValidarRestricoesMarcacao(aulaResponseDTO.getValidarRestricoesMarcacao());
        aulaDTO.setNaoValidarModalidadeContrato(aulaResponseDTO.getNaoValidarModalidadeContrato());
        aulaDTO.setProdutoGymPass(null);
        aulaDTO.setIdClasseGymPass(null);
        aulaDTO.setUrlTurmaVirtual(aulaResponseDTO.getUrlTurmaVirtual());
        aulaDTO.setVisualizarProdutosGympass(false);
        aulaDTO.setVisualizarProdutosTotalpass(false);
        aulaDTO.setPermiteFixar(aulaResponseDTO.getPermiteFixar());
        aulaDTO.setImage(null);
        aulaDTO.setImageUrl(aulaResponseDTO.getImageUrl());
        return aulaDTO;
    }

    private AulaColetivaResponseDTO aulaResponseDTOToAulaDTOClonarV2(String ctx, AulaResponseDTO aulaResponseDTO) throws ServiceException {
        AulaColetivaResponseDTO aulaDTO = new AulaColetivaResponseDTO();
        try {
            AulaColetivaResponseDTO aulaColetivaResponseDTO = turmaDao.obterAulaColetiva(ctx, aulaResponseDTO.getId());
            Date hoje = new Date();
            aulaDTO.setIdentificador(aulaResponseDTO.getNome() + " - Clone");
            aulaDTO.setDescricao(aulaColetivaResponseDTO.getDescricao());
            aulaDTO.setCor(aulaColetivaResponseDTO.getCor());
            aulaDTO.setNiveis(aulaResponseDTO.getNiveis());
            aulaDTO.setIdadeMinimaAnos(aulaResponseDTO.getIdadeMinimaAnos());
            aulaDTO.setIdadeMinimaMeses(aulaResponseDTO.getIdadeMinimaMeses());
            aulaDTO.setIdadeMaximaAnos(aulaResponseDTO.getIdadeMaximaAnos());
            aulaDTO.setIdadeMaximaMeses(aulaResponseDTO.getIdadeMaximaMeses());
            aulaDTO.setDataInicio(hoje.getTime());
            aulaDTO.setDataFinal(Uteis.somarDias(hoje, 1).getTime());
            aulaDTO.setBonificacao(Double.valueOf(aulaResponseDTO.getBonificacao() != null ? aulaResponseDTO.getBonificacao() : null));
            aulaDTO.setMeta(Double.valueOf(aulaResponseDTO.getMeta() != null ? aulaResponseDTO.getMeta() : null));
            aulaDTO.setModalidadeId(aulaResponseDTO.getModalidade() != null ? aulaResponseDTO.getModalidade().getId() : null);
            aulaDTO.setPontuacaoBonus(aulaResponseDTO.getPontuacaoBonus() != null ? aulaResponseDTO.getPontuacaoBonus() : null);
            aulaDTO.setToleranciaMin(aulaResponseDTO.getToleranciaMin() != null ? aulaResponseDTO.getToleranciaMin() : null);
            aulaDTO.setTipoTolerancia(aulaResponseDTO.getTipoTolerancia() != null ? aulaResponseDTO.getTipoTolerancia() : null);
            aulaDTO.setValidarRestricoesMarcacao(aulaResponseDTO.getValidarRestricoesMarcacao());
            aulaDTO.setNaoValidarModalidadeContrato(aulaResponseDTO.getNaoValidarModalidadeContrato());
            aulaDTO.setProdutoGymPass(null);
            aulaDTO.setIdClasseGymPass(null);
            aulaDTO.setUrlTurmaVirtual(aulaResponseDTO.getUrlTurmaVirtual());
            aulaDTO.setVisualizarProdutosGympass(aulaResponseDTO.getVisualizarProdutosGympass());
            aulaDTO.setVisualizarProdutosTotalpass(aulaResponseDTO.getVisualizarProdutosTotalpass());
            aulaDTO.setPermiteFixar(aulaResponseDTO.getPermiteFixar());
            aulaDTO.setAulaIntegracaoSelfloops(aulaResponseDTO.getAulaIntegracaoSelfloops());
            aulaDTO.setImageDataUpload(null);
            aulaDTO.setImageUrl(aulaResponseDTO.getImageUrl());
            return aulaDTO;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("Erro ao preparar dados da aula para clonar", e);
        }
    }

    public AulaResponseDTO gravarAula(AulaDTO aulaDTO, Integer id) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {

            Aula aula = new Aula(aulaDTO);
            if (id != null) {
                aula.setCodigo(id);
            }
            if (aulaDTO.getHorarios().length == 0) {
                throw new ServiceException(AulaExcecoes.HORARIO_NAO_INFORMADO);
            }
            List<AulaHorario> aulaHorario = obterHorarios(ctx, aula);
            List<AulaHorario> horariosSelecionados = new ArrayList<>();

            List<String> aulasInativas = new ArrayList<>();

            for (HorarioDTO h : aulaDTO.getHorarios()) {
                AulaHorario horario = new AulaHorario();
                horario.setInicio(h.getInicio());
                horario.setFim(h.getFim());
                horario.setAula(aula);
                horariosSelecionados.add(horario);

            }

            if (aulaHorario != null && aulaHorario.size() > 0) {
                aula.getHorarios().addAll(aulaHorario);
                /**
                 * Verifica se tem aula horario excluido
                 */
                for (AulaHorario ah : aulaHorario) {
                    boolean ativo = false;
                    for (AulaHorario horarioSelecionado : horariosSelecionados) {
                        if (ah.getInicio().equals(horarioSelecionado.getInicio()) && ah.getFim().equals(horarioSelecionado.getFim())) {
                            ativo = true;
                        }
                    }
                    if (!ativo) {
                        aulasInativas.add(ah.getInicio() + " - " + ah.getFim());
                    }
                }

                /**
                 * Adiciona novos horarios
                 */
                List<AulaHorario> novosHorarios = new ArrayList<>();
                for (AulaHorario horarioSlc : horariosSelecionados) {
                    boolean novo = true;
                    for (AulaHorario ah : aulaHorario) {
                        if (ah.getInicio().equals(horarioSlc.getInicio()) && ah.getFim().equals(horarioSlc.getFim())) {
                            novo = false;
                        }
                    }
                    if (novo) {
                        novosHorarios.add(horarioSlc);
                    }
                }
                if (novosHorarios.size() > 0) {
                    aula.getHorarios().addAll(novosHorarios);
                }
            } else {
                aula.getHorarios().addAll(horariosSelecionados);
            }

            List<String> dias = new ArrayList<String>();
            for (String d : aulaDTO.getDiasSemana()) {
                DiasSemana diaSem = DiasSemana.getDiaSemanaChave(d);
                dias.add(diaSem.getCodigo());
            }
            inserirAula(ctx, aula, dias, horariosSelecionados, aulasInativas);
            agendaService.iniciaThreadGympass(ctx, aula.getCodigo(), 0, 0, TipoThreadGympassEnum.SINCRONIZAR_TURMA);
            return new AulaResponseDTO(aula, SuperControle.independente(ctx));

        } catch (Exception e) {
            throw new ServiceException(AulaExcecoes.ERRO_INCLUIR, e);
        }
    }

    private String cadastroAulaZw(AulaDTO aulaDTO, Integer id, Integer empresaId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            Date dataInicial = new Date(aulaDTO.getDataInicio());
            Date dataFinal = new Date(aulaDTO.getDataFinal());

            TurmaAulaCheiaJSON turma = new TurmaAulaCheiaJSON();

            byte[] imgAnterior = aulaDTO.getImage();
            if (imgAnterior != null) {
                byte[] reduzirImgAnterior = ResizeImage.reduzirImagem(imgAnterior, 1000);
                turma.setImage(reduzirImgAnterior);
            } else {
                turma.setImage(aulaDTO.getImage());
            }

            for (String d : aulaDTO.getDiasSemana()) {
                DiasSemana diaSem = DiasSemana.getDiaSemanaChave(d);
                turma.setDias(turma.getDias() + ";" + diaSem.getCodigo());
            }
            turma.setDias(turma.getDias().replaceFirst(";", ""));

            for (HorarioDTO horario : aulaDTO.getHorarios()) {
                turma.setHorarios(turma.getHorarios() + ";" + horario.getInicio().trim() + " - " + horario.getFim().trim());
            }
            turma.setHorarios(turma.getHorarios().replaceFirst(";", ""));

            turma.setCodigo(id);
            turma.setNome(aulaDTO.getNome());

            StringBuilder sql = new StringBuilder("SELECT c.codigo, p.nome FROM colaborador c");
            sql.append(" INNER JOIN pessoa p on p.codigo = c.pessoa");
            sql.append(" INNER JOIN tipoColaborador tC on tC.colaborador = c.codigo");
            sql.append(" WHERE c.codigo = ").append(aulaDTO.getProfessorId());

            try (Connection conZW = conexaoZWService.conexaoZw(ctx);
                 ResultSet rsProfessorZw = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                if (rsProfessorZw.next()) {
                    int professorId = rsProfessorZw.getInt("codigo");
                    ColaboradorDTO colaboradorDTO = new ColaboradorDTO();
                    colaboradorDTO.setId(professorId);
                    colaboradorDTO.setNome(rsProfessorZw.getString("nome"));
                    turma.setProfessor(professorId);
                }
                turma.setModalidade(Integer.parseInt(aulaDTO.getModalidadeId()));
                turma.setAmbiente(Integer.parseInt(aulaDTO.getAmbienteId()));
                turma.setCapacidade(Integer.parseInt(aulaDTO.getCapacidade()));
                turma.setLimiteVagasAgregados(aulaDTO.getLimiteVagasAgregados() != null ? aulaDTO.getLimiteVagasAgregados() : 0);
                turma.setOcupacao(!StringUtils.isBlank(aulaDTO.getOcupacao()) ? FrequenciaEnum.valueOf(aulaDTO.getOcupacao()).ordinal() : null);
                turma.setInicio(Uteis.getDataAplicandoFormatacao(dataInicial, "dd/MM/yyyy"));
                turma.setFim(Uteis.getDataAplicandoFormatacao(dataFinal, "dd/MM/yyyy"));
                turma.setBonificacao((!StringUtils.isBlank(aulaDTO.getBonificacao()) ? Double.valueOf(aulaDTO.getBonificacao()) : 0.0));
                turma.setMensagem(aulaDTO.getMensagem());
                turma.setMeta((!StringUtils.isBlank(aulaDTO.getMeta()) ? Double.valueOf(aulaDTO.getMeta()) : 0.0));
                turma.setPontosBonus((!StringUtils.isBlank(aulaDTO.getPontuacaoBonus()) ? Integer.valueOf(aulaDTO.getPontuacaoBonus()) : 0));
                turma.setTolerancia((!StringUtils.isBlank(aulaDTO.getToleranciaMin()) ? Integer.valueOf(aulaDTO.getToleranciaMin().replace(".", "").replace(",", "")) : 0));
                turma.setTipoTolerancia((!StringUtils.isBlank(aulaDTO.getTipoTolerancia()) ? Integer.valueOf(aulaDTO.getTipoTolerancia()) : 1));
                turma.setEmpresa(empresaId);
                turma.setUrlVideoYoutube(aulaDTO.getUrlVideoYoutube());
                turma.setValidarRestricoesMarcacao(aulaDTO.getValidarRestricoesMarcacao());
                turma.setNaoValidarModalidadeContrato(aulaDTO.getNaoValidarModalidadeContrato());
                turma.setIdClasseGymPass(aulaDTO.getIdClasseGymPass());
                turma.setProdutoGymPass(aulaDTO.getProdutoGymPass());
                turma.setUrlTurmaVirtual(aulaDTO.getUrlTurmaVirtual());
                turma.setImageUrl(aulaDTO.getImageUrl());
                turma.setVisualizarProdutosGympass(aulaDTO.getVisualizarProdutosGympass());
                turma.setPermiteFixar(aulaDTO.getPermiteFixar());
                turma.setAulaIntegracaoSelfloops(aulaDTO.getAulaIntegracaoSelfloops());
                turma.setManterFotoAnterior(aulaDTO.getManterFotoAnterior());
                turma.setVisualizarProdutosTotalpass(aulaDTO.getVisualizarProdutosTotalpass());
                turma.setIdadeMaximaAnos(aulaDTO.getIdadeMaximaAnos());
                turma.setIdadeMaximaMeses(aulaDTO.getIdadeMaximaMeses());
                turma.setIdadeMinimaAnos(aulaDTO.getIdadeMinimaAnos());
                turma.setIdadeMinimaMeses(aulaDTO.getIdadeMinimaMeses());
                turma.setNiveis("");
                turma.setTipoReservaEquipamento(aulaDTO.getTipoReservaEquipamento());
                turma.setMapaEquipamentos(aulaDTO.getMapaEquipamentos());

                if (aulaDTO.getNiveis() != null) {
                    for (NivelTO nivel : aulaDTO.getNiveis()) {
                        turma.setNiveis(turma.getNiveis() + "," + nivel.getId());
                    }
                    turma.setNiveis(turma.getNiveis().replaceFirst(",", ""));
                }

                Integer idUsuario = sessaoService.getUsuarioAtual().getId();
                Usuario usuario = usuarioService.obterPorId(ctx, idUsuario);
                if (usuario != null) {
                    turma.setUsuario(usuario.getUsuarioZW() == null ? 0 : usuario.getUsuarioZW());
                }
                return inserirZW(ctx, turma, aulaDTO);
            }
            } catch (Exception e) {
                throw new ServiceException(AulaExcecoes.ERRO_INCLUIR, e);
            }
    }



    @Override
    public AulaResponseDTO alterar(Integer id, AulaDTO aulaDTO) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            Aula aula = new Aula(aulaDTO);
            aula.setCodigo(id);
            aula = alterar(ctx, aula);
            return new AulaResponseDTO(aula, SuperControle.independente(ctx));
        } catch(ServiceException e){
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AulaExcecoes.ERRO_ALTERAR, e);
        }
    }

    @Override
    public void resetAula(Integer id, Integer empresa) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
            CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
            HttpPost httpPost = new HttpPost(url + "/prest/aulacheia/reset");

            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("chave", ctx));
            params.add(new BasicNameValuePair("empresa", "0"));
            params.add(new BasicNameValuePair("codAula", id.toString()));

            httpPost.setEntity(new UrlEncodedFormEntity(params));
            CloseableHttpResponse response = client.execute(httpPost);
            ResponseHandler<String> handler = new BasicResponseHandler();
            String body = handler.handleResponse(response);
            client.close();

            gymPassBookingService.inativarUmaAula(ctx, empresa, "ZW-B-" + id, 0);
        } catch (Exception e) {
            throw new ServiceException(AulaExcecoes.ERRO_EXCLUIR, e);
        }
    }


    public void removerAula(Integer id) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            if (SuperControle.independente(ctx)) {
                Aula aula = new Aula();
                aula.setCodigo(id);
                excluirAula(ctx, aula);
            } else {
                Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());

                String retorno = "";
                if ( usuario != null ) {
                    retorno = excluirAulaCheia( ctx, id, usuario.getUsuarioZW() );
                }

                if (!retorno.toLowerCase().equals("ok")) {
                    throw new ServiceException(AulaExcecoes.ERRO_EXCLUIR);
                } else {
                    IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                    List<AgendaTotalJSON> list = integracaoWS.pesquisarHorarioTurmaPorTurma(ctx, id);

                    List<Integer> aulaHorarioSelecionado = new ArrayList<>();
                    for (AgendaTotalJSON aulaHorario : list) {
                        aulaHorarioSelecionado.add(Integer.parseInt(aulaHorario.getId()));
                    }
                    if (!aulaHorarioSelecionado.isEmpty()) {
                        professorSubstituidoDao.removerPorAulaHorario(ctx, aulaHorarioSelecionado);
                    }

                    Empresa empTreino = empresaService.obterPorIdZW(ctx, sessaoService.getUsuarioAtual().getEmpresaAtual());
                    if (empTreino != null && !UteisValidacao.emptyNumber(empTreino.getCodigo())) {
                        SelfLoopsConfiguracoes confiSelfloops = selfloopsConfiguracoesService.obterPorEmpresa(ctx, empTreino.getCodigo());
                        if (confiSelfloops != null && confiSelfloops.isIntegracaoRelizadaSucesso()) {
                            inativarAulaRemovidasSelfloops(ctx, id, confiSelfloops, null);
                        }
                    }
                }
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AulaExcecoes.ERRO_EXCLUIR, e);
        }
    }

    private Integer obterCodigoAulaPorHorario(String chave, Integer horario){
        String sql = "select turma from horarioturma where codigo = " + horario;
        try (Connection conZW = conexaoZWService.conexaoZw(chave);
             ResultSet rsTurma = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
            return rsTurma.next() ? rsTurma.getInt("turma") : null;
        }catch (Exception e){
            Uteis.logar(e, AulaServiceImpl.class);
            return null;
        }
    }

    @Override
    public AulaColetivaResponseDTO detalhesAulaColetiva(Integer id, Integer empresaId, Integer horario) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();

        if (SuperControle.independente(ctx)) {
            throw new ServiceException("Fluxo não permitido para treino independente");
        }

        try {
            if (id == null && horario != null) {
                id = obterCodigoAulaPorHorario(ctx, horario);
            }
            if (id == null || id < 1) {
                throw new ServiceException(AulaExcecoes.ERRO_aula_NAO_EXISTE);
            }
            AulaColetivaResponseDTO aula = turmaDao.obterAulaColetiva(ctx, id);
            if (aula != null) {
                aula.setLinkVideos(turmaDao.obterListaTurmaVideo(ctx, aula.getCodigo()));
                if (!UteisValidacao.emptyString(aula.getImageUrl()) && !aula.getImageUrl().startsWith("http")) {
                    aula.setImageUrl(Aplicacao.obterUrlFotoDaNuvem(aula.getImageUrl()));
                }
            }
            aula.setListaMapaEquipamentoAparelho(montarListaEquipamentoAparelho(ctx, aula.getCodigo(), aula.getMapaEquipamentos()));

            return aula;
        } catch(ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AulaExcecoes.ERRO_BUSCAR, e);
        }
    }

    @Override
    public List<HorarioTurmaResponseDTO> obterHorariosAulaColetiva(JSONObject filtros, PaginadorDTO paginadorDTO, Integer codigoAula) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        if (SuperControle.independente(ctx)) {
            throw new ServiceException("Fluxo não permitido para treino independente");
        }
        if (UteisValidacao.emptyNumber(codigoAula)) {
            codigoAula = 0;
        }
        return turmaDao.listarHorariosTurma(ctx, filtros, paginadorDTO, codigoAula);
    }

    @Override
    public AulaResponseDTO detalhesAula(Integer id, Integer empresaId, Integer horario) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        aulaDao.getCurrentSession(ctx).clear();
        try {
            if (id == null && horario != null){
                id = obterCodigoAulaPorHorario(ctx, horario);
            }
            if(id == null || id < 1) {
                throw new ServiceException(AulaExcecoes.ERRO_aula_NAO_EXISTE);
            }
            AulaResponseDTO aulaRet = new AulaResponseDTO();
            if (SuperControle.independente(ctx)) {
                aulaRet = new AulaResponseDTO(obterPorId(ctx, id), SuperControle.independente(ctx));
            } else {
                List<TurmaAulaCheiaJSON> aulas = obterAulasColetivas(ctx, id, empresaId, null, null);
                if (aulas != null && !aulas.isEmpty()) {
                    aulaRet = getAulaResponse(ctx, aulas.get(0));
                }
                if(aulaRet != null){
                    aulaRet.setLinkVideos(turmaDao.obterListaTurmaVideo(ctx, aulaRet.getId()));
                }
                if (!UteisValidacao.emptyNumber(horario)) {
                    HorarioTurmaResponseDTO htDTO = turmaDao.obterHorarioTurma(ctx, horario);
                    if (htDTO != null) {
                        if (!UteisValidacao.emptyNumber(htDTO.getMaxAlunos()) && UteisValidacao.emptyNumber(aulaRet.getCapacidade())) {
                            aulaRet.setCapacidade(htDTO.getMaxAlunos());
                        }
                        if (!UteisValidacao.emptyNumber(htDTO.getProfessorId()) && (aulaRet.getProfessor() != null && UteisValidacao.emptyNumber(aulaRet.getProfessor().getId()))) {
                            ProfessorSintetico professor = new ProfessorSintetico();
                            professor.setCodigoColaborador(htDTO.getProfessorId());
                            professor.setNome(htDTO.getProfessor());
                            Usuario u = new Usuario();
                            u.setProfessor(professor);
                            aulaRet.setProfessor(new ProfessorResponseTO(u, SuperControle.independente(ctx)));
                        }
                        if (!UteisValidacao.emptyNumber(htDTO.getAmbienteId()) && (aulaRet.getAmbiente() != null && UteisValidacao.emptyNumber(aulaRet.getAmbiente().getId()))) {
                            Ambiente ambiente = obterAmbientePorCodigoZW(ctx, htDTO.getAmbienteId());
                            if (ambiente != null) {
                                aulaRet.setAmbiente(new AmbienteResponseTO(ambiente, SuperControle.independente(ctx), false));
                            } else {
                                aulaRet.setAmbiente(new AmbienteResponseTO());
                            }
                        }
                    }
                }
            }
            return aulaRet;
        } catch(ServiceException e){
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ModalidadeExcecoes.ERRO_BUSCAR_MODALIDADE, e);
        }
    }

    @Override
    public List<TvAulaDTO> listaAulasHorario(Integer empresaId, HttpServletRequest request) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<TvAulaDTO> ret = new ArrayList<>();
            int dayOfWeek = Calendario.getInstance(new Date()).get(Calendar.DAY_OF_WEEK);
            if (SuperControle.independente(ctx)) {
                DiasSemana diasSemana = DiasSemana.getDiaSemanaNumeral(dayOfWeek);
                List<Aula> aulas = aulaDao.aulasDiaSemana(ctx, diasSemana.getCodigo());
                for (Aula aula : aulas) {
                    if (Calendario.getDataComHoraZerada(aula.getDataFim()).after(Calendario.getDataComHoraZerada(new Date()))) {
                        for (AulaHorario aulaHorario : aula.getHorarios()) {
                            if (aulaHorario.getAtivo() && new Date().before(Calendario.getDataComHora(new Date(), aulaHorario.getFim()))) {
                                TvAulaDTO tvAulaResponse = new TvAulaDTO();
                                tvAulaResponse.setAulaHorarioId(aulaHorario.getCodigo());
                                tvAulaResponse.setNome(aula.getNome());
                                tvAulaResponse.setMensagem(aula.getMensagem());
                                tvAulaResponse.setAmbiente(new AmbienteResponseTO(aula.getAmbiente(), true, false));
                                tvAulaResponse.setModalidade(
                                        new ModalidadeSimplesDTO(
                                                aula.getModalidade().getCodigo(),
                                                aula.getModalidade().getNome(),
                                                aula.getModalidade().getCor().getCor()));
                                aula.getProfessor().setUriImagem(
                                        fotoService.defineURLFotoPessoa(
                                                request, aula.getProfessor().getPessoa().getFotoKey(), null, false, ctx, false));
                                tvAulaResponse.setProfessor(new ColaboradorSimplesTO(aula.getProfessor(), true));
                                tvAulaResponse.setHorarioInicio(aulaHorario.getInicio());
                                tvAulaResponse.setHorarioFim(aulaHorario.getFim());

                                ret.add(tvAulaResponse);
                            }
                        }
                    }
                }
            } else {
                IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                List<TurmaAulaCheiaJSON> aulasCheia = integracaoWS.obterTurmasAulaCheiaPorDia(ctx, empresaId, dayOfWeek);

                for (TurmaAulaCheiaJSON aulaCheia : aulasCheia) {
                    TvAulaDTO tvAulaResponse = new TvAulaDTO(aulaCheia);
                    ColaboradorSimplesTO professor = new ColaboradorSimplesTO();
                    professor.setId(aulaCheia.getProfessor());
                    professor.setNome(aulaCheia.getNomeProfessor());
                    professor.setImageUri(Aplicacao.obterUrlFotoDaNuvem(aulaCheia.getProfessorFotokey()));
                    tvAulaResponse.setProfessor(professor);

                    ret.add(tvAulaResponse);
                }
            }
            Collections.sort(ret);
            return ret;
        } catch (Exception ex) {
            throw new ServiceException(AulaExcecoes.ERRO_lISTAR, ex);
        }
    }

    @Override
    public TvAulaDTO detalhesAulaHorario(HttpServletRequest request, Integer empresaId, Integer horarioAulaId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            TvAulaDTO tvAulaResponse = new TvAulaDTO();

            if (SuperControle.independente(ctx)) {
                AulaHorario aulaHorario = aulaHorarioDao.findById(ctx, horarioAulaId);

                tvAulaResponse.setAulaHorarioId(aulaHorario.getCodigo());
                tvAulaResponse.setNome(aulaHorario.getAula().getNome());
                tvAulaResponse.setMensagem(aulaHorario.getAula().getMensagem());
                tvAulaResponse.setAmbiente(new AmbienteResponseTO(aulaHorario.getAula().getAmbiente(), true, false));
                tvAulaResponse.setModalidade(
                        new ModalidadeSimplesDTO(
                                aulaHorario.getAula().getModalidade().getCodigo(),
                                aulaHorario.getAula().getModalidade().getNome(),
                                aulaHorario.getAula().getModalidade().getCor().getCor()));
                aulaHorario.getAula().getProfessor().setUriImagem(
                        fotoService.defineURLFotoPessoa(
                                request, aulaHorario.getAula().getProfessor().getPessoa().getFotoKey(), null, false, ctx, false));
                tvAulaResponse.setProfessor(new ColaboradorSimplesTO(aulaHorario.getAula().getProfessor(), true));
                tvAulaResponse.setHorarioInicio(aulaHorario.getInicio());
                tvAulaResponse.setHorarioFim(aulaHorario.getFim());
                List<AulaAluno> alunos = aulaAlunoDao.alunosHorarioDia(ctx, horarioAulaId, new Date());
                List<AlunoResponseTO> alunoRet = new ArrayList<>();
                for (AulaAluno aulaAluno : alunos) {
                    aulaAluno.getCliente().setUrlFoto(fotoService.defineURLFotoPessoa(request, aulaAluno.getCliente().getPessoa().getFotoKey(), null, false, ctx, false));
                    alunoRet.add(new AlunoResponseTO(aulaAluno.getCliente(), false, SuperControle.independente(ctx)));
                }
                tvAulaResponse.getAlunos().addAll(alunoRet);
            } else {
                final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWeb);
                IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                AgendaTotalJSON aulaCheia = integracaoWS.consultarUmaAula(ctx, Calendario.getData(Calendario.MASC_DATA), horarioAulaId);
                if (aulaCheia == null) {
                    throw new ServiceException(AulaExcecoes.AULA_NAO_ENCONTRADA);
                }
                List<AlunoAulaAcessoJSON> alunosJSON;
                try {
                    alunosJSON = integracaoWS.obterAlunosDaAulaComAcesso(url, ctx, horarioAulaId, Calendario.getData(Calendario.MASC_DATA));
                } catch (Exception ex) {
                    throw new ServiceException(AmbienteExcecoes.AMBIENTE_SEM_COLETOR);
                }
                tvAulaResponse.setAulaHorarioId(horarioAulaId);
                tvAulaResponse.setNome(aulaCheia.getTitulo());
                tvAulaResponse.setMensagem(aulaCheia.getMensagem());
                Date inicioJSON = Calendario.getDate("dd/MM/yyyy HH:mm", aulaCheia.getInicio());
                tvAulaResponse.setHorarioInicio(Calendario.getHora(inicioJSON, "HH:mm"));
                Date fimJSON = Calendario.getDate("dd/MM/yyyy HH:mm", aulaCheia.getFim());
                tvAulaResponse.setHorarioFim(Calendario.getHora(fimJSON, "HH:mm"));
                tvAulaResponse.setAmbiente(new AmbienteResponseTO(aulaCheia.getCodigoLocal(), aulaCheia.getLocal()));
                tvAulaResponse.setModalidade(new ModalidadeSimplesDTO(aulaCheia.getCodigoTipo(), aulaCheia.getTitulo(), ""));

                ColaboradorSimplesTO professor = new ColaboradorSimplesTO();
                professor.setId(aulaCheia.getCodigoResponsavel());
                professor.setNome(aulaCheia.getResponsavel());
                professor.setImageUri(Aplicacao.obterUrlFotoDaNuvem(aulaCheia.getFotoProfessor()));
                tvAulaResponse.setProfessor(professor);
                List<AlunoResponseTO> alunos = new ArrayList<>();
                for (AlunoAulaAcessoJSON alunoJSON : alunosJSON) {
                    AlunoResponseTO aluno = new AlunoResponseTO();
                    aluno.setId(alunoJSON.getCodigo());
                    aluno.setNome(alunoJSON.getNome());
                    aluno.setImageUri(Aplicacao.obterUrlFotoDaNuvem(alunoJSON.getFotokey()));
                    if (alunoJSON.isAcessoViaFace()) {
                        aluno.setTipoAcesso(TipoAcessoAulaEnum.RECONHECIMENTO_FACIAL);
                    } else if (alunoJSON.isAcessoViaFingerPrint()) {
                        aluno.setTipoAcesso(TipoAcessoAulaEnum.FINGER_PRINT);
                    } else if (alunoJSON.isAcessoViakeyboard()) {
                        aluno.setTipoAcesso(TipoAcessoAulaEnum.KEYBOARD);
                    } else if (alunoJSON.isAcessoViaMobile()) {
                        aluno.setTipoAcesso(TipoAcessoAulaEnum.MOBILE);
                    }
                    alunos.add(aluno);
                }
                tvAulaResponse.getAlunos().addAll(alunos);

            }

            return tvAulaResponse;
        } catch (Exception ex) {
            throw new ServiceException(AulaExcecoes.AULA_NAO_ENCONTRADA, ex);
        }
    }

    protected void prepare(final String ctx) throws ServiceException {
        EntityManagerService ems = (EntityManagerService) UtilContext.getBean(EntityManagerService.class);
        try {
            ems.closeQuietly(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<TurmaAulaCheiaJSON> getTurmas() {
        return turmas;
    }

    public void setTurmas(List<TurmaAulaCheiaJSON> turmas) {
        this.turmas = turmas;
    }

    public List getListaAulaFiltrada() {
        return listaAulaFiltrada;
    }

    public void setListaAulaFiltrada(List listaAulaFiltrada) {
        this.listaAulaFiltrada = listaAulaFiltrada;
    }
    @Override
    public List<LogTO> listarLogAula(String dia, String id, Integer empresa, JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            Date date = Uteis.getDate(dia, "yyyyMMdd");
            List<LogTO> listarLog = new ArrayList<>();
            String ctx = sessaoService.getUsuarioAtual().getChave();
            if (filtros == null) {
                filtros = new JSONObject();
            }
            String timeZone = empresaService.obterFusoHorarioEmpresa(ctx, empresa);
            filtros.put("idAula", id + "_" + Uteis.getDataAplicandoFormatacao(date, "dd/MM/yyyy"));
            JSONObject jsonObject = chamadaZW(ctx, "/prest/aulacheia/log-aula", null, paginadorDTO, filtros, empresa);
            JSONArray content = new JSONArray(jsonObject.getString("content"));

            for(int i = 0; i < content.length(); i++){
                JSONObject log = content.getJSONObject(i);
                long dataalteracao = log.getLong("dataalteracao");
                Date dataNoFuso = aplicaTimeZone(new Date(dataalteracao), timeZone);
                String dataHora   = Uteis.getDataAplicandoFormatacaoTZ(dataNoFuso, "dd/MM/yyyy HH:mm", timeZone);
                String apenasHora = Uteis.getDataAplicandoFormatacaoTZ(dataNoFuso, "HH:mm:ss",       timeZone);
                LogTO logTO = new LogTO(log.getInt("codigo"), log.getString("nomeentidadedescricao"),
                        log.getString("nomeentidadedescricao"),
                        log.getString("responsavelalteracao"),
                        dataHora, apenasHora,
                        new ArrayList());
                logTO.setDescricao(UteisValidacao.emptyString(log.optString("valorcampoalterado")) ?
                        log.getString("valorcampoanterior") : log.getString("valorcampoalterado"));
                logTO.getAlteracoes().add(new AlteracoesTO(log.getString("nomecampo"), log.getString("valorcampoanterior"), log.getString("valorcampoalterado")));
                listarLog.add(logTO);
                }

            long total = listarLog.size();
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(total);
            }

            long size = paginadorDTO.getSize() != null ? paginadorDTO.getSize() : 10L;
            long page = paginadorDTO.getPage() != null ? paginadorDTO.getPage() : 0L;

            long totalPaginas = (total + size - 1) / size;
            if (page >= totalPaginas && totalPaginas > 0) {
                page = totalPaginas - 1;
                paginadorDTO.setPage(page);
            } else if (page < 0) {
                page = 0;
                paginadorDTO.setPage(page);
            }

            int start = (int) (page * size);
            int end = Math.min(start + (int) size, (int) total);
            List<LogTO> paginatedList = listarLog.subList(start, end);

            return paginatedList;
        }catch (Exception e){
            Uteis.logar(e, AulaServiceImpl.class);
            throw new ServiceException(e);
        }
    }

    @Override
    public List<LogTO> listarLogAgendaAulas(String id, Integer empresa, JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<LogTO> listarLog = new ArrayList<>();
            String ctx = sessaoService.getUsuarioAtual().getChave();
            if(filtros == null){
                filtros = new JSONObject();
            }
            if(id != null){
                filtros.put("idAula", id);
            }
            JSONObject jsonObject = chamadaZW(ctx, "/prest/aulacheia/log-agenda-aulas", null, paginadorDTO, filtros, empresa);
            if(paginadorDTO != null){
                paginadorDTO.setQuantidadeTotalElementos(new Long(jsonObject.optInt("total")));
            }
            JSONArray content = new JSONArray(jsonObject.getString("content"));
            for(int i = 0; i < content.length(); i++){
                JSONObject log = content.getJSONObject(i);
                JSONArray alteracoes = log.getJSONArray("alteracoes");
                long dataalteracao = log.getLong("dataalteracao");
                TipoRevisaoEnum operacao = TipoRevisaoEnum.getFromAdm(log.getString("operacao"));
                LogTO logTO = new LogTO(log.getInt("codigo"),
                        operacao == null ? "" : operacao.getDescricaoLog(),
                        log.getString("descricao"),
                        log.getString("username"),
                        Uteis.getDataAplicandoFormatacao(new Date(dataalteracao), "dd/MM/yyyy HH:mm"),
                        Uteis.getDataAplicandoFormatacao(new Date(dataalteracao), "HH:mm:ss"),
                        new ArrayList());
                for(int j = 0; j < alteracoes.length(); j++) {
                    JSONObject logInterno = alteracoes.getJSONObject(j);
                    logTO.getAlteracoes().add(new AlteracoesTO(logInterno.getString("nomecampo"), logInterno.getString("valorcampoanterior"), logInterno.getString("valorcampoalterado")));
                }

                for (AlteracoesTO a : logTO.getAlteracoes()) {
                    logTO.setDescricao(
                            logTO.getDescricao() +
                                    ("[" + a.getCampo() + ":'"
                                            + (UteisValidacao.emptyString(a.getValorAnterior()) ? "" : (a.getValorAnterior() + "' para'"))
                                            + a.getValorAlterado() + "']<br/>")
                    );
                }

                listarLog.add(logTO);
            }

            return listarLog;
        }catch (Exception e){
            Uteis.logar(e, AulaServiceImpl.class);
            throw new ServiceException(e);
        }
    }

    public Boolean verificarAulasCrossfitAluno(String ctx, String matricula) throws Exception {
        JSONObject filtros = new JSONObject();
        filtros.put("matricula", matricula);
        filtros.put("dia", Uteis.getDataAplicandoFormatacao(new Date(), "yyyy-MM-dd"));
        JSONObject jsonObject = chamadaZW(ctx, "/prest/aulacheia/verificar-aulas-crossfit-aluno", null, null, filtros, null);
        return jsonObject.getBoolean("content");
    }

    private JSONObject chamadaZW(String ctx,
              String endpoint,
              Integer codigo,
              PaginadorDTO paginadorDTO,
              JSONObject filtroAulasJSON, Integer empresa) throws Exception{

        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);

        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        if (empresa != null) {
            params.add(new BasicNameValuePair("empresa", empresa.toString()));
        }
        if (codigo != null) {
            params.add(new BasicNameValuePair("codigo", codigo.toString()));
        }
        if(paginadorDTO != null){
            params.add(new BasicNameValuePair("page", paginadorDTO.getPage().toString()));
            params.add(new BasicNameValuePair("size", paginadorDTO.getSize().toString()));
            params.add(new BasicNameValuePair("sort", paginadorDTO.getSort()));
        }
        if(filtroAulasJSON != null){
            params.add(new BasicNameValuePair("filtros", filtroAulasJSON.toString()));
        }
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }


    @Override
    public boolean alunoEmAula(String ctx, String matricula, Integer codigoHorarioTurma, Date dia, String chaveOrigem) throws Exception {
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost;
        if (!UteisValidacao.emptyString(chaveOrigem) && !ctx.equals(chaveOrigem)) {
            httpPost = new HttpPost(url + "/prest/aulacheia/aluno-gestao-rede-em-aula");
        } else {
            httpPost = new HttpPost(url + "/prest/aulacheia/aluno-em-aula");
        }

        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("matricula", matricula.toString()));
        params.add(new BasicNameValuePair("idHorarioTurma", codigoHorarioTurma.toString()));
        params.add(new BasicNameValuePair("dia", String.valueOf(dia.getTime())));
        if (!UteisValidacao.emptyString(chaveOrigem)) {
            params.add(new BasicNameValuePair("chaveOrigem", chaveOrigem));
        }
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        JSONObject jsonObject = new JSONObject(body);
        return jsonObject.getBoolean("content");
    }

    public boolean aulaFoiExlcuida(Integer codigoAula, String ctx, String dia) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("select * from auladiaexclusao where codigohorarioturma = ").append(codigoAula);
        sb.append(" and dataauladia = '").append(Uteis.getDataJDBC(Uteis.getDate(dia))).append("'");
        try (ResultSet rs = aulaDiaExclusaoDao.createStatement(ctx, sb.toString())) {
            if (rs.next()) {
                return true;
            }
        }

        return false;
    }

    public boolean horarioTurmaExiste(Integer codigoAula, String ctx) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            ResultSet rs = ConexaoZWServiceImpl.criarConsulta("select * from horarioturma where codigo = "
                    + codigoAula + " and situacao = 'AT'", conZW);
            if (rs.next()) {
                Integer codTurma = rs.getInt("turma");
                try (ResultSet rs2 = ConexaoZWServiceImpl.criarConsulta("select * from turma where codigo = " + codTurma, conZW)) {
                    if (rs2.next()) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    public Integer atualizarAgendamentoRemovendoPassivo(String ctx, Integer aulaHorarioId, Date dia, String nomePassivo, Integer codigoPassivo) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT codigo, codigocliente, matricula FROM ClienteSintetico cs ");
        sql.append(" WHERE upper(trim(cs.nome)) = '"+nomePassivo.trim().toUpperCase()+"' limit 1;");
        try (ResultSet rs = aulaDao.createStatement(ctx, sql.toString())) {
            if (rs.next()) {
                try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                    ConexaoZWServiceImpl.executarConsulta("update alunohorarioturma set cliente = " +
                            rs.getInt("codigocliente") + ", passivo = null where horarioturma = "+aulaHorarioId+" and passivo = " +
                            codigoPassivo + " and dia = '"+Uteis.getDataFormatoBD(dia)+"'", conZW);
                    ConexaoZWServiceImpl.executarConsulta("update passivo set cliente = " + rs.getInt("codigocliente") + " where codigo = " +
                            codigoPassivo, conZW);
                    ConexaoZWServiceImpl.executarConsulta("update fecharmetadetalhado set cliente = " + rs.getInt("codigocliente") + " where passivo = " +
                            codigoPassivo, conZW);
                    ConexaoZWServiceImpl.executarConsulta("update historicocontato set cliente = " + rs.getInt("codigocliente") + " where passivo = " +
                            codigoPassivo, conZW);
                }
                return rs.getInt("matricula");
            }
        }
        return 0;
    }

    public Integer atualizarAgendamentoRemovendoIndicado(String ctx, Integer aulaHorarioId, Date dia, String nomeIndicado, Integer codigoIndicado) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT codigo, codigocliente, matricula FROM ClienteSintetico cs ");
        sql.append(" WHERE upper(trim(cs.nome)) = '"+nomeIndicado.trim().toUpperCase()+"' limit 1;");
        try (ResultSet rs = aulaDao.createStatement(ctx, sql.toString())) {
            if (rs.next()) {
                try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                    ConexaoZWServiceImpl.executarConsulta("update alunohorarioturma set cliente = " +
                            rs.getInt("codigocliente") + ", indicado = null where horarioturma = "+aulaHorarioId+" and indicado = " +
                            codigoIndicado + " and dia = '"+Uteis.getDataFormatoBD(dia)+"'", conZW);
                    ConexaoZWServiceImpl.executarConsulta("update indicado set cliente = " + rs.getInt("codigocliente") + " where codigo = " +
                            codigoIndicado, conZW);
                    ConexaoZWServiceImpl.executarConsulta("update fecharmetadetalhado set cliente = " + rs.getInt("codigocliente") + " where indicado = " +
                            codigoIndicado, conZW);
                    ConexaoZWServiceImpl.executarConsulta("update historicocontato set cliente = " + rs.getInt("codigocliente") + " where indicado = " +
                            codigoIndicado, conZW);
                }
                return rs.getInt("matricula");
            }
        }
        return 0;
    }

    public List<FilaDeEsperaDTO> consultarFilaEspera(Integer codigoHorarioTurma, String dia) throws ServiceException {
        List<FilaDeEsperaDTO> filaEspera = new ArrayList<>();
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            String sql = "SELECT f.codigo, f.horarioturma, f.dataentrada, f.cliente, c.matricula, " +
                    "s.situacao, s.situacaoContrato, p.nome AS nomeAluno, ht.horainicial, ht.horafinal, f.ordem " +
                    "FROM filaesperaturma f " +
                    "JOIN cliente c ON f.cliente = c.codigo " +
                    "JOIN situacaoclientesinteticodw s ON f.cliente = s.codigocliente " +
                    "JOIN pessoa p ON c.pessoa = p.codigo " +
                    "JOIN horarioturma ht ON ht.codigo = f.horarioturma " +
                    "WHERE f.horarioturma = ? AND f.dataregistro = ? " +
                    "ORDER BY f.ordem ASC, f.dataentrada ASC";

            try (Connection conZW = conexaoZWService.conexaoZw(ctx);
                 PreparedStatement ps = conZW.prepareStatement(sql)) {

                ps.setInt(1, codigoHorarioTurma);
                ps.setDate(2, new java.sql.Date(Calendario.getDate("yyyyMMdd", dia).getTime()));

                try (ResultSet rs = ps.executeQuery()) {
                    int totalRegistros = 0;
                    while (rs.next()) {
                        FilaDeEsperaDTO filaDeEspera = new FilaDeEsperaDTO();
                        filaDeEspera.setCodigoHorarioTurma(rs.getInt("horarioturma"));
                        filaDeEspera.setDia(rs.getString("dataentrada"));
                        filaDeEspera.setCodigoAluno(rs.getInt("cliente"));
                        filaDeEspera.setMatricula(rs.getInt("matricula"));
                        filaDeEspera.setSituacaoAluno(rs.getString("situacao"));
                        filaDeEspera.setSituacaoContrato(rs.getString("situacaoContrato"));
                        filaDeEspera.setNomeAluno(rs.getString("nomeAluno"));
                        filaDeEspera.setCodigoFila(rs.getInt("codigo"));
                        filaDeEspera.setOrdem(rs.getInt("ordem"));
                        filaDeEspera.setVinculoComAula(String.valueOf(AlunoVinculoAulaEnum.ESPERA));

                        String sqlParq = "select r.codigo  from respostaclienteparq r " +
                                "inner join clientesintetico c on c.codigo = r.cliente_codigo " +
                                "where r.parqpositivo is true and c.codigocliente ="+rs.getInt("cliente");

                        try (ResultSet st = this.aulaDao.createStatement(ctx, sqlParq)) {
                            if (st.next()) {
                                filaDeEspera.setParqPositivo(true);
                            }
                        }

                        filaEspera.add(filaDeEspera);
                        totalRegistros++;
                    }
                }

            } catch (ServiceException e) {
                Uteis.logar(e, AulaServiceImpl.class);
                throw e;
            } catch (SQLException e) {
                Uteis.logar(new ServiceException("Erro SQL na consulta da fila de espera - HorarioTurma: " +
                        codigoHorarioTurma + ", Dia: " + dia + " - " + e.getMessage(), e), AulaServiceImpl.class);
                throw new ServiceException("Erro ao consultar fila de espera: " + e.getMessage());
            } catch (Exception e) {
                Uteis.logar(new ServiceException("Erro geral na consulta da fila de espera - HorarioTurma: " +
                        codigoHorarioTurma + ", Dia: " + dia + " - " + e.getMessage(), e), AulaServiceImpl.class);
                throw new ServiceException("Erro inesperado ao consultar fila de espera: " + e.getMessage());
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

        return filaEspera;
    }

    public List<FilaDeEsperaDTO> consultarFilaEsperaTurmaCrm(Integer codigoHorarioTurma) throws ServiceException {
        List<FilaDeEsperaDTO> filaEspera = new ArrayList<>();
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            String sql = "SELECT f.codigo, f.horarioturma, f.dataentrada, f.cliente, f.passivo, c.matricula, s.situacao, s.situacaoContrato, coalesce(p.nome, ps.nome) AS nomeAluno, ht.horainicial, ht.horafinal, f.ordem " +
                    "FROM filaesperaturmacrm f " +
                    "LEFT JOIN cliente c ON f.cliente = c.codigo " +
                    "LEFT JOIN passivo ps ON f.passivo = ps.codigo " +
                    "LEFT JOIN situacaoclientesinteticodw s ON f.cliente = s.codigocliente " +
                    "LEFT JOIN pessoa p ON c.pessoa = p.codigo " +
                    "JOIN horarioturma ht ON ht.codigo = f.horarioturma " +
                    "WHERE f.horarioturma = " + codigoHorarioTurma +
                    " ORDER BY f.ordem ASC, f.dataentrada ASC";

            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, conZW);

                while (rs.next()) {
                    FilaDeEsperaDTO filaDeEspera = new FilaDeEsperaDTO();
                    filaDeEspera.setCodigoHorarioTurma(rs.getInt("horarioturma"));
                    filaDeEspera.setDia(rs.getString("dataentrada"));
                    filaDeEspera.setCodigoAluno(rs.getInt("cliente"));
                    filaDeEspera.setPassivo(rs.getInt("passivo"));
                    filaDeEspera.setMatricula(rs.getInt("matricula"));
                    filaDeEspera.setSituacaoAluno(rs.getString("situacao"));
                    filaDeEspera.setSituacaoContrato(rs.getString("situacaoContrato"));
                    filaDeEspera.setNomeAluno(rs.getString("nomeAluno").concat(!UteisValidacao.emptyNumber(filaDeEspera.getPassivo()) ? " (RECEPTIVO)" : ""));
                    filaDeEspera.setCodigoFila(rs.getInt("codigo"));
                    filaDeEspera.setOrdem(rs.getInt("ordem"));
                    filaDeEspera.setVinculoComAula(String.valueOf(AlunoVinculoAulaEnum.ESPERA));
                    filaEspera.add(filaDeEspera);
                }

            } catch (ServiceException e) {
                Uteis.logar(e, AulaServiceImpl.class);
                throw e;
            } catch (SQLException e) {
                throw new RuntimeException(e);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

        return filaEspera;
    }

    public List<FilaDeEsperaDTO> ordenarFilaEsperaTurmaCrm(Integer codigoHorarioTurma, Integer matricula, Integer passivo, String tipoOrdenacao, Integer novaOrdem) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        List<FilaDeEsperaDTO> alunosOrdenados = consultarFilaEsperaTurmaCrm(codigoHorarioTurma);

        FilaDeEsperaDTO alunoMovido = null;
        for (FilaDeEsperaDTO aluno : alunosOrdenados) {
            if (!UteisValidacao.emptyNumber(aluno.getMatricula()) ? aluno.getMatricula().equals(matricula) : aluno.getPassivo().equals(passivo)) {
                alunoMovido = aluno;
                break;
            }
        }

        if ("decremento".equals(tipoOrdenacao)) {
            tipoOrdenacao = "desc";
        } else if ("incremento".equals(tipoOrdenacao)) {
            tipoOrdenacao = "asc";
        }
        if (alunoMovido != null) {
            Connection con = null;
            try {
                con = conexaoZWService.conexaoZw(ctx);
                switch (tipoOrdenacao) {
                    case "asc":
                        moverParaCima(alunosOrdenados, alunoMovido, con);
                        break;
                    case "desc":
                        moverParaBaixo(alunosOrdenados, alunoMovido, con);
                        break;
                    case "input":
                        if (novaOrdem != null) {
                            moverParaOrdemEspecifica(alunosOrdenados, alunoMovido, novaOrdem, con);
                        }
                        break;
                    default:
                        throw new IllegalArgumentException("Tipo de ordenação desconhecido: " + tipoOrdenacao);
                }

                atualizarOrdemNoBancoTurmaCrm(alunosOrdenados, ctx);
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException("Erro ao ordenar fila de espera", e);
            } finally {
                if (con != null) {
                    try {
                        con.close();
                    } catch (SQLException e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        return alunosOrdenados;
    }

    public List<FilaDeEsperaDTO> ordenarFilaEspera(Integer codigoHorarioTurma, String dia, Integer matricula, String tipoOrdenacao, Integer novaOrdem) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        List<FilaDeEsperaDTO> alunosOrdenados = consultarFilaEspera(codigoHorarioTurma, dia);

        FilaDeEsperaDTO alunoMovido = null;
        for (FilaDeEsperaDTO aluno : alunosOrdenados) {
            if (aluno.getMatricula().equals(matricula)) {
                alunoMovido = aluno;
                break;
            }
        }

        if ("decremento".equals(tipoOrdenacao)) {
            tipoOrdenacao = "desc";
        } else if ("incremento".equals(tipoOrdenacao)) {
            tipoOrdenacao = "asc";
        }
        if (alunoMovido != null) {
            Connection con = null;
            try {
                con = conexaoZWService.conexaoZw(ctx);
            switch (tipoOrdenacao) {
                case "asc":
                    moverParaCima(alunosOrdenados, alunoMovido, con);
                    break;
                case "desc":
                    moverParaBaixo(alunosOrdenados, alunoMovido, con);
                    break;
                case "input":
                    if (novaOrdem != null) {
                        moverParaOrdemEspecifica(alunosOrdenados, alunoMovido, novaOrdem, con);
                    }
                    break;
                default:
                    throw new IllegalArgumentException("Tipo de ordenação desconhecido: " + tipoOrdenacao);
            }

            atualizarOrdemNoBanco(alunosOrdenados, ctx);
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException("Erro ao ordenar fila de espera", e);
            } finally {
                if (con != null) {
                    try {
                        con.close();
                    } catch (SQLException e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        return alunosOrdenados;
    }

    public void moverParaCima(List<FilaDeEsperaDTO> alunosOrdenados, FilaDeEsperaDTO alunoMovido, Connection con) throws Exception {
        int index = alunosOrdenados.indexOf(alunoMovido);
        if (index >= 0 && index < alunosOrdenados.size() - 1) {
            FilaDeEsperaDTO alunoAbaixo = alunosOrdenados.get(index + 1);
            int ordemTemp = alunoAbaixo.getOrdem();
            alunoAbaixo.setOrdem(alunoMovido.getOrdem());
            alunoMovido.setOrdem(ordemTemp);

            alunosOrdenados.set(index + 1, alunoMovido);
            alunosOrdenados.set(index, alunoAbaixo);

            Log log = new Log();
            log.setNomeEntidade("ALUNO_AULA_COLETIVA");
            log.setNomeEntidadeDescricao("Movimentação na Fila de Espera");
            log.setChavePrimaria(alunoMovido.getCodigoFila().toString());
            log.setNomeCampo("Alteração da ordem");
            log.setValorCampoAlterado("Nova Ordem: " + alunoMovido.getOrdem());
            log.setOperacao("ALTERAÇÃO");
            log.setCliente(0);
            log.setPessoa(0);
            gravarLog(con, log);

        }
    }


    public void moverParaBaixo(List<FilaDeEsperaDTO> alunosOrdenados, FilaDeEsperaDTO alunoMovido, Connection con) throws Exception {
        int index = alunosOrdenados.indexOf(alunoMovido);
        if (index >= 0 && index <= alunosOrdenados.size() - 1) {
            FilaDeEsperaDTO alunoAcima = alunosOrdenados.get(index - 1);

            alunoMovido.setOrdem(alunoMovido.getOrdem() - 1);
            alunoAcima.setOrdem(alunoAcima.getOrdem() + 1);

            alunosOrdenados.set(index - 1, alunoMovido);
            alunosOrdenados.set(index, alunoAcima);

            Log log = new Log();
            log.setNomeEntidade("ALUNO_AULA_COLETIVA");
            log.setNomeEntidadeDescricao("Movimentação na Fila de Espera");
            log.setChavePrimaria(alunoMovido.getCodigoFila().toString());
            log.setNomeCampo("Alteração de Ordem");
            log.setValorCampoAlterado("[Aluno Movido: " + alunoMovido.getNomeAluno() + "]<br/>[Nova Ordem: " + alunoMovido.getOrdem() + "]");
            log.setOperacao("ALTERAÇÃO");
            log.setCliente(0);
            log.setPessoa(0);
            gravarLog(con, log);


        }
        for (int i = 0; i < alunosOrdenados.size(); i++) {
            alunosOrdenados.get(i).setOrdem(i + 1);
        }
   }


    public void moverParaOrdemEspecifica(List<FilaDeEsperaDTO> alunosOrdenados, FilaDeEsperaDTO alunoMovido, int novaOrdem, Connection con) throws Exception {
        int indexAtual = alunosOrdenados.indexOf(alunoMovido);
        if (indexAtual != -1 && novaOrdem > 0 && novaOrdem <= alunosOrdenados.size()) {
            alunosOrdenados.remove(indexAtual);
            alunosOrdenados.add(novaOrdem - 1, alunoMovido);

            Log log = new Log();
            log.setNomeEntidade("ALUNO_AULA_COLETIVA");
            log.setNomeEntidadeDescricao("Movimentação na Fila de Espera");
            log.setChavePrimaria(alunoMovido.getCodigoFila().toString());
            log.setNomeCampo("Alteração de Ordem");
            log.setValorCampoAlterado("[Aluno Movido: " + alunoMovido.getNomeAluno() + "]<br/>[Nova Ordem: " + alunoMovido.getOrdem() + "]");
            log.setOperacao("ALTERAÇÃO");
            log.setCliente(0);
            log.setPessoa(0);
            gravarLog(con, log);

            for (int i = 0; i < alunosOrdenados.size(); i++) {
                alunosOrdenados.get(i).setOrdem(i + 1);
            }
        }
    }

    public void atualizarOrdemNoBanco(List<FilaDeEsperaDTO> alunosOrdenados, String ctx) throws ServiceException {
        String sqlUpdate = "UPDATE filaesperaturma SET ordem = ? WHERE codigo = ?";
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            for (FilaDeEsperaDTO aluno : alunosOrdenados) {
                try (PreparedStatement ps = conZW.prepareStatement(sqlUpdate)) {
                    ps.setInt(1, aluno.getOrdem());
                    ps.setInt(2, aluno.getCodigoFila());
                    ps.executeUpdate();
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void atualizarOrdemNoBancoTurmaCrm(List<FilaDeEsperaDTO> alunosOrdenados, String ctx) throws ServiceException {
        String sqlUpdate = "UPDATE filaesperaturmacrm SET ordem = ? WHERE codigo = ?";
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            for (FilaDeEsperaDTO aluno : alunosOrdenados) {
                try (PreparedStatement ps = conZW.prepareStatement(sqlUpdate)) {
                    ps.setInt(1, aluno.getOrdem());
                    ps.setInt(2, aluno.getCodigoFila());
                    ps.executeUpdate();
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void gravarLog(Connection con, Log log) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada,\n");
        sql.append(" nomecampo, valorcampoanterior, valorcampoalterado, \n");
        sql.append(" dataalteracao, responsavelalteracao, operacao, pessoa, cliente, nomecampodescricao, tags)  \n");
        sql.append(" VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            int i = 0;
            stm.setString(++i, log.getNomeEntidade());
            stm.setString(++i, log.getNomeEntidadeDescricao());
            stm.setString(++i, log.getChavePrimaria());
            stm.setString(++i, "");
            stm.setString(++i, log.getNomeCampo());
            stm.setString(++i, log.getValorCampoAnterior());
            stm.setString(++i, log.getValorCampoAlterado());
            stm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            stm.setString(++i, sessaoService.getUsuarioAtual().getUsername());
            stm.setString(++i, log.getOperacao());
            stm.setInt(++i, log.getPessoa());
            stm.setInt(++i, log.getCliente());
            stm.setNull(++i, Types.NULL);
            stm.setNull(++i, Types.NULL);
            stm.execute();
        }
    }

    public void inserirEdicaoAulaTemporaria(String ctx, String nsu, Integer usuario,
                                            Integer horarioTurma,
                                            EdicaoAulaTemporaria edicaoAula) throws ServiceException {
        String sql = "INSERT INTO public.edicaoaulatemporaria (nsu, professor, ambiente, nivel, horarioinicial, " +
                "horariofinal, diaaula, capacidade, idademinimaanos, idademinimameses, idademaximaanos, " +
                "idademaximameses, tolerancia, tipotolerancia, mensagem, aulagympass, " +
                "aulatotalpass, validarmodalidadecontratocheckin, controlarcheckin, tipoalteracao, limite, usuarioalterou, horarioturma, " +
                " diasemana,nome ) " +
                "VALUES (? , ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?)";
        try (Connection conZW = conexaoZWService.conexaoZw(ctx);
            PreparedStatement pstmt = conZW.prepareStatement(sql)) {
            pstmt.setString(1, nsu);
            pstmt.setInt(2, edicaoAula.getProfessor());
            pstmt.setInt(3, edicaoAula.getAmbiente());
            pstmt.setString(4, edicaoAula.getNivel());
            pstmt.setString(5, edicaoAula.getHorarioInicial());
            pstmt.setString(6, edicaoAula.getHorarioFinal());
            if (edicaoAula.getDiaAula() == null) {
                pstmt.setNull(7, Types.NULL);
            } else {
                pstmt.setTimestamp(7, new Timestamp(edicaoAula.getDiaAula().getTime()));
            }
            pstmt.setInt(8, edicaoAula.getCapacidade());
            pstmt.setInt(9, edicaoAula.getIdadeMinimaAnos());
            pstmt.setInt(10, edicaoAula.getIdadeMinimaMeses());
            pstmt.setInt(11, edicaoAula.getIdadeMaximaAnos());
            pstmt.setInt(12, edicaoAula.getIdadeMaximaMeses());
            pstmt.setInt(13, edicaoAula.getToleranciaCheckinMinutos());
            pstmt.setString(14, edicaoAula.getToleranciaCheckinPeriodo());
            pstmt.setString(15, edicaoAula.getMensagem());
            pstmt.setBoolean(16, edicaoAula.isAulaGymPass());
            pstmt.setBoolean(17, edicaoAula.isAulaTotalPass());
            pstmt.setBoolean(18, edicaoAula.isValidarModalidadeContratoCheckin());
            pstmt.setBoolean(19, edicaoAula.isControlarCheckin());
            pstmt.setString(20, edicaoAula.getTipoAlteracao());
            if (edicaoAula.getLimite() == null) {
                pstmt.setNull(21, Types.NULL);
            } else {
                pstmt.setTimestamp(21, new Timestamp(edicaoAula.getLimite().getTime()));
            }
            pstmt.setInt(22, usuario);
            pstmt.setInt(23, horarioTurma);
            pstmt.setString(24, edicaoAula.getDiaSemana());
            String nomeAula = edicaoAula.getNome();
            if (nomeAula == null || nomeAula.trim().isEmpty()) {
                nomeAula = buscarNomeDaAula(ctx, edicaoAula.getHorarioTurma());
            }
            pstmt.setString(25, nomeAula);
            pstmt.executeUpdate();

            gerarLogEdicaoTemporaria(conZW,
                    horarioTurma,
                    edicaoAula);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private String buscarNomeDaAula(String ctx, Integer horarioTurma) {
        String sql = "SELECT identificador FROM turma WHERE codigo = (SELECT turma FROM horarioturma WHERE codigo = ?)";
        try (Connection conZW = conexaoZWService.conexaoZw(ctx);
             PreparedStatement pstmt = conZW.prepareStatement(sql)) {
            pstmt.setInt(1, horarioTurma);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("identificador");
                }
            }
        } catch (Exception ex) {
            Uteis.logar(ex, AulaServiceImpl.class);
        }
        return "Aula sem nome";
    }

    public void gerarLogEdicaoTemporaria(Connection conZW,
                               Integer horarioTurma,
                               EdicaoAulaTemporaria edicao) throws Exception {
        String sql = "select turma from horarioturma where codigo = " + horarioTurma;
        try(ResultSet rsTurma = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
            Integer turma =  rsTurma.next() ? rsTurma.getInt("turma") : null;
            Log log = new Log();
            log.setNomeEntidade("AULA_COLETIVA");
            log.setNomeEntidadeDescricao("Edição de aula pela agenda");
            log.setChavePrimaria(turma.toString());
            log.setNomeCampo("Edição de aula pela agenda");
            log.setValorCampoAlterado(edicao.toString());
            log.setOperacao("ALTERAÇÃO");
            log.setCliente(0);
            log.setPessoa(0);
            gravarLog(conZW, log);
            log.setNomeEntidade("ALUNO_AULA_COLETIVA");
            log.setChavePrimaria(horarioTurma.toString().concat("_").concat(Uteis.getDataAplicandoFormatacao(edicao.getDiaAula(), "dd/MM/yyyy")));
            gravarLog(conZW, log);
        }
    }

    public void gerarLogEdicaoTemporariaV2(Connection conZW,
                                         Integer horarioTurma,
                                         EdicaoAulaTemporaria edicao,EdicaoAulaTemporaria edicaoAntes) throws Exception {
        String sql = "select turma from horarioturma where codigo = " + horarioTurma;
        try(ResultSet rsTurma = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
            Integer turma =  rsTurma.next() ? rsTurma.getInt("turma") : null;
            Log log = new Log();
            log.setNomeEntidade("AULA_COLETIVA");
            log.setNomeEntidadeDescricao("Edição de aula pela agenda");
            log.setChavePrimaria(turma.toString());
            log.setNomeCampo("Edição de aula pela agenda");
            log.setValorCampoAlterado(edicao.getDescricaoParaLog(edicaoAntes));
            log.setValorCampoAnterior(edicaoAntes.getDescricaoParaLog(edicao));
            log.setOperacao("ALTERAÇÃO");
            log.setCliente(0);
            log.setPessoa(0);
            gravarLog(conZW, log);
            log.setNomeEntidade("ALUNO_AULA_COLETIVA");
            log.setChavePrimaria(horarioTurma.toString().concat("_").concat(Uteis.getDataAplicandoFormatacao(edicao.getDiaAula(), "dd/MM/yyyy")));
            gravarLog(conZW, log);
        }
    }

    public AulaResponseDTO edicaoAulaTemporaria(Integer idHorarioTurma, String diaAula) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            EdicaoAulaTemporaria edicaoAulaTemporaria = consultarEdicoesAulaTemporaria(ctx, idHorarioTurma, Uteis.getDate(diaAula, "yyyyMMdd"));
            return edicaoAulaTemporaria == null ? null : edicaoAulaTemporaria.toAulaResponseDTO();
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }
    @Override
    public EdicaoAulaTemporaria replicarAulaTemporariamente(AulaDTO aulaDTO, Integer id, String dataAula) throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            String nsu = id + "_" + dataAula;
            Date essaAula = Uteis.getDate(dataAula, "yyyyMMdd");
            TipoAlteracaoAulaEnum tipoEscolhaEdicao = TipoAlteracaoAulaEnum.valueOf(aulaDTO.getTipoEscolhaEdicao());
            Date limite = null;
            switch (tipoEscolhaEdicao) {
                case DETERMINADA_DATA:
                    limite = Uteis.getDate(aulaDTO.getDiaLimiteTurmaEdicao(), "yyyy/MM/dd");
                    break;
                case ESSA_PROXIMA:
                    limite = Uteis.somarDias(essaAula, 8);
                    break;
            }
            String sql = "update edicaoaulatemporaria set limite = ?, tipoalteracao = ?  where nsu = ?";
            try (Connection conZW = conexaoZWService.conexaoZw(ctx);
                 PreparedStatement pstmt = conZW.prepareStatement(sql)) {
                if (limite == null) {
                    pstmt.setNull(1, Types.NULL);
                } else {
                    pstmt.setTimestamp(1, new Timestamp(limite.getTime()));
                }
                pstmt.setString(2, tipoEscolhaEdicao.name());
                pstmt.setString(3, nsu);
                pstmt.execute();

                gerarLogReplicardicaoTemporaria(conZW, id, essaAula, tipoEscolhaEdicao, tipoEscolhaEdicao.equals(TipoAlteracaoAulaEnum.DETERMINADA_DATA) ? limite : null);
            }
            return new EdicaoAulaTemporaria(aulaDTO);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AulaExcecoes.ERRO_INCLUIR, e);
        }
    }

    public void gerarLogReplicardicaoTemporaria(Connection conZW,
                                         Integer horarioTurma,
                                         Date dia,
                                         TipoAlteracaoAulaEnum tipo, Date limite) throws Exception {
        String sql = "select turma from horarioturma where codigo = " + horarioTurma;
        try(ResultSet rsTurma = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
            Integer turma =  rsTurma.next() ? rsTurma.getInt("turma") : null;
            Log log = new Log();
            log.setNomeEntidade("AULA_COLETIVA");
            log.setNomeEntidadeDescricao("Replicar edição");
            log.setChavePrimaria(turma.toString());
            log.setNomeCampo("Replicar edição");
            log.setValorCampoAlterado(tipo.name() + (limite == null ? "" : (" - Limite: " + Uteis.getDataAplicandoFormatacao(limite, "dd/MM/yyyy"))));
            log.setOperacao("ALTERAÇÃO");
            log.setCliente(0);
            log.setPessoa(0);
            gravarLog(conZW, log);
            log.setNomeEntidade("ALUNO_AULA_COLETIVA");
            log.setChavePrimaria(horarioTurma.toString().concat("_").concat(Uteis.getDataAplicandoFormatacao(dia, "dd/MM/yyyy")));
            gravarLog(conZW, log);
        }
    }

    public EdicaoAulaTemporaria consultarEdicoesAulaTemporaria(String ctx, Integer idHorarioTurma, Date diaAula) throws ServiceException {
        String sql = "SELECT codigo, professor, ambiente, nivel, horarioinicial, horariofinal, diaaula, " +
                "capacidade, idademinimaanos, idademinimameses, idademaximaanos, idademaximameses, " +
                "tolerancia, tipotolerancia, mensagem, aulagympass, aulatotalpass, " +
                "validarmodalidadecontratocheckin, controlarcheckin, tipoalteracao, limite, horarioturma, nome " +
                "FROM edicaoaulatemporaria where horarioturma = " + idHorarioTurma +
                " and (tipoalteracao = '" + TipoAlteracaoAulaEnum.TODAS +
                "' or (diaaula <= '" + Uteis.getDataFormatoBD(diaAula) + "' and limite >= '" + Uteis.getDataFormatoBD(diaAula) + "'))";


        try (Connection conZW = conexaoZWService.conexaoZw(ctx);
             PreparedStatement pstmt = conZW.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            if (rs.next()) {
                return montarDadosEdicaoTemporaria(rs);
            }
        } catch (Exception ex) {
            Uteis.logar(ex, AulaServiceImpl.class);
        }
        return null;
    }

    public List<EdicaoAulaTemporaria> consultarEdicoesAulaTemporariaPeriodo(String ctx, Date inicio, Date fim) throws ServiceException {
        String sql = "SELECT a.descricao as ambnome, p.nome as professorNome, ea.nome as nome, ea.codigo, ea.professor, ea.ambiente, ea.nivel, " +
                "ea.horarioinicial, ea.horariofinal, ea.diaaula, " +
                "ea.capacidade, ea.idademinimaanos, ea.idademinimameses, ea.idademaximaanos, ea.idademaximameses, " +
                "ea.tolerancia, ea.tipotolerancia, ea.mensagem, ea.aulagympass, ea.aulatotalpass, " +
                "ea.validarmodalidadecontratocheckin, ea.controlarcheckin, ea.tipoalteracao, ea.limite, ea.horarioturma " +
                " FROM edicaoaulatemporaria ea " +
                " left join colaborador c on c.codigo = ea.professor " +
                " left join pessoa p on p.codigo = c.pessoa " +
                " left join ambiente a on a.codigo = ea.ambiente " +
                " where (ea.tipoalteracao = '" + TipoAlteracaoAulaEnum.TODAS.name() +
                "' or ea.diaaula = '" + Uteis.getDataFormatoBD(inicio) + "' " +
                " or (ea.diaaula BETWEEN '" + Uteis.getDataFormatoBD(inicio) + "' and '" + Uteis.getDataFormatoBD(fim) + "')" +
                " or (ea.limite BETWEEN '" + Uteis.getDataFormatoBD(inicio) + "' and '" + Uteis.getDataFormatoBD(fim) + "')" +
                " or ('" + Uteis.getDataFormatoBD(inicio) + "' BETWEEN ea.diaaula and ea.limite))";
        List<EdicaoAulaTemporaria> edicoes = new ArrayList<>();
        try (Connection conZW = conexaoZWService.conexaoZw(ctx);
             PreparedStatement pstmt = conZW.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            while (rs.next()) {
                EdicaoAulaTemporaria edicao = montarDadosEdicaoTemporaria(rs);
                edicao.setProfessorNome(rs.getString("professorNome"));
                edicao.setAmbienteNome(rs.getString("ambnome"));
                edicao.setNome(rs.getString("nome"));
                edicoes.add(edicao);
            }
        } catch (Exception ex) {
            Uteis.logar(ex, AulaServiceImpl.class);
        }
        return edicoes;
    }

    private EdicaoAulaTemporaria montarDadosEdicaoTemporaria(ResultSet rs) throws SQLException {
        EdicaoAulaTemporaria edicao = new EdicaoAulaTemporaria();
        edicao.setCodigo(rs.getInt("codigo"));
        edicao.setProfessor(rs.getInt("professor"));
        edicao.setAmbiente(rs.getInt("ambiente"));
        edicao.setNivel(rs.getString("nivel"));
        edicao.setHorarioInicial(rs.getString("horarioinicial"));
        edicao.setHorarioFinal(rs.getString("horariofinal"));
        edicao.setDiaAula(rs.getTimestamp("diaaula"));
        edicao.setCapacidade(rs.getInt("capacidade"));
        edicao.setIdadeMinimaAnos(rs.getInt("idademinimaanos"));
        edicao.setIdadeMinimaMeses(rs.getInt("idademinimameses"));
        edicao.setIdadeMaximaAnos(rs.getInt("idademaximaanos"));
        edicao.setIdadeMaximaMeses(rs.getInt("idademaximameses"));
        edicao.setToleranciaCheckinMinutos(rs.getInt("tolerancia"));
        edicao.setToleranciaCheckinPeriodo(rs.getString("tipotolerancia"));
        edicao.setMensagem(rs.getString("mensagem"));
        edicao.setAulaGymPass(rs.getBoolean("aulagympass"));
        edicao.setAulaTotalPass(rs.getBoolean("aulatotalpass"));
        edicao.setValidarModalidadeContratoCheckin(rs.getBoolean("validarmodalidadecontratocheckin"));
        edicao.setControlarCheckin(rs.getBoolean("controlarcheckin"));
        edicao.setTipoAlteracao(rs.getString("tipoalteracao"));
        edicao.setLimite(rs.getTimestamp("limite"));
        edicao.setHorarioTurma(rs.getInt("horarioturma"));
        String nome = rs.getString("nome");
        if (nome == null || nome.trim().isEmpty()) {
            nome = buscarNomeDaAula(sessaoService.getUsuarioAtual().getChave(), rs.getInt("horarioturma"));
        }
        edicao.setNome(nome);
        return edicao;
    }

    public void editarEdicaoAulaTemporaria(EdicaoAulaTemporaria edicaoAula, EdicaoAulaTemporaria edicaoAulaAntes) throws ServiceException {
        String sql = "UPDATE public.edicaoaulatemporaria SET professor = ?, ambiente = ?, nivel = ?, " +
                "horarioinicial = ?, horariofinal = ?, diaaula = ?, capacidade = ?, idademinimaanos = ?, " +
                "idademinimameses = ?, idademaximaanos = ?, idademaximameses = ?, tolerancia = ?, " +
                "tipotolerancia = ?, mensagem = ?, aulagympass = ?, aulatotalpass = ?, " +
                "validarmodalidadecontratocheckin = ?, controlarcheckin = ?, tipoalteracao = ?, limite = ?, " +
                "usuarioalterou = ?, diasemana = ? , nome = ?" +
                "WHERE codigo = ?";
        String ctx = sessaoService.getUsuarioAtual().getChave();
        Integer usuario = sessaoService.getUsuarioAtual().getId();
        try (Connection conZW = conexaoZWService.conexaoZw(ctx);
             PreparedStatement pstmt = conZW.prepareStatement(sql)) {
            pstmt.setInt(1, edicaoAula.getProfessor());
            pstmt.setInt(2, edicaoAula.getAmbiente());
            pstmt.setString(3, edicaoAula.getNivel());
            pstmt.setString(4, edicaoAula.getHorarioInicial());
            pstmt.setString(5, edicaoAula.getHorarioFinal());
            pstmt.setTimestamp(6, new Timestamp(edicaoAula.getDiaAula().getTime()));
            pstmt.setInt(7, edicaoAula.getCapacidade());
            pstmt.setInt(8, edicaoAula.getIdadeMinimaAnos());
            pstmt.setInt(9, edicaoAula.getIdadeMinimaMeses());
            pstmt.setInt(10, edicaoAula.getIdadeMaximaAnos());
            pstmt.setInt(11, edicaoAula.getIdadeMaximaMeses());
            pstmt.setInt(12, edicaoAula.getToleranciaCheckinMinutos());
            pstmt.setString(13, edicaoAula.getToleranciaCheckinPeriodo());
            pstmt.setString(14, edicaoAula.getMensagem());
            pstmt.setBoolean(15, edicaoAula.isAulaGymPass());
            pstmt.setBoolean(16, edicaoAula.isAulaTotalPass());
            pstmt.setBoolean(17, edicaoAula.isValidarModalidadeContratoCheckin());
            pstmt.setBoolean(18, edicaoAula.isControlarCheckin());
            pstmt.setString(19, edicaoAula.getTipoAlteracao());
            pstmt.setTimestamp(20, edicaoAula.getLimite() != null ? new Timestamp(edicaoAula.getLimite().getTime()) : null);
            pstmt.setInt(21, usuario);
            pstmt.setString(22, edicaoAula.getDiaSemana());
            String nome = edicaoAula.getNome();
            if (nome == null || nome.trim().isEmpty()) {
                nome = buscarNomeDaAula(ctx, edicaoAula.getHorarioTurma());
            }
            pstmt.setString(23, nome);
            pstmt.setInt(24, edicaoAula.getCodigo());
            pstmt.execute();

            gerarLogEdicaoTemporariaV2(conZW,
                    edicaoAula.getHorarioTurma(),
                    edicaoAula, edicaoAulaAntes);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private Date aplicaTimeZone(Date original, String tzId) {
        try {
            TimeZone tz = TimeZone.getTimeZone(tzId);
            Calendar cal = Calendar.getInstance(tz);
            cal.setTimeInMillis(original.getTime());
            return cal.getTime();
        } catch (Exception e) {
            return original;
        }
    }


}

