/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.gympass.json;

import br.com.pacto.controller.json.agendamento.TurmaResponseDTO;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;

import java.text.ParseException;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 30/03/2020
 */
public class HorarioTurmaGymPassJSON extends SuperJSON {

    private Integer codigo;
    private Date dia;
    private String inicio;
    private String fim;
    private String ambiente;
    private String professor;
    private String professorSubstituto;
    private boolean ativo;
    private Integer qtdTotal;
    private Integer qtdReservado;
    private Integer idSlotGymPass;


    public HorarioTurmaGymPassJSON(){
    }

    public HorarioTurmaGymPassJSON(TurmaResponseDTO dto) throws ParseException {
        this.codigo = dto.getHorarioTurmaId();
        this.dia = Calendario.getDate("yyyyMMdd", dto.getDia());
        this.inicio = dto.getHorarioInicio();
        this.fim = dto.getHorarioFim();
        this.ambiente = dto.getAmbiente().getNome();

        if (dto.getProfessor() == null) {
            this.professor = "PROFESSOR";
        } else {
            this.professor = dto.getProfessor().getNome();
        }

        if (dto.getProfessorSubstituto() != null) {
            this.professorSubstituto = dto.getProfessorSubstituto().getNome();
        } else {
            this.professorSubstituto = null;
        }
        this.ativo = true;
        this.qtdTotal = dto.getCapacidade();
        this.qtdReservado = dto.getNumeroAlunos();
    }

    public String getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(String ambiente) {
        this.ambiente = ambiente;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public Integer getQtdTotal() {
        return qtdTotal;
    }

    public void setQtdTotal(Integer qtdTotal) {
        this.qtdTotal = qtdTotal;
    }

    public Integer getQtdReservado() {
        return qtdReservado;
    }

    public void setQtdReservado(Integer qtdReservado) {
        this.qtdReservado = qtdReservado;
    }

    public Integer getIdSlotGymPass() {
        return idSlotGymPass;
    }

    public void setIdSlotGymPass(Integer idSlotGymPass) {
        this.idSlotGymPass = idSlotGymPass;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getProfessorSubstituto() {
        return professorSubstituto;
    }

    public void setProfessorSubstituto(String professorSubstituto) {
        this.professorSubstituto = professorSubstituto;
    }

    public String getInicio() {
        return inicio;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public String getFim() {
        return fim;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public Date getInicioDate() {
        try {
            return Calendario.getDataComHora(Calendario.getDataComHoraZerada(this.getDia()), this.getInicio());
        } catch (Exception ex) {
            return null;
        }
    }

    public Date getFimDate() {
        try {
            if (this.getFim().equalsIgnoreCase("00:00")) {
                return Calendario.getDataComHora(Calendario.getDataComHoraZerada(Uteis.somarDias(this.getDia(), 1)), this.getFim());
            }
            return Calendario.getDataComHora(Calendario.getDataComHoraZerada(this.getDia()), this.getFim());
        } catch (Exception ex) {
            return null;
        }
    }

    public Integer getDuracaoMinutos() {
        try {
            Long diferenca = Calendario.diferencaEmMinutos(this.getInicioDate(), this.getFimDate());
            return diferenca.intValue();
        } catch (Exception ex) {
            return null;
        }
    }

    public String getIdReferencia() {
        try {
            return getCodigo() + "_" + Calendario.getData(getDia(), "yyyyMMdd");
        } catch (Exception ex) {
            return null;
        }
    }
}
