package br.com.pacto.service.impl.gestao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.bi.*;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.aluno.AlunoSimplesDTO;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.gestao.*;
import br.com.pacto.dao.intf.cliente.ClienteAcompanhamentoAvaliacaoDao;
import br.com.pacto.dao.intf.configuracoes.ConfiguracaoSistemaDao;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.ProfessorExcecoes;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.gestao.BITreinoService;
import br.com.pacto.service.intf.gestao.DashboardBIService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.enumeradores.DiasSemana;
import br.com.pacto.util.impl.UtilReflection;


import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



import javax.servlet.http.HttpServletRequest;
import java.sql.ResultSet;
import java.util.*;

@Service
public class BITreinoServiceImpl implements BITreinoService {

    private MicroCharts micro;
    @Autowired
    private ConfiguracaoSistemaService cs;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ConfiguracaoSistemaDao configuracaoSistemaDao;
    @Autowired
    private ProfessorSinteticoService professorSinteticoService;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;
    @Autowired
    private ClienteAcompanhamentoAvaliacaoDao clienteAcompanhamentoAvaliacaoDao;

    public DashboardBIService getService() {
        return UtilContext.getBean(DashboardBIService.class);
    }

    private ConfiguracaoSistemaDao getConfiguracaoSistemaDao() {
        return this.configuracaoSistemaDao;
    }

    public Integer getAnoAtual() {
        return Uteis.getAnoData(Calendario.hoje());
    }

    public Integer getDiaAtual() {
        return Uteis.getDiaMesData(Calendario.hoje());
    }

    public Integer getMesAtual() {
        return Uteis.getMesData(Calendario.hoje());
    }

    public Date getDataAtual() {
        Date atual = Calendario.hoje();
        try {
            UsuarioSimplesDTO usuarioAtual = sessaoService.getUsuarioAtual();
            Usuario usuario = usuarioService.obterPorId(usuarioAtual.getChave(), usuarioAtual.getId());
            String timeZoneDefault = usuario.getProfessor().getEmpresa().getTimeZoneDefault();
            atual = Calendario.hoje(timeZoneDefault);
        }catch (Exception e){
            Uteis.logar(e, BITreinoServiceImpl.class);
        }
        return atual;
    }

    @Override
    public BITreinoResponseDTO gerarBI(Integer idProfessor, Integer empresaId) {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            FiltrosDashboard a = new FiltrosDashboard();
            a.setEmpresa(empresaId);
            a.setDiasParaFrente(30);
            a.setDiasParaTras(30);
            if (!SuperControle.independente(ctx)) {
                ProfessorSintetico professor = UtilContext.getBean(ProfessorSinteticoService.class).consultarPorCodigoColaborador(ctx, idProfessor);
                if (professor != null) {
                    idProfessor = professor.getCodigo();
                }
            }

            DashboardBI dash = getService().processarGestao(ctx, idProfessor, getDataAtual(), a, !SuperControle.independente(ctx), SuperControle.independente(ctx), empresaId);
            if (dash == null) {
                dash = getService().obterUltimoBI(ctx, idProfessor, empresaId);
            }

            List<TipoEventoDisponibilidadeJSON> listaTipos = new ArrayList<TipoEventoDisponibilidadeJSON>();
            List<TipoEventoDisponibilidadeBI> listaTiposBi = getService().obterTipos(ctx, getAnoAtual(), getMesAtual(), idProfessor, empresaId);
            for (TipoEventoDisponibilidadeBI t : listaTiposBi) {
                if (t.getDisponibilidade() > 0L) {
                    listaTipos.add(new TipoEventoDisponibilidadeJSON(t));
                }
            }

            List<DiasSemanaDashboardBI> listaOrdenada = getService().obterDias(ctx, getAnoAtual(), getMesAtual(), idProfessor, empresaId);
            List<DiasSemanaDashboardBI> listReturn = new ArrayList<>();

            for (DiasSemana diaSemana : DiasSemana.values()) {
                DiasSemanaDashboardBI diasSemanaDashboardBI = new DiasSemanaDashboardBI();
                for (DiasSemanaDashboardBI diaDashboard : listaOrdenada) {
                    if (diaDashboard.getDiaSemana().equals(diaSemana)) {
                        diasSemanaDashboardBI.setNrDias(diasSemanaDashboardBI.getNrDias() + diaDashboard.getNrDias());
                        diasSemanaDashboardBI.setTotalManha(diasSemanaDashboardBI.getTotalManha() + diaDashboard.getTotalManha());
                        diasSemanaDashboardBI.setTotalTarde(diasSemanaDashboardBI.getTotalTarde() + diaDashboard.getTotalTarde());
                        diasSemanaDashboardBI.setTotalNoite(diasSemanaDashboardBI.getMediaNoite() + diaDashboard.getTotalNoite());
                        diasSemanaDashboardBI.setDiaSemana(diaSemana);
                        diasSemanaDashboardBI.setAno(diaDashboard.getAno());
                        diasSemanaDashboardBI.setMes(diaDashboard.getMes());
                        diasSemanaDashboardBI.setCodigoProfessor(diaDashboard.getCodigoProfessor());
                        diasSemanaDashboardBI.setEmpresa(diaDashboard.getEmpresa());
                    }
                }
                listReturn.add(diasSemanaDashboardBI);
            }

            micro = getService().montarMicroCharts(ctx, idProfessor, a);


            return new BITreinoResponseDTO(dash, listaTipos, micro, listReturn);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    ///treino-bi/procesar-dados
    public void processar(Integer idProfessor, Integer empresaId) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        Boolean independente = SuperControle.independente(ctx);
        FiltrosDashboard a = new FiltrosDashboard();
        a.setEmpresa(empresaId);
        a.setDiasParaFrente(30);
        a.setDiasParaTras(30);
        getService().processarGestao(ctx, idProfessor, getDataAtual(), a, !independente, independente, empresaId);
    }

    public DashboardBI obterDash(Integer idProfessor, Integer empresaId) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        return obterDash(idProfessor, empresaId, ctx);
    }

    public DashboardBI obterDash(String ctx) throws ServiceException {
        UsuarioSimplesDTO usuarioSimplesDTO = new UsuarioSimplesDTO();
        usuarioSimplesDTO.setChave(ctx);
        sessaoService.setUsuarioAtual(usuarioSimplesDTO);

        List<Empresa> empresas = empresaService.obterTodos(ctx);
        if(empresas == null || empresas.isEmpty()){
            throw new ServiceException("nao foi possivel encontrar pelo menos um registro de empresa na chave informada");
        }
        return obterDash(0, empresas.get(0).getCodigo(), ctx);
    }

    public DashboardBI obterDash(Integer idProfessor, Integer empresaId, String ctx) throws ServiceException {

        FiltrosDashboard a = new FiltrosDashboard();
        a.setEmpresa(empresaId);
        a.setDiasParaFrente(30);
        a.setDiasParaTras(30);
        Calendar dias = Calendar.getInstance();
        dias.add(Calendar.DATE, 1);
        try {
            DashboardBI dashboardBI = getService().obterBIGeral(ctx, idProfessor, empresaId, 1);
            if (dashboardBI != null){
                int diasEntreDatas = Uteis.getNumeroDiasEntreDatas(dashboardBI.getFimProcessamento() , dias.getTime());
                if (diasEntreDatas > 2){
                    return atualizarDash(idProfessor,empresaId);
                }
                dashboardBI.setTotalAlunosSemAcompanhamento(clienteSinteticoService.consultarQuantidadePorDesacompanhadosProfessor(ctx, empresaId, idProfessor, Calendario.hoje(), Calendario.hoje()));
                dashboardBI.setBiCarteira(new BITreinoCarteiraDTO(dashboardBI));

                processarAvaliacoesAcompanhamento(idProfessor, empresaId, ctx, dashboardBI);
                dashboardBI.getBiCarteira().setTotalAlunosEmAcompanhamento(clienteSinteticoService.consultarQuantidadeEmAcompanhamentoProfessor(ctx, empresaId, idProfessor, Calendario.hoje(), Calendario.hoje()));
                dashboardBI.setBiTreinamento(treinamento(idProfessor,empresaId,dashboardBI));
                dashboardBI.setBiTreinoAvaliacaoTreino(new BITreinoAvaliacaoTreinoDTO(dashboardBI));
            }
            if (dashboardBI == null){
                return atualizarDash(idProfessor,empresaId);
            }
            return dashboardBI;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public DashboardBI obterBiTreinamento(Integer idProfessor, Integer empresaId, String ctx) throws ServiceException {
        FiltrosDashboard a = new FiltrosDashboard();
        a.setEmpresa(empresaId);
        a.setDiasParaFrente(30);
        a.setDiasParaTras(30);
        Calendar dias = Calendar.getInstance();
        dias.add(Calendar.DATE, 1);
        try {
            DashboardBI dashboardBI = getService().obterBIGeral(ctx, idProfessor, empresaId);
            if (dashboardBI != null){
                int diasEntreDatas = Uteis.getNumeroDiasEntreDatas(dashboardBI.getFimProcessamento() , dias.getTime());
                if (diasEntreDatas > 2){
                    return atualizarDash(idProfessor,empresaId);
                }
                dashboardBI.setBiTreinamento(treinamento(idProfessor,empresaId,dashboardBI));
            }
            if (dashboardBI == null){
                return atualizarDash(idProfessor,empresaId);
            }
            return dashboardBI;
        } catch (Exception e) {
            throw new ServiceException(e);
        }

    }

    public DashboardBI atualizarDash(Integer idProfessor, Integer empresaId) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        boolean independente = SuperControle.independente(ctx);
        FiltrosDashboard a = new FiltrosDashboard();
        a.setEmpresa(empresaId);
        a.setDiasParaFrente(30);
        a.setDiasParaTras(30);
        Calendar dias = Calendar.getInstance();
        dias.add(Calendar.DATE, 1);
        try {
            if(!"true".equalsIgnoreCase(Aplicacao.getProp(ctx, Aplicacao.sincronizandoGympass+empresaId))) {
                if (UteisValidacao.emptyNumber(idProfessor) && !independente) {
                    getService().atualizarHorariosGympass(ctx, empresaId);
                }
            }
            DashboardBI dashboardBI = getService().processarGestao(ctx, idProfessor, getDataAtual(), a, !independente, independente, empresaId);
            dashboardBI.setTotalAlunosSemAcompanhamento(clienteSinteticoService.consultarQuantidadePorDesacompanhadosProfessor(ctx, empresaId, idProfessor,Calendario.hoje(),Calendario.hoje()));
            dashboardBI.setBiCarteira(new BITreinoCarteiraDTO(dashboardBI));
            dashboardBI.getBiCarteira().setTotalAlunosEmAcompanhamento(clienteSinteticoService.consultarQuantidadeEmAcompanhamentoProfessor(ctx, empresaId, idProfessor, Calendario.hoje(), Calendario.hoje()));
            processarAvaliacoesAcompanhamento(idProfessor, empresaId, ctx, dashboardBI);
            dashboardBI.setBiTreinamento(treinamento(idProfessor,empresaId,dashboardBI));
            dashboardBI.setBiTreinoAvaliacaoTreino(new BITreinoAvaliacaoTreinoDTO(dashboardBI));
            return dashboardBI;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public BITreinoCarteiraDTO biCarteira(Integer idProfessor, Integer empresaId) throws ServiceException {
        if(idProfessor == null){
            idProfessor = 0;
        }
        DashboardBI dash = obterDash(idProfessor, empresaId);
        return new BITreinoCarteiraDTO(dash);
    }

    private void processarAvaliacoesAcompanhamento(Integer idProfessor, Integer empresaId, String ctx, DashboardBI dashboardBI) throws Exception {
        List<AvaliacaoAgrupadaDTO> contagemAvaliacoes = clienteAcompanhamentoAvaliacaoDao.contarAvaliacoesAgrupadasPorNota(ctx, idProfessor, empresaId);
        dashboardBI.setNr1EstrelaAcompanhamento(0);
        dashboardBI.setNr2EstrelasAcompanhamento(0);
        dashboardBI.setNr3EstrelasAcompanhamento(0);
        dashboardBI.setNr4EstrelasAcompanhamento(0);
        dashboardBI.setNr5EstrelasAcompanhamento(0);
        int totalAvaliacoes = 0;
        for (AvaliacaoAgrupadaDTO dto : contagemAvaliacoes) {
            if (dto.getNota() != null && dto.getTotal() != null) {
                totalAvaliacoes += dto.getTotal().intValue();
                switch (dto.getNota()) {
                    case 1:
                        dashboardBI.setNr1EstrelaAcompanhamento(dto.getTotal().intValue());
                        break;
                    case 2:
                        dashboardBI.setNr2EstrelasAcompanhamento(dto.getTotal().intValue());
                        break;
                    case 3:
                        dashboardBI.setNr3EstrelasAcompanhamento(dto.getTotal().intValue());
                        break;
                    case 4:
                        dashboardBI.setNr4EstrelasAcompanhamento(dto.getTotal().intValue());
                        break;
                    case 5:
                        dashboardBI.setNr5EstrelasAcompanhamento(dto.getTotal().intValue());
                        break;
                }
            }
        }
        dashboardBI.setNrAvaliacoesAcompanhamento(totalAvaliacoes);
    }

    public BITreinoAgendaDTO agenda(Integer idProfessor, Integer empresaId) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        DashboardBI dash = obterDash(idProfessor, empresaId);
        List<TipoEventoDisponibilidadeBI> listaTiposBi = null;
        try {
            listaTiposBi = getService().obterTipos(ctx, getAnoAtual(), getMesAtual(),
                    idProfessor,
                    empresaId);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
        return new BITreinoAgendaDTO(dash, listaTiposBi);
    }

    public BITreinoTreinamentoDTO treinamento(Integer idProfessor, Integer empresaId) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        if(idProfessor == null) {
            idProfessor = 0;
        }
        DashboardBI dash = obterDash(idProfessor, empresaId);

        List<DiasSemanaDashboardBI> listadias = null;
        List<AcessosExecucoesBI> listaAcessosBi = null;
        ItemMediaBI menor = null;
        ItemMediaBI maior = null;
        try {
            Date diaLimite = Uteis.somarDias(Calendario.hoje(), -dash.getDiasConfiguracao());
            listadias = getService().obterDias(ctx, getAnoAtual(), getMesAtual(), idProfessor, empresaId);
            listaAcessosBi = getService().obterAcessos(ctx, diaLimite, idProfessor, empresaId);
            menor = getService().obterItemMediaTreino(ctx, dash.getCodigoProfessor(), false);
            maior = getService().obterItemMediaTreino(ctx, dash.getCodigoProfessor(), true);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
        return new BITreinoTreinamentoDTO(dash, listadias, listaAcessosBi, maior, menor);
    }

    private BITreinoTreinamentoDTO treinamento(Integer idProfessor, Integer empresaId, DashboardBI dash) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        if(idProfessor == null) {
            idProfessor = 0;
        }
        List<DiasSemanaDashboardBI> listadias = null;
        List<AcessosExecucoesBI> listaAcessosBi = null;
        ItemMediaBI menor = null;
        ItemMediaBI maior = null;
        List<TreinoRealizadoAppDTO> treinoRealizadoAppDTOS = new ArrayList<>();
        try {
            Date diaLimite = Uteis.somarDias(Calendario.hoje(), -dash.getDiasConfiguracao());
            listadias = getService().obterDias(ctx, getAnoAtual(), getMesAtual(), idProfessor, empresaId);
            listaAcessosBi = getService().obterAcessos(ctx, diaLimite, idProfessor, empresaId);
            menor = getService().obterItemMediaTreino(ctx, dash.getCodigoProfessor(), false);
            maior = getService().obterItemMediaTreino(ctx, dash.getCodigoProfessor(), true);
            treinoRealizadoAppDTOS = getService().obterTreinosRealizadosPeloAppAgrupandoPorDiaDaSemana(ctx, diaLimite, idProfessor, empresaId);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
        return new BITreinoTreinamentoDTO(dash, listadias, listaAcessosBi, maior, menor, treinoRealizadoAppDTOS);
    }


    @Override
    public List<AlunoSimplesDTO> listAlunosExecucaoTreinoUltimosDias(Integer idProfessor, Integer empresaId, Integer dia, String periodo, JSONObject filters, PaginadorDTO paginadorDTO) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        ConfiguracaoSistema configuracaoSistema = cs.consultarPorTipo(ctx, ConfiguracoesEnum.PERIODO_USADO_BI);
        int diasPesquisa = configuracaoSistema.getValorAsInteger();
        int diasParaTras = (diasPesquisa > 0 ? diasPesquisa : 30);
        Date diaLimite = Uteis.somarDias(Calendario.hoje(), -diasParaTras);
        return getService().consultarAlunosTreinoPrograma(ctx,diaLimite, empresaId, dia, periodo, filters, paginadorDTO);
    }

    public List<Map<String, Object>> listaAcessos(Integer idProfessor, Integer empresaId, Long dia, String tipo) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            DashboardBI dash = obterDash(idProfessor, empresaId);
            IndicadorDashboardEnum indicadorSelecionado = null;
            Date diaSelec = Uteis.dataHoraZeradaUTC(dia);
            List lista = null;
            switch (tipo) {
                case "EXECUCOES_TREINO":
                    indicadorSelecionado = IndicadorDashboardEnum.EXECUCOES_TREINO;
                    lista = getService().obterListaExecucoes(ctx, false, diaSelec, dash);
                    break;
                case "SMARTPHONE":
                    indicadorSelecionado = IndicadorDashboardEnum.SMARTPHONE;
                    lista = getService().obterListaExecucoes(ctx, true, diaSelec, dash);
                    break;
                case "ACESSO":
                    indicadorSelecionado = IndicadorDashboardEnum.ACESSOS;
                    lista = getService().obterListaAcesso(ctx, diaSelec, dash, Boolean.FALSE);
                    break;
                case "ALUNOS_TREINO":
                    indicadorSelecionado = IndicadorDashboardEnum.ACESSOS_TREINO;
                    lista = getService().obterListaAcesso(ctx, diaSelec, dash, Boolean.TRUE);
                    break;
            }
            if (indicadorSelecionado != null && lista != null){
                return montarLista(indicadorSelecionado, lista);
            }
            return null;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    private List<Map<String, Object>> montarLista(IndicadorDashboardEnum indicadorSelecionado, List lista) {
        String[] columnKeys = indicadorSelecionado.getColunas().split("\\|");
        List<Map<String, Object>> listaBI = new ArrayList<>();
        for (Object o : lista) {
            Map<String, Object> mapColuna = new HashMap<>();
            for (String column : columnKeys) {
                String property = getProperty(column);
                Object valor = UtilReflection.getValor(o, property);
                if (valor == null){
                    valor = "-";
                }
                mapColuna.put(property, valor);
            }
            listaBI.add(mapColuna);
        }
        return listaBI;
    }

    private List<LinkedHashMap<String,Object>> montarListaLinkedMap(IndicadorDashboardEnum indicadorSelecionado, List lista) throws JSONException {
        String[] columnKeys = indicadorSelecionado.getColunas().split("\\|");
        List<LinkedHashMap<String,Object>> listaBILinked = new ArrayList<>();
        for (Object o : lista) {
            LinkedHashMap<String,Object> linkedMap = new LinkedHashMap<>();
            for (String column : columnKeys) {
                String property = getProperty(column);
                Object valor = UtilReflection.getValor(o, property);
                if (valor == null){
                    valor = "-";
                }
                linkedMap.put(property, valor);
            }
            listaBILinked.add(linkedMap);
        }
        return listaBILinked;
    }

    public List<Map<String, Object>> listaBI(Integer idProfessor, Integer empresaId, IndicadorDashboardEnum indicadorSelecionado, PaginadorDTO paginadorDTO, String filter) throws ServiceException {
        List lista = pesquisaBI(idProfessor, empresaId, indicadorSelecionado, paginadorDTO, filter, null);
        return montarLista(indicadorSelecionado, lista);
    }

    public List<Map<String, Object>> listaBIRequest(Integer idProfessor, Integer empresaId, IndicadorDashboardEnum indicadorSelecionado, PaginadorDTO paginadorDTO, String filter, HttpServletRequest request) throws ServiceException {
        List lista = pesquisaBI(idProfessor, empresaId, indicadorSelecionado, paginadorDTO, filter, request);
        return montarLista(indicadorSelecionado, lista);
    }

    private List pesquisaBI(Integer idProfessor, Integer empresaId, IndicadorDashboardEnum indicadorSelecionado, PaginadorDTO paginadorDTO, String filter, HttpServletRequest request) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        if (idProfessor == null){
            idProfessor = 0;
        }
        DashboardBI dash = obterDash(idProfessor, empresaId);
        List lista;
        try {
            FiltrosDashboard filtros = new FiltrosDashboard();
            filtros.setEmpresa(empresaId);
            ConfiguracaoSistema configuracaoSistema = cs.consultarPorTipo(ctx, ConfiguracoesEnum.PERIODO_USADO_BI);
            int diasPesquisa = Integer.parseInt(configuracaoSistema.getValor());
            filtros.setDiasParaFrente(diasPesquisa > 0 ? diasPesquisa : 30);
            filtros.setDiasParaTras(diasPesquisa > 0 ? diasPesquisa : 30);
            Date dataInicioPesquisa = new Date();
            Date dataFimPesquisa    = new Date();

            if(filter != null && filter.contains("inicio") && filter.contains("fim")){
                JSONObject objJson = new JSONObject();
                filter  = Uteis.retirarAcentuacaoRegex(filter);
                objJson = new JSONObject(filter);
                dataInicioPesquisa = new Date(objJson.optLong("inicio"));
                dataFimPesquisa    = Calendario.getDataComHora(new Date(objJson.optLong("fim")), "23:59");
            } else {
                dataInicioPesquisa = Uteis.somarDias(Calendario.hoje(), -(filtros.getDiasParaTras()));
            }

            switch (indicadorSelecionado) {
                case SEM_ACOMPANHAMENTO:
                    lista = clienteSinteticoService.consultarPorDesacompanhadosProfessor(ctx, empresaId, idProfessor, null, Calendario.hoje(), Calendario.hoje(), true, paginadorDTO, filter, request, null);
                    break;
                case EM_ACOMPANHAMENTO:
                    lista = clienteSinteticoService.consultarPorAcompanhadosProfessor(ctx, empresaId, idProfessor, null, Calendario.hoje(), Calendario.hoje(), true, paginadorDTO, filter, request, null);
                    break;
                case ALUNOS_A_VENCER:
                    lista = getService().consultarAlunos(ctx, dash, indicadorSelecionado, paginadorDTO, filter, idProfessor);
                    lista.sort(Comparator.comparing(o -> ((ClienteSintetico) o).getDataVigenciaAteAjustada()));
                    break;
                case TREINOS_A_VENCER:
                case VENCIDOS:
                    lista = getService().consultarAlunos(ctx, dash, indicadorSelecionado, paginadorDTO, filter, idProfessor);
                    break;
                case PROFESSORES:
                    lista = getService().obterProfessoresAgenda(ctx, dash, filtros, Calendario.hoje(), paginadorDTO, filter, dataInicioPesquisa, dataFimPesquisa);
                    break;
                case HRS_DISPONIBILIDADE:
                    lista = getService().obterAgendamentos(ctx, idProfessor, dash, dataInicioPesquisa, dataFimPesquisa, true, null, null, paginadorDTO, filter);
                    break;
                case HRS_ATENDIMENTO:
                    lista = getService().obterAgendamentos(ctx, idProfessor, dash, dataInicioPesquisa,  dataFimPesquisa, false, null, StatusAgendamentoEnum.EXECUTADO, null, filter);
                    break;
                case OCUPACAO:
                case AGENDAMENTOS:
                    lista = getService().obterAgendamentos(ctx, idProfessor, dash, dataInicioPesquisa,  dataFimPesquisa, false, null, null,paginadorDTO, filter);
                    break;
                case FALTARAM:
                    lista = getService().obterAgendamentos(ctx, idProfessor, dash, dataInicioPesquisa, dataFimPesquisa, false, null, StatusAgendamentoEnum.FALTOU,paginadorDTO, filter);
                    break;
                case COMPARECERAM:
                    lista = getService().obterAgendamentos(ctx, idProfessor, dash, dataInicioPesquisa,  dataFimPesquisa, false, null, StatusAgendamentoEnum.EXECUTADO,paginadorDTO, filter);
                    break;
                case CANCELARAM:
                    lista = getService().obterAgendamentos(ctx, idProfessor, dash, dataInicioPesquisa,  dataFimPesquisa, false, null, StatusAgendamentoEnum.CANCELADO,paginadorDTO, filter);
                    setarResponsaveisCancelamento(ctx, lista);
                    break;
                case AVALIACOES_FISICAS:
                    lista = getService().obterAgendamentos(ctx, idProfessor, dash, dataInicioPesquisa,  dataFimPesquisa, false, TipoAgendamentoEnum.AVALIACAO_FISICA, null,paginadorDTO, filter);
                    break;
                case NOVOS_TREINOS:
                    lista = getService().obterTreinosAgenda(ctx, idProfessor, dash, dataInicioPesquisa, dataFimPesquisa, false, paginadorDTO, filter);
                    break;
                case TREINOS_RENOVADOS:
                    lista = getService().obterTreinosAgenda(ctx, idProfessor, dash, dataInicioPesquisa, dataFimPesquisa, true, paginadorDTO, filter);
                    break;
                case TREINOS_REVISADOS:
                    lista = getService().obterRevisoes(ctx, idProfessor, dash, dataInicioPesquisa, dataFimPesquisa, paginadorDTO, filter);
                    break;
                case AVALIACOES:
                case ESTRELAS_1:
                case ESTRELAS_2:
                case ESTRELAS_3:
                case ESTRELAS_4:
                case ESTRELAS_5:
                    lista = getService().obterAvaliacoesTreino(ctx, idProfessor, dash, dataInicioPesquisa, dataFimPesquisa, paginadorDTO, filter, empresaId, indicadorSelecionado);
                    break;
                default:
                    // Ordenacao transfereida para consulta sql ate refatoracao de componente
                    lista = getService().consultarAlunos(ctx, dash, indicadorSelecionado, paginadorDTO, filter, idProfessor);
                    break;
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
        return lista;
    }

    private void setarResponsaveisCancelamento(String chave, List<Agendamento> lista) {
        for (Agendamento agendamento : lista) {
            if(agendamento.getCliente() != null){
                agendamento.setClienteUltAlteracao(new Usuario());
                agendamento.getClienteUltAlteracao().setCliente(agendamento.getCliente());
                agendamento.getClienteUltAlteracao().setNome(agendamento.getCliente().getNome());
            }
            try {
                String sql = "select dataalteracao from log l where chaveprimaria = '" + agendamento.getCodigo() +
                        "' and nomeentidade = 'AGENDADESERVICO'\n" +
                        "and (operacao = 'ALTERAÇÃO' or operacao = 'CANCELAMENTO')\n" +
                        "order by codigo desc\n" +
                        "limit 1 ";
                try (ResultSet rs = configuracaoSistemaDao.createStatement(chave, sql)) {
                    if (rs.next()) {
                        agendamento.setUltimaAlteracao(rs.getTimestamp("dataalteracao"));
                    }
                }
            }catch (Exception e){
                Uteis.logar(e, BITreinoServiceImpl.class);
            }
        }
    }

    public List<LinkedHashMap<String, Object>> listaBILinkedMap(Integer idProfessor, Integer empresaId, IndicadorDashboardEnum indicadorSelecionado, PaginadorDTO paginadorDTO, String filter) throws ServiceException, JSONException {
        List lista = pesquisaBI(idProfessor, empresaId, indicadorSelecionado, paginadorDTO, filter, null);
        return montarListaLinkedMap(indicadorSelecionado, lista);
    }

    public List<Map<String, Object>> listaDisponibilidades(Integer empresaId,
                                                           Integer idProfessor,
                                                           IndicadorDashboardEnum indicadorSelecionado,
                                                           StatusAgendamentoEnum statusAg,
                                                           TipoAgendamentoEnum tipoAg,
                                                           String filter) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            FiltrosDashboard filtros = new FiltrosDashboard();
            filtros.setEmpresa(empresaId);
            filtros.setDiasParaFrente(30);
            filtros.setDiasParaTras(30);
            Date dataInicioPesquisa = Uteis.somarDias(Calendario.hoje(), -(filtros.getDiasParaTras()));
            DashboardBI dash = obterDash(idProfessor, empresaId);
            List lista = getService().obterAgendamentos(ctx, idProfessor, dash, dataInicioPesquisa,  Calendario.hoje(), statusAg == null,
                    tipoAg, statusAg,null, filter);
            return montarLista(indicadorSelecionado, lista);

        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    private String getProperty(String columnKey) {
        if (columnKey.contains("<")) {
            return columnKey.substring(0, columnKey.indexOf("<")).replaceFirst("LINK", "");
        } else {
            return columnKey.substring(0, columnKey.indexOf(">")).replaceFirst("LINK", "");
        }
    }

    /**
     * Foi feito dessa forma, para que a filtragem dos professores no BI, funcionar tanto no Angular como no JSF
     * @param idProfessor
     * @return
     * @throws ServiceException
     */
    @Override
    public Integer codigoProfessor(Integer idProfessor, Integer empresa, Integer pessoa) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ProfessorSintetico ps;
            Integer result = 0;
            if (SuperControle.independente(ctx)) {
                result = idProfessor;
            } else {
                if (UteisValidacao.emptyNumber(pessoa) || UteisValidacao.emptyNumber(empresa)) {
                    ps = professorSinteticoService.consultarPorCodigoColaborador(ctx, idProfessor);
                    if (ps != null) {
                        result = ps.getCodigo();
                    }
                } else {
                    ps = professorSinteticoService.obterPorCodigoPessoaZW(ctx, pessoa, empresa);
                    if (ps == null) {
                        Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
                        Integer codProfessor = usuarioService.consultarCodProfessorPorCodigoExterno(ctx, usuario.getCodigoExterno(), empresa);
                        if (UteisValidacao.emptyNumber(codProfessor)) {
                            result = usuario.getProfessor().getCodigo();
                        } else {
                            result = codProfessor;
                        }
                    } else {
                        result = ps.getCodigo();
                    }
                }
            }
            return result;
        } catch (Exception ex) {
            throw new ServiceException(ProfessorExcecoes.ERRO_BUSCAR_PROFESSOR, ex);
        }
    }

    @Override
    public void importarAlunosForaTreino(Integer idProfessor, Integer empresaId) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        boolean independente = SuperControle.independente(ctx);
        if(!"true".equalsIgnoreCase(Aplicacao.getProp(ctx, Aplicacao.importandoClientesZw+empresaId))) {
            if (UteisValidacao.emptyNumber(idProfessor) && !independente) {
                getService().importarAlunosForaTreino(ctx, empresaId);
            }
        }
    }

}
