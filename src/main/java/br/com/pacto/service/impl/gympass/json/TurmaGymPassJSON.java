/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.gympass.json;

import br.com.pacto.bean.aula.Aula;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.Calendario;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 30/03/2020
 */
public class TurmaGymPassJSON extends SuperJSON {

    private Integer codigo;
    private String nome;
    private String descricao;
    private String observacao;
    private boolean ativo;
    private Integer empresaZW;
    private Integer empresaTR;
    private Integer produtoGymPass;
    private Integer idClasseGymPass;
    private String urlTurmaVirtual;
    private List<HorarioTurmaGymPassJSON> horarios;

    public TurmaGymPassJSON() {
    }

    public TurmaGymPassJSON(ResultSet rs) throws Exception {
        this.codigo = rs.getInt("codigo");
        this.nome = rs.getString("identificador");
        this.descricao = rs.getString("identificador");
        this.observacao = "";
        this.empresaZW = rs.getInt("empresa");
        this.produtoGymPass = rs.getInt("produtogympass");
        this.idClasseGymPass =  rs.getInt("idclassegympass");
        this.urlTurmaVirtual = rs.getString("urlturmavirtual");
        Date fimVigencia = rs.getDate("datafinalvigencia");
        this.ativo = Calendario.maiorOuIgual(fimVigencia, Calendario.hoje());
        this.horarios = new ArrayList<>();
    }

    public TurmaGymPassJSON(Aula aula, Integer empresaTR) {
        this.codigo = aula.getCodigo();
        this.nome = aula.getNome();
        this.descricao = aula.getNome();
        this.observacao = "";
        this.empresaTR = empresaTR;
        this.produtoGymPass = aula.getProdutoGymPass();
        this.idClasseGymPass = aula.getIdClasseGymPass();
        this.urlTurmaVirtual = aula.getUrlTurmaVirtual();
        this.ativo = Calendario.igual(Calendario.hoje(), aula.getDataInicio()) ||
                Calendario.igual(Calendario.hoje(), aula.getDataFim()) ||
                Calendario.entre(Calendario.hoje(), aula.getDataInicio(), aula.getDataFim());
        this.horarios = new ArrayList<>();
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Integer getProdutoGymPass() {
        return produtoGymPass;
    }

    public void setProdutoGymPass(Integer produtoGymPass) {
        this.produtoGymPass = produtoGymPass;
    }

    public Integer getIdClasseGymPass() {
        return idClasseGymPass;
    }

    public void setIdClasseGymPass(Integer idClasseGymPass) {
        this.idClasseGymPass = idClasseGymPass;
    }

    public List<HorarioTurmaGymPassJSON> getHorarios() {
        if (horarios == null) {
            horarios = new ArrayList<>();
        }
        return horarios;
    }

    public void setHorarios(List<HorarioTurmaGymPassJSON> horarios) {
        this.horarios = horarios;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getUrlTurmaVirtual() {
        return urlTurmaVirtual;
    }

    public void setUrlTurmaVirtual(String urlTurmaVirtual) {
        this.urlTurmaVirtual = urlTurmaVirtual;
    }

    public Integer getEmpresaZW() {
        return empresaZW;
    }

    public void setEmpresaZW(Integer empresaZW) {
        this.empresaZW = empresaZW;
    }

    public Integer getEmpresaTR() {
        return empresaTR;
    }

    public void setEmpresaTR(Integer empresaTR) {
        this.empresaTR = empresaTR;
    }
}
